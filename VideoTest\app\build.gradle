plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace 'com.android.rockchip.mediacodecnew'
    compileSdk 35

    defaultConfig {
        applicationId "com.android.rockchip.mediacodecnew"
        minSdk 31
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    signingConfigs {
        platform {
            //签名文件的路径
            storeFile file('signApk/platform.keystore')
            storePassword 'android'
            keyAlias = 'platform'
            keyPassword 'android'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.platform
        }
    }
}

dependencies {
    implementation libs.kotlin.stdlib
    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation 'com.github.bumptech.glide:glide:4.15.1'
    implementation 'androidx.recyclerview:recyclerview:1.3.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.15.1'
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
//    implementation files('libs/TP2HD-VisionSDK.aar') // 引用生成的 AAR 文件
//    implementation files('libs/android.jar') // 引用生成的 AAR 文件
//    compileOnly files('libs/classes.jar') // 引用生成的 AAR 文件
    //noinspection GradleDependency
    implementation("com.github.pedroSG94.RootEncoder:library:2.3.5")
    //noinspection GradleDependency
    implementation("com.github.pedroSG94:RTSP-Server:1.2.1")

    // JCIFS库，用于Samba文件传输
    implementation 'eu.agno3.jcifs:jcifs-ng:2.1.9'

    // TIFF格式支持库
    implementation 'io.github.beyka:Android-TiffBitmapFactory:0.9.9.1'
}

