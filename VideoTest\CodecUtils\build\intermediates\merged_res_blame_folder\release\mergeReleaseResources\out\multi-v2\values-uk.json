{"logs": [{"outputFile": "com.android.rockchip.video.CodecUtils-release-35:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a62aa56eb7c2c5ac23be6fb0044947f3\\transformed\\core-1.9.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "8747", "endColumns": "100", "endOffsets": "8843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4fc5f63fc0925efc93853f0f2ba4b8d\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1133,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,8665", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1128,1207,1298,1391,1486,1580,1680,1773,1868,1963,2054,2145,2244,2350,2456,2554,2661,2768,2873,3043,3143,8742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\676e6cf5fb8bbd8d94b07d695c20fbc3\\transformed\\material-1.10.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1179,1270,1336,1399,1487,1549,1616,1674,1745,1804,1858,1972,2032,2095,2149,2222,2341,2427,2510,2649,2734,2821,2954,3042,3120,3177,3228,3294,3366,3442,3532,3615,3688,3765,3846,3920,4029,4119,4198,4289,4385,4459,4540,4635,4689,4771,4837,4924,5010,5072,5136,5199,5272,5379,5489,5587,5693,5754,5809", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1174,1265,1331,1394,1482,1544,1611,1669,1740,1799,1853,1967,2027,2090,2144,2217,2336,2422,2505,2644,2729,2816,2949,3037,3115,3172,3223,3289,3361,3437,3527,3610,3683,3760,3841,3915,4024,4114,4193,4284,4380,4454,4535,4630,4684,4766,4832,4919,5005,5067,5131,5194,5267,5374,5484,5582,5688,5749,5804,5886"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3392,3500,3591,3687,3803,3886,3953,4044,4110,4173,4261,4323,4390,4448,4519,4578,4632,4746,4806,4869,4923,4996,5115,5201,5284,5423,5508,5595,5728,5816,5894,5951,6002,6068,6140,6216,6306,6389,6462,6539,6620,6694,6803,6893,6972,7063,7159,7233,7314,7409,7463,7545,7611,7698,7784,7846,7910,7973,8046,8153,8263,8361,8467,8528,8583", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "419,3221,3299,3387,3495,3586,3682,3798,3881,3948,4039,4105,4168,4256,4318,4385,4443,4514,4573,4627,4741,4801,4864,4918,4991,5110,5196,5279,5418,5503,5590,5723,5811,5889,5946,5997,6063,6135,6211,6301,6384,6457,6534,6615,6689,6798,6888,6967,7058,7154,7228,7309,7404,7458,7540,7606,7693,7779,7841,7905,7968,8041,8148,8258,8356,8462,8523,8578,8660"}}]}, {"outputFile": "com.android.rockchip.video.CodecUtils-mergeReleaseResources-33:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a62aa56eb7c2c5ac23be6fb0044947f3\\transformed\\core-1.9.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "8747", "endColumns": "100", "endOffsets": "8843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4fc5f63fc0925efc93853f0f2ba4b8d\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1133,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,8665", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1128,1207,1298,1391,1486,1580,1680,1773,1868,1963,2054,2145,2244,2350,2456,2554,2661,2768,2873,3043,3143,8742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\676e6cf5fb8bbd8d94b07d695c20fbc3\\transformed\\material-1.10.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1179,1270,1336,1399,1487,1549,1616,1674,1745,1804,1858,1972,2032,2095,2149,2222,2341,2427,2510,2649,2734,2821,2954,3042,3120,3177,3228,3294,3366,3442,3532,3615,3688,3765,3846,3920,4029,4119,4198,4289,4385,4459,4540,4635,4689,4771,4837,4924,5010,5072,5136,5199,5272,5379,5489,5587,5693,5754,5809", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1174,1265,1331,1394,1482,1544,1611,1669,1740,1799,1853,1967,2027,2090,2144,2217,2336,2422,2505,2644,2729,2816,2949,3037,3115,3172,3223,3289,3361,3437,3527,3610,3683,3760,3841,3915,4024,4114,4193,4284,4380,4454,4535,4630,4684,4766,4832,4919,5005,5067,5131,5194,5267,5374,5484,5582,5688,5749,5804,5886"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3392,3500,3591,3687,3803,3886,3953,4044,4110,4173,4261,4323,4390,4448,4519,4578,4632,4746,4806,4869,4923,4996,5115,5201,5284,5423,5508,5595,5728,5816,5894,5951,6002,6068,6140,6216,6306,6389,6462,6539,6620,6694,6803,6893,6972,7063,7159,7233,7314,7409,7463,7545,7611,7698,7784,7846,7910,7973,8046,8153,8263,8361,8467,8528,8583", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,132,87,77,56,50,65,71,75,89,82,72,76,80,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81", "endOffsets": "419,3221,3299,3387,3495,3586,3682,3798,3881,3948,4039,4105,4168,4256,4318,4385,4443,4514,4573,4627,4741,4801,4864,4918,4991,5110,5196,5279,5418,5503,5590,5723,5811,5889,5946,5997,6063,6135,6211,6301,6384,6457,6534,6615,6689,6798,6888,6967,7058,7154,7228,7309,7404,7458,7540,7606,7693,7779,7841,7905,7968,8041,8148,8258,8356,8462,8523,8578,8660"}}]}]}