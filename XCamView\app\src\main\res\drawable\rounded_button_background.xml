<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="12dp" />
            <solid android:color="#4D4D4D" />
            <stroke
                android:width="2dp"
                android:color="#FFFFFF" />
        </shape>
    </item>
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="12dp" />
            <solid android:color="#3D3D3D" />
            <stroke
                android:width="1dp"
                android:color="#AAAAAA" />
        </shape>
    </item>
</selector>