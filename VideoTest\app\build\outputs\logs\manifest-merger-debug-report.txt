-- Merging decision tree log ---
manifest
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\4ce9c579fed3c5f4d791917b3e996493\transformed\TP2HD-VisionSDK\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\737b03f0fc1ad0fd308d8e116bf71918\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a01b9d546a859681e827bccd129c316e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f152fa6167fb0d6d760b96586848d6bd\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5ee0973f6a00ce88be66ccc82fc087bc\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\f576180423cc1bd116688e674b755feb\transformed\glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\04247bd69f8b61876207c6561d26ab49\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\290be66140f305b67b4be447093c2d4a\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\401347183849308ee05797ac0014afaa\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f607d66e8c448d6cd81de1bc1cc204e\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\31dcaef73b1014d12efac9ffe902352c\transformed\library-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5b3346267b1dc162d49a651923ad0cc\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d98fed36487a98fedda955635d4172\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\04dbf0cfeda2c4c655b9e06b5f383afd\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d45dda4cfda41c1585c003f486528bd\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\fce2bfeb0cf2473be714d4e561d40659\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a6dfb2eb9e3e07376275479148eeb61\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61b17053d7e307bc724e10021fe7d10f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d82214c72ae4098b39f6d7a4a391b518\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\842b58170730cb8b1a391f63e33e6e12\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\3aa29553d9b17e04ba347f6b00ef6b33\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\076e8345b2ee410e061cbe8ca5302393\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2935d3d8ed647f0d4520d764f14b2e4a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0592eb67f7bf8f00d60492705d5dc67c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55a9a4376cef5f58a80c7146149df2cc\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d740bff666d881d5062def413615c5a2\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e3f1b5f79ef0b28c9753be4198cd8851\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6da6e05957e9bbd65f8ffa024d47472b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\36b95a2639159ba79a3963faad794626\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\948a5c79523b8a0e44ac48291ca7fc8e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4433dc59190f84015f99a5dadc972527\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\c5fe518da69a560dd1ee483b311cf518\transformed\encoder-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\5d617e9afcf2be6c1bc44d15a1888912\transformed\rtmp-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\bbbddcf0f4a55ab01b543a7cd49a3dd1\transformed\rtsp-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\83004392f0361adce6f990711d5ac684\transformed\srt-2.3.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\017eb6f15475f77eb4172dc1e76f4a29\transformed\common-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\826d2467031818311358a8e4ffef4793\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\892eb02f2e13402f23a2b167869c4d00\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6485ad1bc619db763f0d11c67602a7ec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02be8f81d8d9b9e987e41b103ea7244b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1f5f5a1424f919603725142311459592\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\ec3bda9fbabb9fa43b7799bdce583ca0\transformed\gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\9b0473a6d87fb2b75aa4cc2052aafd13\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\303095b6ed09231f4c5510324332dbe9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\30e56c6c7e1e79d2055de4244226e442\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f62fb140c3ebe653e59cc445549cd21\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6807519cf2b6f1f2856a2f2f632d3adf\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b71494879e7c8f9656a97d53b3c301fa\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a858b662946256d1706b5446d2694df\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\de2ced79d3b261cba8d6fc72a8b9a95c\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:2:1-14:12
	package
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:3:5-46
	android:versionName
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:4:5-51
	android:versionCode
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:11:5-71
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:11:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:14:5-79
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.INTERNET
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:17:5-79
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:17:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:25:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:25:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:26:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:26:22-73
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:27:5-78
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:27:22-76
uses-permission#android.permission.TETHER_PRIVILEGED
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:28:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:28:22-73
uses-permission#android.permission.WRITE_SETTINGS
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:29:5-73
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:29:22-70
uses-permission#android.permission.MANAGE_WIFI_HOTSPOT
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:30:5-77
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:30:22-75
uses-permission#android.permission.CONNECTIVITY_INTERNAL
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:5-115
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:78-113
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:22-77
uses-permission#android.permission.START_TETHERING
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:5-109
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:72-107
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:22-71
uses-permission#com.android.providers.tv.permission.READ_EPG_DATA
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:35:5-89
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:35:22-86
uses-feature#android.software.live_tv
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:5-86
	android:required
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:59-83
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:19-58
uses-permission#android.permission.NEARBY_WIFI_DEVICES
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:41:5-78
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:41:22-75
uses-feature#android.hardware.camera
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:44:5-60
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:44:19-57
application
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:45:5-86:19
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:45:5-86:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\737b03f0fc1ad0fd308d8e116bf71918\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\737b03f0fc1ad0fd308d8e116bf71918\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a01b9d546a859681e827bccd129c316e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a01b9d546a859681e827bccd129c316e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\f576180423cc1bd116688e674b755feb\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\f576180423cc1bd116688e674b755feb\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02be8f81d8d9b9e987e41b103ea7244b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02be8f81d8d9b9e987e41b103ea7244b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\ec3bda9fbabb9fa43b7799bdce583ca0\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\ec3bda9fbabb9fa43b7799bdce583ca0\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\30e56c6c7e1e79d2055de4244226e442\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\30e56c6c7e1e79d2055de4244226e442\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\de2ced79d3b261cba8d6fc72a8b9a95c\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:9:5-12:19
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\de2ced79d3b261cba8d6fc72a8b9a95c\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:9:5-12:19
	android:extractNativeLibs
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:53:9-35
	android:label
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:51:9-41
	android:fullBackupContent
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:48:9-54
	android:roundIcon
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:52:9-54
	tools:targetApi
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:55:9-29
	android:icon
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:50:9-43
	android:allowBackup
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:46:9-35
		REJECTED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\de2ced79d3b261cba8d6fc72a8b9a95c\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:10:9-36
	android:theme
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:54:9-51
	android:dataExtractionRules
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:47:9-65
	tools:replace
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:49:9-44
activity#com.android.rockchip.camera2.integrated.MainActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:57:9-65:20
	android:exported
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:59:13-36
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:58:13-80
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:60:13-64:29
action#android.intent.action.MAIN
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:61:17-69
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:61:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:63:17-77
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:63:27-74
activity#com.android.rockchip.camera2.separated.VideoDecoderActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:67:9-68:48
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:68:13-45
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:67:19-93
activity#com.android.rockchip.camera2.separated.MediaBrowserActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:70:9-96
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:70:19-93
activity#com.android.rockchip.camera2.separated.ImageViewerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:72:9-95
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:72:19-92
activity#com.android.rockchip.camera2.integrated.browser.MediaBrowserActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:75:9-105
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:75:19-102
activity#com.android.rockchip.camera2.integrated.browser.ImageViewerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:76:9-104
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:76:19-101
activity#com.android.rockchip.camera2.integrated.browser.TpVideoPlayerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:77:9-106
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:77:19-103
activity#com.android.rockchip.camera2.separated.TpVideoPlayerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:78:9-97
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:78:19-94
service#com.touptek.video.internal.rtsp.service.RTSPService
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:80:9-85:44
	android:enabled
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:82:13-35
	android:exported
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:83:13-37
	android:stopWithTask
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:85:13-41
	android:foregroundServiceType
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:84:13-60
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:81:13-79
uses-sdk
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\4ce9c579fed3c5f4d791917b3e996493\transformed\TP2HD-VisionSDK\AndroidManifest.xml:5:5-44
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\4ce9c579fed3c5f4d791917b3e996493\transformed\TP2HD-VisionSDK\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\737b03f0fc1ad0fd308d8e116bf71918\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\737b03f0fc1ad0fd308d8e116bf71918\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a01b9d546a859681e827bccd129c316e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a01b9d546a859681e827bccd129c316e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f152fa6167fb0d6d760b96586848d6bd\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\f152fa6167fb0d6d760b96586848d6bd\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5ee0973f6a00ce88be66ccc82fc087bc\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\5ee0973f6a00ce88be66ccc82fc087bc\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\f576180423cc1bd116688e674b755feb\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\f576180423cc1bd116688e674b755feb\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\04247bd69f8b61876207c6561d26ab49\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\04247bd69f8b61876207c6561d26ab49\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\290be66140f305b67b4be447093c2d4a\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\290be66140f305b67b4be447093c2d4a\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\401347183849308ee05797ac0014afaa\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\401347183849308ee05797ac0014afaa\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f607d66e8c448d6cd81de1bc1cc204e\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4f607d66e8c448d6cd81de1bc1cc204e\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\31dcaef73b1014d12efac9ffe902352c\transformed\library-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\31dcaef73b1014d12efac9ffe902352c\transformed\library-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5b3346267b1dc162d49a651923ad0cc\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\a5b3346267b1dc162d49a651923ad0cc\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d98fed36487a98fedda955635d4172\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\44d98fed36487a98fedda955635d4172\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\04dbf0cfeda2c4c655b9e06b5f383afd\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\04dbf0cfeda2c4c655b9e06b5f383afd\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d45dda4cfda41c1585c003f486528bd\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d45dda4cfda41c1585c003f486528bd\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\fce2bfeb0cf2473be714d4e561d40659\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\fce2bfeb0cf2473be714d4e561d40659\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a6dfb2eb9e3e07376275479148eeb61\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a6dfb2eb9e3e07376275479148eeb61\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61b17053d7e307bc724e10021fe7d10f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\61b17053d7e307bc724e10021fe7d10f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d82214c72ae4098b39f6d7a4a391b518\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d82214c72ae4098b39f6d7a4a391b518\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\842b58170730cb8b1a391f63e33e6e12\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\842b58170730cb8b1a391f63e33e6e12\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\3aa29553d9b17e04ba347f6b00ef6b33\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\3aa29553d9b17e04ba347f6b00ef6b33\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\076e8345b2ee410e061cbe8ca5302393\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\076e8345b2ee410e061cbe8ca5302393\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2935d3d8ed647f0d4520d764f14b2e4a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2935d3d8ed647f0d4520d764f14b2e4a\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0592eb67f7bf8f00d60492705d5dc67c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0592eb67f7bf8f00d60492705d5dc67c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55a9a4376cef5f58a80c7146149df2cc\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\55a9a4376cef5f58a80c7146149df2cc\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d740bff666d881d5062def413615c5a2\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d740bff666d881d5062def413615c5a2\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e3f1b5f79ef0b28c9753be4198cd8851\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e3f1b5f79ef0b28c9753be4198cd8851\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6da6e05957e9bbd65f8ffa024d47472b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6da6e05957e9bbd65f8ffa024d47472b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\36b95a2639159ba79a3963faad794626\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\36b95a2639159ba79a3963faad794626\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\948a5c79523b8a0e44ac48291ca7fc8e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\948a5c79523b8a0e44ac48291ca7fc8e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4433dc59190f84015f99a5dadc972527\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4433dc59190f84015f99a5dadc972527\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\c5fe518da69a560dd1ee483b311cf518\transformed\encoder-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\c5fe518da69a560dd1ee483b311cf518\transformed\encoder-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\5d617e9afcf2be6c1bc44d15a1888912\transformed\rtmp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\5d617e9afcf2be6c1bc44d15a1888912\transformed\rtmp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\bbbddcf0f4a55ab01b543a7cd49a3dd1\transformed\rtsp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\bbbddcf0f4a55ab01b543a7cd49a3dd1\transformed\rtsp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\83004392f0361adce6f990711d5ac684\transformed\srt-2.3.5\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\83004392f0361adce6f990711d5ac684\transformed\srt-2.3.5\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\017eb6f15475f77eb4172dc1e76f4a29\transformed\common-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\017eb6f15475f77eb4172dc1e76f4a29\transformed\common-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\826d2467031818311358a8e4ffef4793\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\826d2467031818311358a8e4ffef4793\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\892eb02f2e13402f23a2b167869c4d00\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\892eb02f2e13402f23a2b167869c4d00\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6485ad1bc619db763f0d11c67602a7ec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6485ad1bc619db763f0d11c67602a7ec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02be8f81d8d9b9e987e41b103ea7244b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02be8f81d8d9b9e987e41b103ea7244b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1f5f5a1424f919603725142311459592\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1f5f5a1424f919603725142311459592\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\ec3bda9fbabb9fa43b7799bdce583ca0\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\ec3bda9fbabb9fa43b7799bdce583ca0\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\9b0473a6d87fb2b75aa4cc2052aafd13\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\9b0473a6d87fb2b75aa4cc2052aafd13\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\303095b6ed09231f4c5510324332dbe9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\303095b6ed09231f4c5510324332dbe9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\30e56c6c7e1e79d2055de4244226e442\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\30e56c6c7e1e79d2055de4244226e442\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f62fb140c3ebe653e59cc445549cd21\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\9f62fb140c3ebe653e59cc445549cd21\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6807519cf2b6f1f2856a2f2f632d3adf\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6807519cf2b6f1f2856a2f2f632d3adf\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b71494879e7c8f9656a97d53b3c301fa\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b71494879e7c8f9656a97d53b3c301fa\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a858b662946256d1706b5446d2694df\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a858b662946256d1706b5446d2694df\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\de2ced79d3b261cba8d6fc72a8b9a95c\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\de2ced79d3b261cba8d6fc72a8b9a95c\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02be8f81d8d9b9e987e41b103ea7244b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02be8f81d8d9b9e987e41b103ea7244b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\96ba28491bd5a07220831f76481e9634\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f7eb31f6c92a76ef24e2af4a1b500c0a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c629d2bab069b69ff70afa7604a76b8d\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\bb2bf34004b8a763e80071b6dc704e7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
