<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2021 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Fills the entire area with the container's color first... -->
  <item>
    <shape
        android:shape="rectangle">
      <solid android:color="@macro/m3_comp_primary_navigation_tab_container_color"/>
    </shape>
  </item>
  <!-- ..., then draws a rectangle for the divider at the bottom. -->
  <item
      android:gravity="bottom">
    <shape>
      <size android:height="1dp" />
      <!-- TODO(b/272585197) Manually map this based on the token mapping of divider. -->
      <solid android:color="?attr/colorSurfaceVariant" />
    </shape>
  </item>
</layer-list>
