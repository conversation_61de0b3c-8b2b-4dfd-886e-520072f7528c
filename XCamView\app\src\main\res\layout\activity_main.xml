<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:foreground="@android:color/transparent"
    android:keepScreenOn="true"
    android:clickable="true">

    <TextView
        android:id="@+id/main_center_info_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:maxWidth="600dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:background="@drawable/status_banner_bg"
        android:textColor="#FFFFFF"
        android:textSize="30sp"
        android:textStyle="bold"
        android:shadowColor="#80000000"
        android:shadowDx="0"
        android:shadowDy="1"
        android:shadowRadius="2"
        android:visibility="gone"
        android:elevation="8dp"
        android:alpha="0"
        android:fontFamily="sans-serif-medium"/> <!-- 增加Z轴高度 放置覆盖-->

    <TextureView
        android:id="@+id/blue_texture_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:layout_centerInParent="true"/>

    <!-- ROI选择框叠加层 -->
    <com.touptek.ui.TpRoiView
        android:id="@+id/roi_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible" />

    <!-- 测量工具叠加层 -->
    <com.touptek.xcamview.view.MeasurementOverlayView
        android:id="@+id/measurement_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/tv_timer"
        android:layout_width="wrap_content"
        android:layout_height="70dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="16dp"
        android:textColor="#FF0000"
        android:textSize="36sp"
        android:visibility="gone"/>

    <TextureView
        android:id="@+id/tv_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:layout_centerInParent="true"/>


    <!-- 新增居中显示时长的TextView -->
    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="#FFFFFF"
        android:textSize="32sp"
        android:visibility="gone"/>

</RelativeLayout>
