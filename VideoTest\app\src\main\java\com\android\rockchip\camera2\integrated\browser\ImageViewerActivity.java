package com.android.rockchip.camera2.integrated.browser;

import android.os.Bundle;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.touptek.video.TpImageLoader;
import com.touptek.ui.TpImageView;
import com.android.rockchip.mediacodecnew.R;

/**
 * ImageViewerActivity - integrated版本的图片查看器
 * <p>
 * 此Activity用于显示选定的图片，并支持缩放和平移。
 * 使用TpImageLoader加载高质量图片，支持手势缩放和平移操作。
 * </p>
 */
public class ImageViewerActivity extends AppCompatActivity {
    private static final String TAG = "ImageViewerActivity";

    private TpImageView imageView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        /* 设置全屏显示 */
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);

        setContentView(R.layout.image_viewer);

        /* 获取 TpImageView */
        imageView = findViewById(R.id.image_view);

        /* 获取图片路径 */
        String imagePath = getIntent().getStringExtra("imagePath");

        if (imagePath == null || imagePath.isEmpty()) {
            Toast.makeText(this, "图片路径无效", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        /* 配置缩放功能 */
        configureZoomableImageView();

        /* 使用 TpImageLoader 加载高质量图片 */
        TpImageLoader.loadFullImage(imagePath, imageView);



        Toast.makeText(this, "支持双指缩放、拖拽和双击缩放", Toast.LENGTH_SHORT).show();
    }

    /**
     * 配置TpZoomableImageView
     */
    private void configureZoomableImageView() {
        // 启用缩放功能（平移和双击功能默认已启用）
        imageView.setZoomEnabled(true);

        // 设置Matrix变化监听器（替代原来的缩放变化监听器）
        imageView.setMatrixChangeListener(() -> {
            float currentScale = imageView.getCurrentScale();
            android.util.Log.d(TAG, "缩放比例变化: " + currentScale);
            // 可以在这里添加缩放变化的处理逻辑
        });

        android.util.Log.d(TAG, "TpImageView配置完成");
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        // 可以在这里添加退出动画或其他处理
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理资源
        if (imageView != null) {
            imageView.setImageDrawable(null);
            imageView.setMatrixChangeListener(null);
        }
    }
}
