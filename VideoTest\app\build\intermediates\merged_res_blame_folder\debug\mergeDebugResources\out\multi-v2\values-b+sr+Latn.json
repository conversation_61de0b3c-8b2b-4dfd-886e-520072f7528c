{"logs": [{"outputFile": "com.android.rockchip.mediacodecnew.app-mergeDebugResources-36:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5ee0973f6a00ce88be66ccc82fc087bc\\transformed\\appcompat-1.7.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,481,582,688,774,878,1000,1085,1167,1258,1351,1446,1540,1640,1733,1828,1933,2024,2115,2201,2306,2412,2515,2622,2731,2838,3008,9640", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "476,577,683,769,873,995,1080,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2617,2726,2833,3003,3100,9722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\737b03f0fc1ad0fd308d8e116bf71918\\transformed\\material-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1130,1196,1289,1357,1420,1523,1583,1649,1705,1776,1836,1890,2002,2059,2120,2174,2250,2375,2462,2539,2632,2716,2799,2938,3020,3103,3234,3322,3400,3454,3510,3576,3650,3728,3799,3881,3957,4033,4108,4180,4287,4377,4450,4542,4638,4710,4786,4882,4935,5017,5084,5171,5258,5320,5384,5447,5516,5621,5731,5827,5935,5993,6053,6133,6216,6292", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "319,396,473,553,661,755,849,981,1062,1125,1191,1284,1352,1415,1518,1578,1644,1700,1771,1831,1885,1997,2054,2115,2169,2245,2370,2457,2534,2627,2711,2794,2933,3015,3098,3229,3317,3395,3449,3505,3571,3645,3723,3794,3876,3952,4028,4103,4175,4282,4372,4445,4537,4633,4705,4781,4877,4930,5012,5079,5166,5253,5315,5379,5442,5511,5616,5726,5822,5930,5988,6048,6128,6211,6287,6364"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3105,3182,3259,3339,3447,4267,4361,4493,4574,4637,4703,4796,4864,4927,5030,5090,5156,5212,5283,5343,5397,5509,5566,5627,5681,5757,5882,5969,6046,6139,6223,6306,6445,6527,6610,6741,6829,6907,6961,7017,7083,7157,7235,7306,7388,7464,7540,7615,7687,7794,7884,7957,8049,8145,8217,8293,8389,8442,8524,8591,8678,8765,8827,8891,8954,9023,9128,9238,9334,9442,9500,9560,9727,9810,9886", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,116", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "369,3177,3254,3334,3442,3536,4356,4488,4569,4632,4698,4791,4859,4922,5025,5085,5151,5207,5278,5338,5392,5504,5561,5622,5676,5752,5877,5964,6041,6134,6218,6301,6440,6522,6605,6736,6824,6902,6956,7012,7078,7152,7230,7301,7383,7459,7535,7610,7682,7789,7879,7952,8044,8140,8212,8288,8384,8437,8519,8586,8673,8760,8822,8886,8949,9018,9123,9233,9329,9437,9495,9555,9635,9805,9881,9958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c629d2bab069b69ff70afa7604a76b8d\\transformed\\core-1.13.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3541,3639,3741,3838,3942,4046,4151,9963", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3634,3736,3833,3937,4041,4146,4262,10059"}}]}]}