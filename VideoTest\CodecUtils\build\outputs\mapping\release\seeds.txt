com.android.rockchip.video.R
com.android.rockchip.camera2.video.VideoDecoder$VideoDecoderListener
com.android.rockchip.video.R$layout
com.android.rockchip.camera2.view.TpVideoPlayerView$2
com.android.rockchip.camera2.rtsp.RTSPManager$1
com.android.rockchip.camera2.util.NetworkManager
com.android.rockchip.camera2.rtsp.config.RTSPConfig
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener
com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener
com.android.rockchip.camera2.video.TpCaptureImage$1
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder
com.android.rockchip.video.R$attr
com.android.rockchip.camera2.util.TouptekIspParam$1
com.android.rockchip.camera2.video.VideoEncoder
com.android.rockchip.camera2.util.HdmiService$HdmiListener
com.android.rockchip.video.R$styleable
com.android.rockchip.camera2.rtsp.RTSPManager
com.android.rockchip.camera2.util.SMBFileUploader
com.android.rockchip.camera2.video.TpVideoConfig
com.android.rockchip.camera2.video.TpImageLoader
com.android.rockchip.camera2.rtsp.service.ProjectionData
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec
com.android.rockchip.camera2.video.VideoEncoder$VideoDataOutputCallback
com.android.rockchip.camera2.video.TpVideoSystem$StreamType
com.android.rockchip.camera2.video.TvPreviewHelper
com.android.rockchip.camera2.view.TpImageView$OnZoomChangeListener
com.android.rockchip.camera2.util.FileStorageUtils$StorageListener
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder
com.android.rockchip.camera2.view.TpVideoPlayerView
com.android.rockchip.camera2.video.TpCameraManager$Builder
com.android.rockchip.camera2.view.TpVideoPlayerView$1
com.android.rockchip.camera2.util.HdmiService
com.android.rockchip.video.R$color
com.android.rockchip.camera2.video.TpCaptureImage
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType
com.android.rockchip.camera2.video.VideoDecoder
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection
com.android.rockchip.camera2.video.TpCaptureImage$Builder$1
com.android.rockchip.camera2.util.SMBFileUploader$UploadListener
com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution
com.android.rockchip.camera2.util.TouptekIspParam$ParamData
com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener
com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo
com.android.rockchip.camera2.video.TpVideoSystem$2
com.android.rockchip.camera2.util.TransformUtils
com.android.rockchip.camera2.service.StreamingSocketService
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection$UrlCallback
com.android.rockchip.video.R$drawable
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode
com.android.rockchip.camera2.util.TouptekIspParam$OnSerialStateChangedListener
com.android.rockchip.camera2.service.StreamingService
com.android.rockchip.camera2.video.TpCameraManager
com.android.rockchip.camera2.rtsp.service.RTSPStreamer
com.android.rockchip.camera2.util.touptek_serial_rk
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder
com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener
com.android.rockchip.camera2.video.TpVideoSystem$1
com.android.rockchip.camera2.video.VideoEncoder$Builder
com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener
com.android.rockchip.camera2.view.TpCustomProgressBar
com.android.rockchip.camera2.service.StreamingService$StreamType
com.android.rockchip.camera2.view.TpTextureView$OnZoomChangeListener
com.android.rockchip.camera2.util.TouptekIspParam
com.android.rockchip.camera2.view.TpImageView
com.android.rockchip.camera2.view.TpRoiView
com.android.rockchip.camera2.service.StreamingSocketService$1
com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener
com.android.rockchip.camera2.rtsp.service.RTSPService
com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener
com.android.rockchip.camera2.video.TpVideoSystem$ListenerAction
com.android.rockchip.camera2.video.TpVideoConfig$Builder
com.android.rockchip.camera2.view.TpVideoPlayerView$6
com.android.rockchip.camera2.view.TpTextureView
com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler
com.android.rockchip.camera2.util.touptek_serial_rk$DeviceStateCallback
com.android.rockchip.camera2.service.StreamingSocketService$LogListener
com.android.rockchip.camera2.service.StreamingService$HeartbeatListener
com.android.rockchip.camera2.video.TpVideoSystem
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig
com.android.rockchip.camera2.rtsp.service.RTSPService$RTSPBinder
com.android.rockchip.camera2.util.FileStorageUtils
com.android.rockchip.camera2.video.TpVideoSystem$StreamingOperation
com.android.rockchip.camera2.video.TpCaptureImage$Builder
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemAdapter
com.android.rockchip.video.R$style
com.android.rockchip.camera2.util.NetworkManager$HotspotInfo
com.android.rockchip.video.R$id
com.android.rockchip.camera2.video.ImageDecoder
com.android.rockchip.video.R$style: int TpVideoTimeDisplay
com.android.rockchip.camera2.view.TpImageView: android.view.GestureDetector mGestureDetector
com.android.rockchip.camera2.view.TpRoiView: float sizeRatio
com.android.rockchip.camera2.view.TpCustomProgressBar: com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener mProgressChangeListener
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean mControlsVisible
com.android.rockchip.camera2.video.TpCameraManager: java.util.function.BiConsumer onCameraErrorHandler
com.android.rockchip.camera2.util.NetworkManager: androidx.activity.result.ActivityResultLauncher wifiPanelLauncher
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec[] $VALUES
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String password
com.android.rockchip.camera2.video.TpVideoConfig$Builder: int height
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: java.util.concurrent.ScheduledExecutorService scheduler
com.android.rockchip.camera2.video.TpVideoSystem: boolean isTvMode
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution RES_1280x720
com.android.rockchip.camera2.view.TpImageView$GestureState: com.android.rockchip.camera2.view.TpImageView$GestureState PANNING
com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo: java.lang.String ssid
com.android.rockchip.video.R$id: int btn_settings
com.android.rockchip.camera2.rtsp.RTSPManager: java.lang.String TAG
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.service.StreamingService$HeartbeatListener heartbeatListener
com.android.rockchip.camera2.rtsp.RTSPManager: java.util.function.Consumer onPermissionGrantedHandler
com.android.rockchip.camera2.view.TpImageView: float mLastTouchX
com.android.rockchip.camera2.view.TpRoiView: android.graphics.Paint borderPaint
com.android.rockchip.camera2.service.StreamingService: int retryCount
com.android.rockchip.camera2.util.SMBFileUploader: java.lang.String username
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int frameRate
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean mIsStatusSyncEnabled
com.android.rockchip.camera2.view.TpImageView: java.lang.Runnable mResetGestureStateRunnable
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_EXPOSURETIME
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode CBR
com.android.rockchip.camera2.video.ImageDecoder: java.io.File diskCacheDir
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec H264
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.atomic.AtomicBoolean earlyInitialized
com.android.rockchip.video.R$id: int btn_previous_video
com.android.rockchip.camera2.service.StreamingSocketService: int CMD_REQUEST_IMAGE
com.android.rockchip.camera2.util.HdmiService: boolean firstFlag
com.android.rockchip.camera2.video.VideoDecoder: boolean isPlaybackCompleted
com.android.rockchip.camera2.video.TpCaptureImage: android.media.ImageReader imageReader
com.android.rockchip.camera2.view.TpVideoPlayerView: int mPrimaryColor
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: android.content.Context context
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String currentUsbPath
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_DARKENHANCE
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_COLORTONE
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode CQ
com.android.rockchip.camera2.view.TpVideoPlayerView: java.lang.String TAG
com.android.rockchip.camera2.video.TpVideoConfig$Builder: int frameRate
com.android.rockchip.camera2.view.TpTextureView: boolean mZoomEnabled
com.android.rockchip.camera2.view.TpImageView: android.view.ScaleGestureDetector mScaleGestureDetector
com.android.rockchip.camera2.video.TpImageLoader: java.util.concurrent.ConcurrentHashMap activeRequests
com.android.rockchip.camera2.util.SMBFileUploader$TestConnectionTask: java.lang.String errorMessage
com.android.rockchip.video.R$color: int tp_video_button_bg_pressed
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.View mForwardButton
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.video.TpCaptureImage tpCaptureImage
com.android.rockchip.camera2.view.TpCustomProgressBar: int mMax
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_FLIP
com.android.rockchip.camera2.view.TpVideoPlayerView: float[] PLAYBACK_SPEEDS
com.android.rockchip.camera2.view.TpVideoPlayerView: com.android.rockchip.camera2.view.TpTextureView mTextureView
com.android.rockchip.camera2.video.TpCaptureImage$Builder: int maxImages
com.android.rockchip.camera2.util.SMBFileUploader$ListDirectoriesTask: com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener callback
com.android.rockchip.camera2.view.TpCustomProgressBar: float mThumbAlpha
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.video.VideoEncoder videoEncoder
com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo: boolean connected
com.android.rockchip.video.R$color: int tp_video_text_disabled
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: int width
com.android.rockchip.camera2.video.VideoDecoder: com.android.rockchip.camera2.video.VideoDecoder$VideoDecoderListener videoDecoderListener
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: int frameRate
com.android.rockchip.camera2.util.TouptekIspParam: android.content.SharedPreferences sharedPreferences
com.android.rockchip.camera2.video.TpVideoSystem$1: com.android.rockchip.camera2.video.TpVideoSystem this$0
com.android.rockchip.video.R$styleable: int[] TpVideoPlayerView
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: java.lang.String rtspUrl
com.android.rockchip.camera2.view.TpImageView: android.graphics.Matrix mMatrix
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType[] $VALUES
com.android.rockchip.camera2.view.TpImageView: float mLastTouchY
com.android.rockchip.camera2.view.TpVideoPlayerView: android.os.Handler mProgressHandler
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean mIsUpdatingProgress
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder: boolean recordMic
com.android.rockchip.camera2.service.StreamingService$StreamType: com.android.rockchip.camera2.service.StreamingService$StreamType CAMERA
com.android.rockchip.camera2.service.StreamingService: int SOCKET_PORT
com.android.rockchip.camera2.rtsp.RTSPManager$1: com.android.rockchip.camera2.rtsp.RTSPManager this$0
com.android.rockchip.video.R$color: int tp_video_button_stroke_disabled
com.android.rockchip.camera2.util.HdmiService$ReadThread: int stableConnectionCounter
com.android.rockchip.camera2.view.TpImageView: com.android.rockchip.camera2.view.TpImageView$ScaleInfo mScaleInfo
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean isStreaming
com.android.rockchip.camera2.service.StreamingService$StreamType: com.android.rockchip.camera2.service.StreamingService$StreamType SCREEN
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: java.lang.String TAG
com.android.rockchip.camera2.util.FileStorageUtils: android.content.BroadcastReceiver storageReceiver
com.android.rockchip.camera2.service.StreamingService: int ispPort
com.android.rockchip.camera2.rtsp.service.RTSPService: java.lang.String TAG
com.android.rockchip.camera2.view.TpImageView: android.os.Handler mHandler
com.android.rockchip.video.R$id: int btn_forward
com.android.rockchip.camera2.util.HdmiService: java.io.File mHdmiRxFile
com.android.rockchip.camera2.rtsp.service.ProjectionData: int resultCode
com.android.rockchip.camera2.rtsp.service.RTSPService: java.lang.String CHANNEL_ID
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle TOP_RIGHT
com.android.rockchip.camera2.view.TpTextureView: com.android.rockchip.camera2.view.TpTextureView$GestureState mGestureState
com.android.rockchip.camera2.view.TpRoiView: int cameraWidth
com.android.rockchip.camera2.service.StreamingSocketService: com.android.rockchip.camera2.video.TpCaptureImage captureHelper
com.android.rockchip.camera2.video.TpCameraManager: android.os.Handler backgroundHandler
com.android.rockchip.camera2.util.TouptekIspParam: java.lang.Boolean allParamsRangeReceived
com.android.rockchip.camera2.view.TpVideoPlayerView$2: com.android.rockchip.camera2.view.TpVideoPlayerView this$0
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener streamListener
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_EXPOSURECOMPENSATION
com.android.rockchip.camera2.view.TpRoiView: float DEFAULT_SIZE_RATIO
com.android.rockchip.camera2.view.TpVideoPlayerView: com.android.rockchip.camera2.video.TpVideoSystem mVideoSystem
com.android.rockchip.camera2.video.VideoEncoder: java.util.function.Consumer onSurfaceAvailableHandler
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: int MAX_RETRIES
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: int height
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.rtsp.RTSPManager rtspManager
com.android.rockchip.camera2.video.TpCaptureImage: java.util.concurrent.atomic.AtomicInteger currentProcessingCount
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder: android.media.projection.MediaProjection mediaProjection
com.android.rockchip.video.R$id: int speed_1_25
com.android.rockchip.camera2.view.TpRoiView: java.lang.String TAG
com.android.rockchip.video.R$drawable: int tp_video_progress_drawable
com.android.rockchip.camera2.video.TpImageLoader: java.lang.String[] VIDEO_EXTENSIONS
com.android.rockchip.camera2.view.TpCustomProgressBar: android.graphics.Paint mProgressPaint
com.android.rockchip.camera2.video.TvPreviewHelper: android.os.Handler handler
com.android.rockchip.camera2.video.TpCaptureImage: int FORMAT_PNG
com.android.rockchip.camera2.rtsp.RTSPManager: android.os.Handler mainHandler
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.service.ProjectionData projectionData
com.android.rockchip.camera2.video.TpVideoConfig: int width
com.android.rockchip.camera2.video.TpImageLoader: boolean cacheInitialized
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: boolean recordMic
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: int width
com.android.rockchip.camera2.service.StreamingService: int CMD_ISP_PARAM_CONFIG
com.android.rockchip.video.R$id: int btn_next_video
com.android.rockchip.camera2.video.VideoEncoder: java.lang.String outputPath
com.android.rockchip.camera2.video.VideoEncoder: java.util.function.Consumer onSaveCompleteHandler
com.android.rockchip.video.R$id: int tv_current_time
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_WDREXPRATIO
com.android.rockchip.camera2.service.StreamingSocketService: java.lang.String TAG
com.android.rockchip.camera2.video.TvPreviewHelper: long START_TV_REVIEW_DELAY
com.android.rockchip.camera2.util.TouptekIspParam$ParamData: int current
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle[] $VALUES
com.android.rockchip.camera2.service.StreamingSocketService: int THUMBNAIL_HEIGHT
com.android.rockchip.camera2.rtsp.RTSPManager: java.lang.Runnable onStreamStoppedHandler
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.video.VideoEncoder videoEncoder
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int videoBitrate
com.android.rockchip.camera2.view.TpCustomProgressBar: float mThumbRadius
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: int bitrate
com.android.rockchip.video.R$id: int speed_1_5
com.android.rockchip.video.R$color: int tp_video_progress_primary
com.android.rockchip.camera2.util.FileStorageUtils: com.android.rockchip.camera2.util.FileStorageUtils$StorageListener storageListener
com.android.rockchip.camera2.rtsp.RTSPManager: android.content.Context context
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.View mControlsView
com.android.rockchip.camera2.service.StreamingSocketService: int CMD_THUMBNAIL
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TvPreviewHelper tvPreviewHelper
com.android.rockchip.camera2.util.TransformUtils: android.graphics.Matrix currentTransformMatrix
com.android.rockchip.camera2.video.TpImageLoader: long MAX_DISK_CACHE_SIZE
com.android.rockchip.camera2.service.StreamingService: java.lang.Thread socketServerThread
com.android.rockchip.camera2.video.TpVideoConfig: int keyFrameInterval
com.android.rockchip.camera2.video.TpVideoSystem$StreamType: com.android.rockchip.camera2.video.TpVideoSystem$StreamType CAMERA
com.android.rockchip.camera2.service.StreamingSocketService: java.lang.Thread serverThread
com.android.rockchip.camera2.util.NetworkManager$HotspotInfo: boolean enabled
com.android.rockchip.camera2.video.TpVideoSystem: java.lang.String TAG
com.android.rockchip.camera2.video.VideoEncoder: com.android.rockchip.camera2.video.TpVideoConfig tpVideoConfig
com.android.rockchip.video.R$styleable: int TpVideoPlayerView_autoPlay
com.android.rockchip.camera2.view.TpVideoPlayerView: java.lang.Runnable mStatusSyncRunnable
com.android.rockchip.camera2.view.TpTextureView$GestureState: com.android.rockchip.camera2.view.TpTextureView$GestureState IDLE
com.android.rockchip.camera2.view.TpCustomProgressBar: boolean mIsDragging
com.android.rockchip.camera2.video.VideoDecoder: boolean timeBaseNeedsReset
com.android.rockchip.camera2.util.NetworkManager: android.net.wifi.WifiManager wifiManager
com.android.rockchip.camera2.service.StreamingService: int IMAGE_PORT
com.android.rockchip.camera2.view.TpTextureView: java.lang.Runnable mResetGestureStateRunnable
com.android.rockchip.camera2.video.VideoDecoder: long startTimeMs
com.android.rockchip.camera2.video.TpVideoSystem: androidx.appcompat.app.AppCompatActivity activity
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_WBBLUEGAIN
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: java.nio.ByteBuffer ppsBuffer
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.TpVideoConfig tpVideoConfig
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: long RETRY_DELAY_MS
com.android.rockchip.camera2.util.TouptekIspParam$ParamData: boolean isDisabled
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: int keyFrameInterval
com.android.rockchip.camera2.video.TpVideoSystem: android.view.ViewGroup tvContainer
com.android.rockchip.camera2.video.TpCaptureImage: android.util.Size requestedSize
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: java.lang.String mimeType
com.android.rockchip.video.R$id: int speed_2_0
com.android.rockchip.camera2.util.SMBFileUploader$UploadTask: java.lang.String errorMessage
com.android.rockchip.camera2.video.VideoEncoder: java.lang.Runnable storageCheckRunnable
com.android.rockchip.camera2.video.TpVideoSystem: boolean isInitialized
com.android.rockchip.video.R$color: int tp_video_button_bg_normal
com.android.rockchip.camera2.util.SMBFileUploader$UploadTask: java.lang.String localFilePath
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TpVideoConfig videoConfig
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.atomic.AtomicBoolean tpctrlExists
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean mShowControls
com.android.rockchip.camera2.util.SMBFileUploader: java.lang.String TAG
com.android.rockchip.video.R$drawable: int ic_settings_white_24
com.android.rockchip.camera2.video.VideoEncoder: android.view.Surface encoderInputSurface
com.android.rockchip.camera2.video.ImageDecoder: android.os.Handler mainHandler
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution RES_720x480
com.android.rockchip.camera2.service.StreamingService: int ISP_PARAM_PORT
com.android.rockchip.camera2.view.TpImageView: java.lang.String TAG
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: int videoBitrate
com.android.rockchip.camera2.view.TpVideoPlayerView: android.os.Handler mStatusSyncHandler
com.android.rockchip.video.R$drawable: int tp_video_play_button_background
com.android.rockchip.camera2.service.StreamingSocketService: int CMD_ISP_PARAM_CONFIG
com.android.rockchip.camera2.video.TvPreviewHelper: java.lang.String INPUT_ID
com.android.rockchip.camera2.video.ImageDecoder: long MAX_CACHE_SIZE
com.android.rockchip.camera2.service.StreamingService: int socketPort
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: java.lang.Runnable onServiceDisconnected
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int port
com.android.rockchip.camera2.rtsp.config.RTSPConfig: java.lang.String host
com.android.rockchip.camera2.video.TpCaptureImage$Builder: com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener imageCaptureListener
com.android.rockchip.camera2.view.TpVideoPlayerView: java.lang.Runnable mProgressUpdater
com.android.rockchip.video.R$drawable: int tp_speed_dropdown_background
com.android.rockchip.camera2.service.StreamingService$StreamType: com.android.rockchip.camera2.service.StreamingService$StreamType[] $VALUES
com.android.rockchip.camera2.view.TpRoiView: int cameraHeight
com.android.rockchip.camera2.view.TpVideoPlayerView: com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener mVideoPlayerListener
com.android.rockchip.camera2.util.TouptekIspParam: int value
com.android.rockchip.camera2.video.TpCameraManager: android.hardware.camera2.CameraCaptureSession captureSession
com.android.rockchip.camera2.rtsp.service.RTSPService: com.android.rockchip.camera2.rtsp.service.RTSPService$RTSPBinder binder
com.android.rockchip.camera2.video.TpCaptureImage$Builder: android.util.Size imageSize
com.android.rockchip.video.R$id: int seekbar_progress
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String username
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: com.android.rockchip.camera2.rtsp.config.RTSPConfig config
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.atomic.AtomicBoolean manualRtspControl
com.android.rockchip.camera2.util.TouptekIspParam: int REQUEST_PARAM_RANGES
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: com.android.rockchip.camera2.rtsp.service.ProjectionData projectionData
com.android.rockchip.camera2.util.NetworkManager: android.content.BroadcastReceiver tetherChangeReceiver
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution RES_1920x1080
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType SCREEN
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int width
com.android.rockchip.camera2.view.TpRoiView: float lastTouchY
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: android.view.Surface inputSurface
com.android.rockchip.camera2.video.VideoDecoder: java.lang.Thread decodingThread
com.android.rockchip.camera2.service.StreamingService: java.lang.String TAG
com.android.rockchip.camera2.rtsp.service.RTSPService: com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder videoEncoder
com.android.rockchip.camera2.service.StreamingService: androidx.appcompat.app.AppCompatActivity activity
com.android.rockchip.camera2.view.TpRoiView: float touchSlop
com.android.rockchip.camera2.view.TpTextureView: android.view.GestureDetector mGestureDetector
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: int width
com.android.rockchip.camera2.util.TouptekIspParam: java.util.Map isDisableStates
com.android.rockchip.camera2.video.TpVideoSystem: java.lang.String currentVideoPath
com.android.rockchip.camera2.util.TouptekIspParam: java.util.Map DefaultValues
com.android.rockchip.camera2.view.TpRoiView: java.lang.Integer cachedRoiTop
com.android.rockchip.camera2.video.TpVideoConfig$Builder: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode bitrateMode
com.android.rockchip.camera2.view.TpTextureView$GestureState: com.android.rockchip.camera2.view.TpTextureView$GestureState[] $VALUES
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_ROI_HEIGHT
com.android.rockchip.camera2.view.TpCustomProgressBar: float mTrackHeight
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: int value
com.android.rockchip.video.R$style: int TpVideoSpeedItem
com.android.rockchip.camera2.video.TpCaptureImage: int FORMAT_BMP
com.android.rockchip.camera2.video.VideoEncoder: android.media.MediaCodec decoder
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: java.lang.Runnable clientMonitorTask
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: int bitrate
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.ExecutorService socketExecutor
com.android.rockchip.camera2.view.TpTextureView: float mLastTouchY
com.android.rockchip.camera2.video.TpVideoConfig: int height
com.android.rockchip.camera2.view.TpImageView: boolean mPanEnabled
com.android.rockchip.camera2.view.TpRoiView: boolean isMirrorEnabled
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.ExecutorService executorService
com.android.rockchip.camera2.video.TpImageLoader: android.util.LruCache memoryCache
com.android.rockchip.video.R$drawable: int ic_skip_previous_white_24
com.android.rockchip.video.R$layout: int tp_speed_dropdown_menu
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_GAMMA
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: java.lang.String TAG
com.android.rockchip.camera2.service.StreamingSocketService$1: java.util.concurrent.atomic.AtomicBoolean val$captureDone
com.android.rockchip.camera2.video.TpCameraManager$Builder: java.util.function.Consumer onCameraOpenedHandler
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.View mStepFrameButton
com.android.rockchip.camera2.view.TpRoiView: boolean isDragging
com.android.rockchip.camera2.video.TpCaptureImage: com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener imageCaptureListener
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.atomic.AtomicBoolean isRunning
com.android.rockchip.camera2.video.VideoEncoder: boolean isMuxerStarted
com.android.rockchip.camera2.rtsp.service.RTSPService: int NOTIFICATION_ID
com.android.rockchip.video.R$drawable: int tp_video_button_background
com.android.rockchip.video.R$style: int TpVideoProgressBar
com.android.rockchip.camera2.view.TpRoiView: boolean isROIEnabled
com.android.rockchip.camera2.view.TpVideoPlayerView: float mCurrentSpeed
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: long FORMAT_TIMEOUT_MS
com.android.rockchip.video.R$layout: int tp_video_player_controls
com.android.rockchip.camera2.view.TpTextureView: boolean mDoubleTapEnabled
com.android.rockchip.camera2.util.NetworkManager: android.net.ConnectivityManager connectivityManager
com.android.rockchip.video.R$drawable: int ic_pause_white_24
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode[] $VALUES
com.android.rockchip.camera2.video.TpVideoConfig: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec codec
com.android.rockchip.camera2.video.TpCaptureImage$Builder: android.os.HandlerThread backgroundThread
com.android.rockchip.camera2.view.TpRoiView: android.graphics.Matrix gestureTransformMatrix
com.android.rockchip.camera2.view.TpRoiView: android.graphics.RectF roiRect
com.android.rockchip.camera2.view.TpCustomProgressBar: float mCurrentThumbRadius
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: com.android.rockchip.camera2.rtsp.service.RTSPService service
com.android.rockchip.video.R$drawable: int tp_video_settings_button_background
com.android.rockchip.camera2.view.TpRoiView: android.graphics.Paint handleInnerPaint
com.android.rockchip.camera2.view.TpRoiView: int strokeWidth
com.android.rockchip.camera2.video.VideoDecoder: long startMediaTimeUs
com.android.rockchip.video.R$id: int speed_0_5
com.android.rockchip.camera2.util.NetworkManager: android.net.ConnectivityManager$NetworkCallback networkCallback
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: int bufferSize
com.android.rockchip.camera2.util.SMBFileUploader$ListDirectoriesTask: java.lang.String errorMessage
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_CONTRAST
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String serverIp
com.android.rockchip.camera2.video.VideoDecoder: float playbackSpeed
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: java.lang.String MIME_TYPE
com.android.rockchip.camera2.util.HdmiService$ReadThread: boolean currentNotifiedState
com.android.rockchip.camera2.util.FileStorageUtils: boolean isUsbConnected
com.android.rockchip.camera2.view.TpTextureView: com.android.rockchip.camera2.view.TpTextureView$OnZoomChangeListener mZoomChangeListener
com.android.rockchip.camera2.video.ImageDecoder: java.lang.String TAG
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TpCaptureImage tpCaptureImage
com.android.rockchip.camera2.util.HdmiService: com.android.rockchip.camera2.util.HdmiService$HdmiListener listener
com.android.rockchip.camera2.rtsp.service.RTSPService: java.lang.String rtspUrl
com.android.rockchip.camera2.video.TpCaptureImage$1: com.android.rockchip.camera2.video.TpCaptureImage this$0
com.android.rockchip.video.R$color: int tp_video_progress_thumb_stroke
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: android.content.Context context
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: boolean enabled
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener listener
com.android.rockchip.camera2.video.VideoDecoder: java.lang.String TAG
com.android.rockchip.camera2.video.VideoDecoder: android.view.Surface surface
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TpCameraManager tpCameraManager
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean formatConfigured
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_CTGREENGAIN
com.android.rockchip.camera2.video.TvPreviewHelper: android.net.Uri channelUri
com.android.rockchip.camera2.util.TouptekIspParam: java.util.Map MinValues
com.android.rockchip.camera2.video.TpImageLoader: android.os.Handler mainHandler
com.android.rockchip.camera2.view.TpTextureView: com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler mTouchEventHandler
com.android.rockchip.camera2.video.TpVideoSystem: boolean isCameraStarted
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.ConcurrentHashMap runningTpctrlProcesses
com.android.rockchip.camera2.view.TpRoiView: int HANDLE_TOUCH_RADIUS
com.android.rockchip.camera2.view.TpCustomProgressBar: int mProgress
com.android.rockchip.camera2.util.touptek_serial_rk: com.android.rockchip.camera2.util.touptek_serial_rk$DeviceStateCallback deviceStateCallback
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String remotePath
com.android.rockchip.camera2.service.StreamingService: android.os.Handler mainHandler
com.android.rockchip.video.R$id: int speed_1_0
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_ISP_DEFAULT_TYPE
com.android.rockchip.camera2.util.SMBFileUploader: android.content.Context context
com.android.rockchip.camera2.video.ImageDecoder: java.util.concurrent.ConcurrentHashMap activeRequests
com.android.rockchip.camera2.video.TpCameraManager: android.media.ImageReader imageReader
com.android.rockchip.camera2.video.VideoEncoder: com.android.rockchip.camera2.video.VideoEncoder$VideoDataOutputCallback mOutputCallback
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_EXPOSUREGAIN
com.android.rockchip.camera2.video.TpCameraManager$Builder: android.content.Context context
com.android.rockchip.camera2.video.TpCameraManager$Builder: java.util.function.BiConsumer onCameraErrorHandler
com.android.rockchip.camera2.view.TpVideoPlayerView: android.os.Handler mControlsHandler
com.android.rockchip.camera2.view.TpRoiView: int ANIMATION_DURATION
com.android.rockchip.camera2.view.TpTextureView: android.graphics.Matrix mMatrix
com.android.rockchip.camera2.video.TpVideoConfig$Builder: int width
com.android.rockchip.camera2.rtsp.RTSPManager: boolean streaming
com.android.rockchip.camera2.video.TpImageLoader: java.util.concurrent.ExecutorService executor
com.android.rockchip.camera2.video.VideoEncoder: boolean isRecording
com.android.rockchip.camera2.view.TpRoiView: int HANDLE_RADIUS
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam[] $VALUES
com.android.rockchip.camera2.util.SMBFileUploader: java.lang.String shareName
com.android.rockchip.camera2.video.TpCaptureImage: java.lang.String outputPath
com.android.rockchip.camera2.video.VideoEncoder: android.media.MediaCodec encoder
com.android.rockchip.camera2.view.TpCustomProgressBar: int mSecondaryProgress
com.android.rockchip.camera2.view.TpVideoPlayerView: java.lang.Runnable mHideControlsRunnable
com.android.rockchip.camera2.video.TpCaptureImage$Builder: android.os.Handler backgroundHandler
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo: java.lang.String description
com.android.rockchip.camera2.service.StreamingSocketService: int THUMBNAIL_WIDTH
com.android.rockchip.camera2.util.SMBFileUploader: java.lang.String PREFS_NAME
com.android.rockchip.video.R$style: int TpVideoPlayButton
com.android.rockchip.video.R$color: int tp_video_button_text_color
com.android.rockchip.camera2.video.TpVideoConfig: int bitRate
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: boolean recordMic
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection serviceConnection
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_ROI_WIDTH
com.android.rockchip.camera2.view.TpRoiView: android.graphics.Paint handlePaint
com.android.rockchip.camera2.view.TpVideoPlayerView: android.widget.TextView mCurrentTimeDisplay
com.android.rockchip.camera2.video.ImageDecoder: boolean cacheInitialized
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: int height
com.android.rockchip.camera2.video.TpCameraManager: android.hardware.camera2.CameraManager cameraManager
com.android.rockchip.camera2.view.TpImageView: com.android.rockchip.camera2.view.TpImageView$OnZoomChangeListener mZoomChangeListener
com.android.rockchip.camera2.view.TpRoiView: android.animation.ValueAnimator rectAnimator
com.android.rockchip.camera2.service.StreamingSocketService: java.net.ServerSocket serverSocket
com.android.rockchip.camera2.util.TransformUtils: java.lang.String TAG
com.android.rockchip.camera2.rtsp.RTSPManager: java.util.function.Consumer onStreamErrorHandler
com.android.rockchip.camera2.view.TpCustomProgressBar: float mThumbScale
com.android.rockchip.camera2.video.TpCameraManager: java.util.function.Consumer onCameraDisconnectedHandler
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec H265
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType CAMERA
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: int frameRate
com.android.rockchip.camera2.view.TpTextureView: boolean mPanEnabled
com.android.rockchip.camera2.util.HdmiService: java.lang.String TAG
com.android.rockchip.camera2.view.TpRoiView: boolean isResizing
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: int BIT_RATE
com.android.rockchip.camera2.video.VideoEncoder: long MIN_STORAGE_SPACE_BYTES
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType currentStreamType
com.android.rockchip.camera2.video.TpImageLoader: java.lang.String TAG
com.android.rockchip.camera2.rtsp.config.RTSPConfig: boolean recordMic
com.android.rockchip.camera2.video.TpCaptureImage$Builder: java.util.function.Consumer onErrorHandler
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_EXPOSURECHOICE
com.android.rockchip.camera2.view.TpRoiView: java.lang.Integer cachedRoiLeft
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo: java.lang.String name
com.android.rockchip.camera2.video.TpVideoSystem: android.content.Context context
com.android.rockchip.camera2.video.VideoDecoder: android.media.MediaExtractor mediaExtractor
com.android.rockchip.camera2.service.StreamingService: java.lang.String selectedNetInterface
com.android.rockchip.camera2.util.NetworkManager: android.os.Handler mainHandler
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: android.media.MediaCodec$BufferInfo bufferInfo
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle NONE
com.android.rockchip.video.R$style: int TpVideoSpeedDialog
com.android.rockchip.camera2.view.TpTextureView$GestureState: com.android.rockchip.camera2.view.TpTextureView$GestureState PANNING
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_HZ
com.android.rockchip.camera2.view.TpVideoPlayerView: int CONTROLS_HIDE_DELAY
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.atomic.AtomicBoolean servicesStarted
com.android.rockchip.video.R$id: int btn_step_frame
com.android.rockchip.camera2.video.TpCaptureImage$Builder: java.util.function.Consumer onImageSavedHandler
com.android.rockchip.camera2.service.StreamingSocketService: java.lang.String tempImagePath
com.android.rockchip.video.R$color: int white
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.View mPreviousVideoButton
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: java.lang.Thread encodingThread
com.android.rockchip.camera2.video.TpCaptureImage: android.os.Handler backgroundHandler
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int height
com.android.rockchip.camera2.view.TpTextureView: com.android.rockchip.camera2.view.TpRoiView mTpRoiView
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.atomic.AtomicBoolean fullyInitialized
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: android.hardware.display.VirtualDisplay virtualDisplay
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: java.util.concurrent.atomic.AtomicBoolean isClientConnected
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: java.lang.String TAG
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType streamType
com.android.rockchip.camera2.util.NetworkManager: java.lang.Runnable hotspotCheckRunnable
com.android.rockchip.video.R$id: int btn_play_pause
com.android.rockchip.video.R$drawable: int tp_speed_item_selected_background
com.android.rockchip.camera2.view.TpTextureView$GestureState: com.android.rockchip.camera2.view.TpTextureView$GestureState SCALING
com.android.rockchip.camera2.view.TpTextureView: com.android.rockchip.camera2.view.TpTextureView$ScaleInfo mScaleInfo
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_SHARPNESS
com.android.rockchip.camera2.video.TpCaptureImage$Builder: int imageOutputFormat
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: android.media.projection.MediaProjection mediaProjection
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_ROI_LEFT
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean mIsPendingPlay
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: java.lang.String MIME_TYPE
com.android.rockchip.camera2.util.HdmiService: java.lang.String lastStatus
com.android.rockchip.camera2.util.TouptekIspParam$ParamData: int max
com.android.rockchip.camera2.util.TouptekIspParam$ParamData: int min
com.android.rockchip.camera2.view.TpImageView$GestureState: com.android.rockchip.camera2.view.TpImageView$GestureState[] $VALUES
com.android.rockchip.camera2.rtsp.RTSPManager: androidx.activity.result.ActivityResultLauncher screenCaptureLauncher
com.android.rockchip.camera2.view.TpTextureView: android.os.Handler mHandler
com.android.rockchip.camera2.view.TpVideoPlayerView: android.widget.TextView mTotalTimeDisplay
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.View mPlayPauseButton
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.Surface mCurrentSurface
com.android.rockchip.camera2.service.StreamingSocketService: int CMD_ISP_PARAM
com.android.rockchip.video.R$drawable: int ic_play_arrow_white_24
com.android.rockchip.camera2.view.TpCustomProgressBar: java.lang.String TAG
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_COLORORGRAY
com.android.rockchip.camera2.view.TpVideoPlayerView: java.lang.String[] SPEED_TEXTS
com.android.rockchip.camera2.video.TpCameraManager: android.os.HandlerThread backgroundThread
com.android.rockchip.video.R$drawable: int tp_video_progress_thumb
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: int height
com.android.rockchip.camera2.video.TvPreviewHelper: android.view.ViewGroup containerView
com.android.rockchip.video.R$color: int tp_video_button_stroke_normal
com.android.rockchip.camera2.video.TpCaptureImage: java.lang.String TAG
com.android.rockchip.camera2.view.TpRoiView: float lastTouchX
com.android.rockchip.camera2.service.StreamingService: java.net.ServerSocket serverSocket
com.android.rockchip.camera2.video.VideoEncoder: int videoTrackIndex
com.android.rockchip.camera2.video.TpCaptureImage: int MAX_CONCURRENT_PROCESSING
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: int width
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder: int bufferSize
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: com.android.rockchip.camera2.video.VideoEncoder videoEncoder
com.android.rockchip.video.R$color: int tp_video_progress_secondary
com.android.rockchip.camera2.util.TouptekIspParam: java.util.Map MaxValues
com.android.rockchip.video.R$color: int tp_video_button_stroke_pressed
com.android.rockchip.camera2.util.HdmiService: boolean lastSignalLocked
com.android.rockchip.camera2.video.TvPreviewHelper: java.lang.String TAG
com.android.rockchip.camera2.service.StreamingService: android.content.Context context
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder videoEncoder
com.android.rockchip.camera2.view.TpCustomProgressBar: android.graphics.Paint mTrackPaint
com.android.rockchip.camera2.view.TpRoiView: int MIN_RECT_SIZE
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_BANDWIDTH
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: android.media.MediaCodec encoder
com.android.rockchip.camera2.video.TpImageLoader: int MEMORY_CACHE_SIZE
com.android.rockchip.camera2.video.ImageDecoder: java.util.concurrent.atomic.AtomicLong requestIdGenerator
com.android.rockchip.camera2.video.VideoDecoder: boolean isPaused
com.android.rockchip.camera2.video.TvPreviewHelper: int MSG_START_TV
com.android.rockchip.camera2.view.TpRoiView: android.graphics.RectF animatedRect
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String shareName
com.android.rockchip.camera2.service.StreamingSocketService: android.os.Handler mainHandler
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_VERSION
com.android.rockchip.camera2.rtsp.service.RTSPService: com.android.rockchip.camera2.rtsp.encoder.AudioEncoder audioEncoder
com.android.rockchip.video.R$drawable: int ic_fast_forward_white_24
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode VBR
com.android.rockchip.video.R$color: int tp_video_play_button_bg_pressed
com.android.rockchip.camera2.view.TpImageView: com.android.rockchip.camera2.view.TpImageView$GestureState mGestureState
com.android.rockchip.camera2.rtsp.RTSPManager: java.util.function.Consumer onStreamStartedHandler
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: int height
com.android.rockchip.camera2.video.VideoEncoder: android.os.Handler storageCheckHandler
com.android.rockchip.camera2.service.StreamingSocketService: com.android.rockchip.camera2.service.StreamingSocketService$LogListener logListener
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: android.os.Handler mainHandler
com.android.rockchip.camera2.util.SMBFileUploader$UploadTask: com.android.rockchip.camera2.util.SMBFileUploader$UploadListener callback
com.android.rockchip.video.R$drawable: int tp_speed_item_background
com.android.rockchip.camera2.util.SMBFileUploader$UploadTask: java.lang.String remoteFilePath
com.android.rockchip.camera2.video.VideoEncoder: java.lang.Runnable onFileSizeLimitReachedHandler
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_SATURATION
com.android.rockchip.camera2.video.ImageDecoder: java.util.concurrent.ConcurrentHashMap fileInfoCache
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: long startTimeMs
com.android.rockchip.camera2.service.StreamingSocketService: int PORT
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_HUE
com.android.rockchip.camera2.video.TpCameraManager: java.lang.String TAG
com.android.rockchip.video.R$color: int tp_video_progress_thumb
com.android.rockchip.camera2.video.TpVideoSystem$StreamType: com.android.rockchip.camera2.video.TpVideoSystem$StreamType SCREEN
com.android.rockchip.camera2.video.TpVideoConfig$Builder: int keyFrameInterval
com.android.rockchip.camera2.view.TpImageView: boolean mDoubleTapEnabled
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.View mNextVideoButton
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection$UrlCallback onServiceConnected
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_CTBLUEGAIN
com.android.rockchip.camera2.video.TpCaptureImage: int imageOutputFormat
com.android.rockchip.camera2.video.TpVideoSystem$2: com.android.rockchip.camera2.video.TpVideoSystem this$0
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.atomic.AtomicBoolean socketServerRunning
com.android.rockchip.camera2.service.StreamingService: java.util.concurrent.atomic.AtomicLong lastHeartbeatTime
com.android.rockchip.camera2.rtsp.RTSPManager: boolean waitingForPermission
com.android.rockchip.camera2.view.TpCustomProgressBar: android.animation.ValueAnimator mThumbAnimator
com.android.rockchip.camera2.service.StreamingSocketService: java.util.concurrent.atomic.AtomicBoolean isRunning
com.android.rockchip.camera2.service.StreamingSocketService: java.lang.Object ispParamLock
com.android.rockchip.camera2.video.TpVideoSystem$1: java.lang.String val$videoPath
com.android.rockchip.camera2.service.StreamingSocketService: java.util.concurrent.ExecutorService executor
com.android.rockchip.camera2.view.TpCustomProgressBar: android.graphics.Paint mThumbShadowPaint
com.android.rockchip.camera2.rtsp.service.RTSPService: com.pedro.rtspserver.RtspServer rtspServer
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.View mRewindButton
com.android.rockchip.camera2.video.TpImageLoader: java.io.File diskCacheDir
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String TAG
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle TOP_LEFT
com.android.rockchip.camera2.video.ImageDecoder: java.util.concurrent.ExecutorService executor
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: int keyFrameInterval
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: android.media.MediaCodec$BufferInfo bufferInfo
com.android.rockchip.camera2.video.TpCameraManager: android.hardware.camera2.CameraDevice cameraDevice
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.service.StreamingService$StreamType currentStreamType
com.android.rockchip.camera2.video.VideoEncoder: android.view.Surface previewSurface
com.android.rockchip.camera2.service.StreamingService: long heartbeatTimeout
com.android.rockchip.camera2.rtsp.RTSPManager: java.lang.String networkInterface
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: android.media.projection.MediaProjection mediaProjection
com.android.rockchip.camera2.util.SMBFileUploader$TestConnectionTask: com.android.rockchip.camera2.util.SMBFileUploader$UploadListener callback
com.android.rockchip.camera2.service.StreamingService: long HEARTBEAT_TIMEOUT
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: android.content.Context context
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: android.media.MediaCodec encoder
com.android.rockchip.camera2.video.TpVideoConfig: int frameRate
com.android.rockchip.camera2.video.VideoEncoder: android.view.Surface decoderOutputSurface
com.android.rockchip.camera2.rtsp.service.ProjectionData: android.content.Intent data
com.android.rockchip.camera2.rtsp.service.RTSPService: com.android.rockchip.camera2.rtsp.config.RTSPConfig config
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: int CHANNEL_COUNT
com.android.rockchip.camera2.view.TpTextureView: android.view.ScaleGestureDetector mScaleGestureDetector
com.android.rockchip.camera2.video.VideoEncoder: boolean isFat32
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager instance
com.android.rockchip.camera2.video.TpVideoSystem$StreamType: com.android.rockchip.camera2.video.TpVideoSystem$StreamType[] $VALUES
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: com.android.rockchip.camera2.rtsp.config.RTSPConfig config
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: android.media.AudioRecord audioRecord
com.android.rockchip.camera2.video.ImageDecoder: java.lang.String CACHE_FILE_EXTENSION
com.android.rockchip.camera2.video.TpCaptureImage: int FORMAT_TIFF
com.android.rockchip.camera2.video.VideoDecoder: long lastFrameTimeUs
com.android.rockchip.video.R$id: int tv_total_time
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: int frameRate
com.android.rockchip.camera2.service.StreamingService: java.lang.String TPCTRL_IP
com.android.rockchip.camera2.video.TpVideoSystem: boolean isStreaming
com.android.rockchip.camera2.video.VideoDecoder: boolean isDecoding
com.android.rockchip.camera2.view.TpRoiView: int HANDLE_INNER_RADIUS
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.service.StreamingService$StreamType currentStreamType
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_MIRROR
com.android.rockchip.camera2.view.TpImageView: boolean mZoomEnabled
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo: java.lang.String ipAddress
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_WBREDGAIN
com.android.rockchip.camera2.view.TpImageView$GestureState: com.android.rockchip.camera2.view.TpImageView$GestureState SCALING
com.android.rockchip.camera2.video.TpVideoConfig$Builder: int bitRate
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean mAutoPlay
com.android.rockchip.camera2.view.TpCustomProgressBar: boolean mIsPressed
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean mIsUserSeeking
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: java.util.concurrent.atomic.AtomicBoolean isEncoding
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle BOTTOM_RIGHT
com.android.rockchip.video.R$drawable: int ic_fast_rewind_white_24
com.android.rockchip.camera2.view.TpRoiView: boolean debugMode
com.android.rockchip.camera2.service.StreamingSocketService: int ISP_PARAM_PORT
com.android.rockchip.camera2.video.VideoEncoder: java.lang.String TAG
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: java.nio.ByteBuffer spsBuffer
com.android.rockchip.camera2.service.StreamingSocketService$1: java.util.concurrent.atomic.AtomicBoolean val$captureSuccess
com.android.rockchip.camera2.view.TpVideoPlayerView: java.lang.String mVideoPath
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: int keyFrameInterval
com.android.rockchip.video.R$drawable: int ic_step_frame_white_24
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: java.lang.String TAG
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: int port
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder: android.content.Context context
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: int SAMPLE_RATE
com.android.rockchip.camera2.video.VideoEncoder: java.util.function.BiConsumer onErrorHandler
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: android.media.projection.MediaProjection mediaProjection
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution[] $VALUES
com.android.rockchip.video.R$id: int speed_0_75
com.android.rockchip.camera2.view.TpTextureView: java.lang.String TAG
com.android.rockchip.camera2.util.HdmiService: java.lang.String mHdmiRxDevicePath
com.android.rockchip.camera2.util.NetworkManager$HotspotInfo: java.lang.String info
com.android.rockchip.camera2.video.VideoDecoder: boolean isFrameByFrame
com.android.rockchip.camera2.view.TpVideoPlayerView$1: com.android.rockchip.camera2.view.TpVideoPlayerView this$0
com.android.rockchip.video.R$style: int TpVideoSettingsButton
com.android.rockchip.camera2.service.StreamingSocketService$1: com.android.rockchip.camera2.service.StreamingSocketService this$0
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean mIsInitialized
com.android.rockchip.video.R$style: int TpVideoSpeedDropdownItem
com.android.rockchip.camera2.service.StreamingSocketService: int CMD_FULLSIZE
com.android.rockchip.camera2.video.TvPreviewHelper: android.media.tv.TvView tvView
com.android.rockchip.camera2.view.TpTextureView: float mLastTouchX
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_LDCRATIO
com.android.rockchip.camera2.util.TouptekIspParam: java.util.List dataChangedListeners
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.VideoEncoder videoEncoder
com.android.rockchip.camera2.video.VideoEncoder: java.lang.Runnable onStorageFullHandler
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.config.RTSPConfig config
com.android.rockchip.camera2.video.VideoEncoder: android.media.MediaMuxer mediaMuxer
com.android.rockchip.camera2.video.TpCaptureImage$Builder: int imageFormat
com.android.rockchip.camera2.util.SMBFileUploader$UploadTask: java.lang.String remoteFileName
com.android.rockchip.camera2.view.TpRoiView: android.graphics.RectF originalRoiRect
com.android.rockchip.camera2.view.TpImageView$GestureState: com.android.rockchip.camera2.view.TpImageView$GestureState IDLE
com.android.rockchip.camera2.video.TpVideoConfig: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode bitrateMode
com.android.rockchip.video.R$color: int tp_video_text_normal
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: com.android.rockchip.camera2.rtsp.service.ProjectionData projectionData
com.android.rockchip.camera2.view.TpCustomProgressBar: android.graphics.Paint mThumbPaint
com.android.rockchip.camera2.util.HdmiService: com.android.rockchip.camera2.util.HdmiService instance
com.android.rockchip.video.R$color: int black
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_ROI_TOP
com.android.rockchip.camera2.video.TpCameraManager$Builder: java.util.function.Consumer onCameraDisconnectedHandler
com.android.rockchip.camera2.video.TpCameraManager: java.util.function.Consumer onCameraOpenedHandler
com.android.rockchip.camera2.view.TpRoiView: boolean isFlipEnabled
com.android.rockchip.video.R$drawable: int tp_video_controls_background
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.touptek_serial_rk serialInstance
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.service.StreamingService streamingService
com.android.rockchip.camera2.util.NetworkManager: com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener listener
com.android.rockchip.video.R$color: int tp_video_button_bg_disabled
com.android.rockchip.camera2.video.TpCaptureImage$Builder$1: com.android.rockchip.camera2.video.TpCaptureImage$Builder this$0
com.android.rockchip.camera2.video.TpCaptureImage: int FORMAT_JPEG
com.android.rockchip.camera2.view.TpVideoPlayerView$6: com.android.rockchip.camera2.view.TpVideoPlayerView this$0
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle BOTTOM_LEFT
com.android.rockchip.camera2.util.NetworkManager: android.content.Context context
com.android.rockchip.camera2.video.VideoDecoder: java.lang.String videoPath
com.android.rockchip.camera2.video.TpCaptureImage: boolean isCaptureRequested
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.service.RTSPStreamer streamer
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int keyFrameInterval
com.android.rockchip.camera2.video.TpCameraManager$Builder: java.util.function.Consumer onCameraConfigFailedHandler
com.android.rockchip.camera2.view.TpRoiView: java.lang.Integer cachedRoiHeight
com.android.rockchip.camera2.util.SMBFileUploader: java.lang.String remotePath
com.android.rockchip.camera2.video.TpVideoConfig$Builder: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec codec
com.android.rockchip.video.R$style: int TpVideoControlButton
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: int retryCount
com.android.rockchip.video.R$style: int TpVideoSpeedDropdownAnimation
com.android.rockchip.camera2.view.TpVideoPlayerView: android.view.View mSettingsButton
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_WBGREENGAIN
com.android.rockchip.camera2.util.SMBFileUploader: boolean enabled
com.android.rockchip.camera2.video.TpVideoSystem: boolean isRecording
com.android.rockchip.camera2.util.HdmiService: boolean threadStatus
com.android.rockchip.camera2.video.TpCaptureImage: android.os.HandlerThread backgroundThread
com.android.rockchip.camera2.rtsp.RTSPManager: boolean initialized
com.android.rockchip.camera2.video.TvPreviewHelper: android.content.Context context
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener listener
com.android.rockchip.camera2.view.TpVideoPlayerView: com.android.rockchip.camera2.view.TpCustomProgressBar mProgressSeekBar
com.android.rockchip.camera2.util.HdmiService$ReadThread: int REQUIRED_STABLE_COUNT
com.android.rockchip.video.R$color: int tp_video_play_button_bg_normal
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_DENOISE
com.android.rockchip.video.R$id: int btn_rewind
com.android.rockchip.camera2.service.StreamingService: int CMD_HEARTBEAT
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.VideoDecoder currentVideoDecoder
com.android.rockchip.camera2.video.VideoEncoder: long MAX_FILE_SIZE_FAT32
com.android.rockchip.video.R$style: int TpVideoSpeedDialogAnimation
com.android.rockchip.camera2.view.TpCustomProgressBar: float mThumbRadiusPressed
com.android.rockchip.camera2.view.TpRoiView: boolean ignoreNextROIParamChanges
com.android.rockchip.camera2.view.TpRoiView: java.lang.Integer cachedRoiWidth
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_CTREDGAIN
com.android.rockchip.camera2.util.TouptekIspParam$ParamData: int defaultValue
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution RES_3840x2160
com.android.rockchip.camera2.video.VideoDecoder: android.media.MediaCodec mediaCodec
com.android.rockchip.camera2.video.VideoEncoder: android.util.Size videoSize
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.service.StreamingSocketService streamingSocketService
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_BRIGHTNESS
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution RES_640x480
com.android.rockchip.camera2.util.SMBFileUploader: java.lang.String serverIp
com.android.rockchip.camera2.util.SMBFileUploader: java.lang.String password
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam$OnSerialStateChangedListener serialStateListener
com.android.rockchip.camera2.video.VideoDecoder: long videoDuration
com.android.rockchip.video.R$attr: int autoPlay
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam TOUPTEK_PARAM_WBCHOICE
com.android.rockchip.video.R$drawable: int ic_skip_next_white_24
com.android.rockchip.camera2.video.TpVideoConfig: java.lang.String TAG
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: com.pedro.rtspserver.RtspServer rtspServer
com.android.rockchip.camera2.view.TpRoiView: com.android.rockchip.camera2.view.TpRoiView$ControlHandle activeHandle
com.android.rockchip.video.R$color: int tp_video_progress_bg
com.android.rockchip.camera2.rtsp.service.RTSPService: void onConnectionSuccess()
com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener: void onPreviousVideo()
com.android.rockchip.camera2.util.SMBFileUploader: SMBFileUploader(android.content.Context)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: RTSPConfig$Resolution(java.lang.String,int,int,int)
com.android.rockchip.camera2.service.StreamingService: boolean sendIspConfig(com.android.rockchip.camera2.util.TouptekIspParam,com.android.rockchip.camera2.util.TouptekIspParam$ParamData)
com.android.rockchip.camera2.util.TransformUtils: void applyZoom(android.view.TextureView,float,float,float)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void lambda$notifyStreamStarted$1(java.lang.String)
com.android.rockchip.camera2.view.TpImageView$OnZoomChangeListener: void onZoomChanged(float,float,float)
com.android.rockchip.camera2.video.TpImageLoader: int calculateInSampleSize(android.graphics.BitmapFactory$Options,int,int)
com.android.rockchip.camera2.video.TpCameraManager$Builder: TpCameraManager$Builder(android.content.Context)
com.android.rockchip.camera2.video.ImageDecoder: void lambda$loadImageAsync$3(int[],android.widget.ImageView)
com.android.rockchip.camera2.video.VideoDecoder: long getCurrentPosition()
com.android.rockchip.camera2.util.NetworkManager: com.android.rockchip.camera2.util.NetworkManager$HotspotInfo getCurrentHotspotState()
com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener: void onStreamError(java.lang.String)
com.android.rockchip.camera2.view.TpCustomProgressBar: TpCustomProgressBar(android.content.Context,android.util.AttributeSet)
com.android.rockchip.camera2.video.TpVideoSystem: void pauseVideo()
com.android.rockchip.camera2.video.TpVideoSystem: void startRecording(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$recreateSurface$14()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$setupControlListeners$5(android.view.View)
com.android.rockchip.camera2.view.TpRoiView: void lambda$animateRectDisappear$3(android.graphics.RectF,float,float,android.animation.ValueAnimator)
com.android.rockchip.camera2.video.VideoEncoder$VideoDataOutputCallback: void onVideoDataAvailable(java.nio.ByteBuffer,android.media.MediaCodec$BufferInfo)
com.android.rockchip.camera2.video.TpVideoConfig$Builder: TpVideoConfig$Builder(int,int)
com.android.rockchip.camera2.util.touptek_serial_rk: void onDeviceStateChanged(boolean)
com.android.rockchip.camera2.util.FileStorageUtils: boolean isRemovableStorage(android.content.Context,java.lang.String)
com.android.rockchip.camera2.util.TouptekIspParam: void setParamMaxValue(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.video.R$id: R$id()
com.android.rockchip.camera2.view.TpVideoPlayerView: void initControlsView()
com.android.rockchip.camera2.util.SMBFileUploader: void setConnectionParams(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)
com.android.rockchip.camera2.service.StreamingService: void release()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$startRecording$1(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.util.NetworkManager: void updateHotspotStatus()
com.android.rockchip.camera2.view.TpVideoPlayerView: java.lang.String formatTime(long)
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam valueOf(java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager$1: RTSPManager$1(com.android.rockchip.camera2.rtsp.RTSPManager)
com.android.rockchip.camera2.video.ImageDecoder: void lambda$clearCache$1()
com.android.rockchip.camera2.video.TpImageLoader: void lambda$loadThumbnail$2(java.lang.String,java.lang.String,int[],android.content.Context,android.widget.ImageView)
com.android.rockchip.camera2.service.StreamingService: boolean isStreaming()
com.android.rockchip.camera2.view.TpRoiView: float[] mapPoint(float,float)
com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener: void onImageSaved(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$setupControlListeners$1()
com.android.rockchip.camera2.view.TpRoiView: void lambda$initAnimator$1(android.animation.ValueAnimator)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$stop$18()
com.android.rockchip.camera2.util.HdmiService: void setHdmiListener(com.android.rockchip.camera2.util.HdmiService$HdmiListener)
com.android.rockchip.camera2.video.VideoDecoder: void seekTo(long)
com.android.rockchip.camera2.util.NetworkManager: void updateNetworkStatus()
com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener: void onError(java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem: void setStreamType(com.android.rockchip.camera2.video.TpVideoSystem$StreamType)
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder$Builder setSize(android.util.Size)
com.android.rockchip.camera2.view.TpVideoPlayerView: void setVideoPlayerListener(com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener)
com.android.rockchip.camera2.view.TpCustomProgressBar: void init()
com.android.rockchip.camera2.video.TpVideoSystem: void initVideoEncoder(android.view.Surface)
com.android.rockchip.camera2.service.StreamingService: void lambda$configureRtspCallbacks$2()
com.android.rockchip.camera2.view.TpVideoPlayerView: com.android.rockchip.camera2.video.TpVideoSystem getVideoSystem()
com.android.rockchip.camera2.video.VideoEncoder: long getCurrentFileSize()
com.android.rockchip.camera2.view.TpCustomProgressBar: void setProgress(int,boolean)
com.android.rockchip.camera2.view.TpRoiView: void clearROIParamCache()
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setFrameRate(int)
com.android.rockchip.camera2.util.TouptekIspParam: void init(android.content.Context)
com.android.rockchip.camera2.service.StreamingService: boolean startTpctrlConsole(java.lang.String)
com.android.rockchip.camera2.service.StreamingService: void startRtspService()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$setupControlListeners$8(android.view.View)
com.android.rockchip.camera2.view.TpRoiView: void applyISPParamUpdate(int,int,int,int)
com.android.rockchip.camera2.rtsp.service.RTSPService: void startForeground()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$36()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean startStream()
com.android.rockchip.camera2.video.TpVideoSystem: java.lang.String getCurrentVideoPath()
com.android.rockchip.camera2.service.StreamingService: void lambda$updateVideoEncoder$12(com.android.rockchip.camera2.video.VideoEncoder)
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.service.StreamingService$HeartbeatListener createHeartbeatListener()
com.android.rockchip.camera2.service.StreamingSocketService$1: StreamingSocketService$1(com.android.rockchip.camera2.service.StreamingSocketService,java.util.concurrent.atomic.AtomicBoolean,java.util.concurrent.atomic.AtomicBoolean)
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String getServerIp()
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void lambda$onVideoPlaybackCompleted$1()
com.android.rockchip.camera2.util.NetworkManager: void stopMonitoring()
com.android.rockchip.camera2.service.StreamingService: void setManualRtspControl(boolean)
com.android.rockchip.camera2.service.StreamingSocketService: void processIspParam(int,int)
com.android.rockchip.camera2.video.TpImageLoader: void initializeDiskCache(android.content.Context)
com.android.rockchip.camera2.view.TpRoiView: boolean isPointNearHandle(float,float,float,float)
com.android.rockchip.camera2.service.StreamingSocketService: void lambda$runServer$0(java.net.Socket)
com.android.rockchip.camera2.util.TransformUtils: void applyPan(android.view.TextureView,com.android.rockchip.camera2.view.TpRoiView,float,float)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$loadFullImage$7(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.service.StreamingService: void stopSubServices()
com.android.rockchip.camera2.video.TpImageLoader: android.graphics.Bitmap loadVideoThumbnailWithGlide(android.content.Context,java.lang.String,int,int)
com.android.rockchip.camera2.util.TouptekIspParam$1: TouptekIspParam$1()
com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener: void onStartTrackingTouch(com.android.rockchip.camera2.view.TpCustomProgressBar)
com.android.rockchip.camera2.video.TpVideoConfig$Builder: com.android.rockchip.camera2.video.TpVideoConfig$Builder setBitrateMode(com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode)
com.android.rockchip.camera2.rtsp.config.RTSPConfig: boolean isRecordMic()
com.android.rockchip.camera2.view.TpImageView: float getCurrentScale()
com.android.rockchip.camera2.video.TpImageLoader: android.graphics.Bitmap decodeTiffWithSampling(java.lang.String,int,int)
com.android.rockchip.camera2.view.TpImageView: float getMinScale()
com.android.rockchip.camera2.video.TpImageLoader: int[] getImageViewDimensions(android.widget.ImageView)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void onVideoFormatChanged(android.media.MediaFormat,java.nio.ByteBuffer,java.nio.ByteBuffer)
com.android.rockchip.camera2.view.TpImageView$GestureState: com.android.rockchip.camera2.view.TpImageView$GestureState valueOf(java.lang.String)
com.android.rockchip.camera2.util.HdmiService: android.util.Size getHdmiResolution()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void notifyStreamStarted(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$setupControlListeners$2(android.view.View)
com.android.rockchip.camera2.service.StreamingService: void lambda$startRtspManually$16(java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager$1: void onStreamStopped()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$setupControlListeners$3(android.view.View)
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: void lambda$startEncoding$0(com.pedro.rtspserver.RtspServer,com.android.rockchip.camera2.rtsp.encoder.AudioEncoder)
com.android.rockchip.camera2.service.StreamingService: void startSocketServer()
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String createImagePath(android.content.Context,java.lang.String,boolean,java.lang.String)
com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener: void onDirectoriesLoaded(java.util.List)
com.android.rockchip.camera2.util.TouptekIspParam: java.util.Map getAllCurrentValues()
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemAdapter: TpVideoSystem$TpVideoSystemAdapter()
com.android.rockchip.camera2.video.TpVideoSystem$ListenerAction: void execute(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener: void onEthernetStateChanged(boolean)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: com.android.rockchip.camera2.rtsp.config.RTSPConfig build()
com.android.rockchip.camera2.video.TpImageLoader: void lambda$loadFullImage$5(java.lang.String,java.lang.String,android.widget.ImageView)
com.android.rockchip.camera2.video.TpVideoSystem$2: void lambda$onStreamError$1(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpCaptureImage: void writeTag(java.io.FileOutputStream,int,int,int,int)
com.android.rockchip.camera2.util.TransformUtils: android.graphics.Matrix resetImageViewTransform(android.widget.ImageView)
com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener: void onPermissionGranted(java.lang.String)
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: void encodeVideoLoop(com.pedro.rtspserver.RtspServer,com.android.rockchip.camera2.rtsp.encoder.AudioEncoder)
com.android.rockchip.camera2.rtsp.RTSPManager$1: void lambda$onStreamError$2(java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager: java.lang.String getNetworkInterface()
com.android.rockchip.camera2.video.TpVideoSystem$StreamType: com.android.rockchip.camera2.video.TpVideoSystem$StreamType valueOf(java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem: void initializeStreamingService()
com.android.rockchip.camera2.util.FileStorageUtils: void startUsbDriveMonitor(android.content.Context,com.android.rockchip.camera2.util.FileStorageUtils$StorageListener)
com.android.rockchip.camera2.view.TpTextureView: boolean performScale(float,float,float)
com.android.rockchip.camera2.service.StreamingSocketService: void handleClient(java.net.Socket)
com.android.rockchip.camera2.video.TpCameraManager$Builder: com.android.rockchip.camera2.video.TpCameraManager$Builder onCameraOpened(java.util.function.Consumer)
com.android.rockchip.camera2.video.TpImageLoader: TpImageLoader()
com.android.rockchip.camera2.video.TpCaptureImage: void release()
com.android.rockchip.camera2.view.TpCustomProgressBar: void setMax(int)
com.android.rockchip.camera2.view.TpImageView: void setPanEnabled(boolean)
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder build()
com.android.rockchip.camera2.video.VideoDecoder: void decodeSingleFrame()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean startScreenStreaming(com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener)
com.android.rockchip.camera2.service.StreamingService: void onTpctrlDetected()
com.android.rockchip.camera2.view.TpVideoPlayerView: void setupVideoSystem()
com.android.rockchip.camera2.video.TpCaptureImage$Builder: com.android.rockchip.camera2.video.TpCaptureImage$Builder setMaxImages(int)
com.android.rockchip.camera2.view.TpVideoPlayerView: TpVideoPlayerView(android.content.Context)
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String createImagePath(android.content.Context)
com.android.rockchip.camera2.service.StreamingService: void switchStreamType()
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec[] values()
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void onVideoPlaybackStopped()
com.android.rockchip.camera2.service.StreamingService: void initStreaming(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.TpCaptureImage,com.android.rockchip.camera2.service.StreamingService$StreamType)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initCaptureHelper$23(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void lambda$onVideoPlaybackStopped$2()
com.android.rockchip.camera2.rtsp.RTSPManager$1: void onPermissionGranted(java.lang.String)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution[] $values()
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void onError(java.lang.String)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: int getWidth()
com.android.rockchip.camera2.video.TpCameraManager: void releaseCamera()
com.android.rockchip.camera2.video.TpCameraManager$Builder: com.android.rockchip.camera2.video.TpCameraManager build()
com.android.rockchip.camera2.view.TpCustomProgressBar: void animateThumbPress(boolean)
com.android.rockchip.camera2.util.touptek_serial_rk: int initSerial(int)
com.android.rockchip.camera2.video.ImageDecoder: void lambda$putBitmapToCache$0(java.lang.String,int,int,android.graphics.Bitmap)
com.android.rockchip.camera2.util.TouptekIspParam: void syncAllCurrentValuesToDevice()
com.android.rockchip.camera2.util.NetworkManager$1: void lambda$onAvailable$0()
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String getFileSystemType(java.lang.String)
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.service.StreamingService$StreamType getCurrentStreamType()
com.android.rockchip.camera2.video.TpVideoSystem: boolean isCurrentVideoPlaybackCompleted()
com.android.rockchip.camera2.video.TpCaptureImage: com.android.rockchip.camera2.video.TpCaptureImage$Builder builder(android.util.Size)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$checkCameraOperationPreconditions$21(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.util.touptek_serial_rk: void stopMonitor()
com.android.rockchip.camera2.view.TpVideoPlayerView: void showControls()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$showSpeedSelectionMenu$12(android.view.View,android.widget.PopupWindow)
com.android.rockchip.camera2.view.TpTextureView: android.view.ScaleGestureDetector createOptimizedScaleGestureDetector(android.content.Context)
com.android.rockchip.camera2.util.touptek_serial_rk: void closeSerial()
com.android.rockchip.camera2.util.TransformUtils: void applyPan(android.view.TextureView,float,float)
com.android.rockchip.camera2.video.TpCaptureImage: void lambda$saveImageAsync$3(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener,java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void hideControls()
com.android.rockchip.camera2.view.TpVideoPlayerView: void nextVideo()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initializeStreamingService$39(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.service.StreamingService: StreamingService(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.StreamingService$HeartbeatListener)
com.android.rockchip.camera2.view.TpImageView: void resetMatrix()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$setupControlListeners$7(android.view.View)
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle[] $values()
com.android.rockchip.camera2.video.TpCaptureImage$1: TpCaptureImage$1(com.android.rockchip.camera2.video.TpCaptureImage)
com.android.rockchip.camera2.service.StreamingService: boolean isManualRtspControl()
com.android.rockchip.camera2.util.SMBFileUploader$UploadListener: void onUploadFailed(java.lang.String)
com.android.rockchip.camera2.util.TouptekIspParam: void setParamDisabled(com.android.rockchip.camera2.util.TouptekIspParam,boolean)
com.android.rockchip.camera2.util.NetworkManager: void disconnectWifi()
com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener: void onDirectoryLoadFailed(java.lang.String)
com.android.rockchip.camera2.view.TpCustomProgressBar: void setProgress(int)
com.android.rockchip.camera2.util.TouptekIspParam: java.util.List getAllParamInfo()
com.android.rockchip.camera2.view.TpImageView: boolean performScale(float,float,float)
com.android.rockchip.camera2.video.TpCaptureImage: TpCaptureImage(com.android.rockchip.camera2.video.TpCaptureImage$Builder)
com.android.rockchip.camera2.video.ImageDecoder: void cleanupCacheIfNeeded()
com.android.rockchip.camera2.view.TpTextureView: void setMaxScale(float)
com.android.rockchip.camera2.service.StreamingService: void configureRtspCallbacks()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initCaptureHelper$25(java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager: void requestScreenCapturePermission()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$showSpeedSelectionMenu$13(float,android.widget.TextView[],android.widget.PopupWindow,android.view.View)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void onVideoDataAvailable(java.nio.ByteBuffer,android.media.MediaCodec$BufferInfo)
com.android.rockchip.camera2.util.NetworkManager: java.util.List getAvailableNetworkInterfaces()
com.android.rockchip.camera2.view.TpRoiView: void init(android.content.Context)
com.android.rockchip.camera2.util.SMBFileUploader: void loadSettings()
com.android.rockchip.camera2.video.TpVideoSystem: boolean isInitialized()
com.android.rockchip.camera2.service.StreamingService: void lambda$configureRtspCallbacks$3()
com.android.rockchip.camera2.video.VideoDecoder: boolean isPaused()
com.android.rockchip.camera2.rtsp.RTSPManager: void handleScreenCaptureResult(androidx.activity.result.ActivityResult)
com.android.rockchip.camera2.video.TvPreviewHelper: void release()
com.android.rockchip.camera2.video.TpVideoConfig: java.lang.String toString()
com.android.rockchip.camera2.video.ImageDecoder: void lambda$loadImageAsync$6(long,java.lang.String,android.widget.ImageView)
com.android.rockchip.camera2.video.TpImageLoader: boolean isVideoFile(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void seekRelative(long)
com.android.rockchip.camera2.service.StreamingService: void lambda$configureRtspCallbacks$4(java.lang.String)
com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener: void onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.view.TpCustomProgressBar: TpCustomProgressBar(android.content.Context,android.util.AttributeSet,int)
com.android.rockchip.camera2.view.TpVideoPlayerView: void initControlsAutoHide()
com.android.rockchip.camera2.util.NetworkManager: void resumeHotspotMonitoring()
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo: java.lang.String getDescription()
com.android.rockchip.camera2.util.touptek_serial_rk: void sendCommand(int,int,int[])
com.android.rockchip.camera2.view.TpImageView: android.view.ScaleGestureDetector createOptimizedScaleGestureDetector(android.content.Context)
com.android.rockchip.camera2.video.VideoEncoder: void requestKeyFrame()
com.android.rockchip.camera2.view.TpTextureView$GestureState: TpTextureView$GestureState(java.lang.String,int)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onImageCaptured(java.lang.String)
com.android.rockchip.camera2.util.SMBFileUploader: void uploadFile(java.lang.String,java.lang.String,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener)
com.android.rockchip.camera2.video.TpImageLoader: void checkAndCleanDiskCache()
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int getPort()
com.android.rockchip.camera2.service.StreamingService$StreamType: StreamingService$StreamType(java.lang.String,int)
com.android.rockchip.camera2.video.TpImageLoader: java.lang.String getFileExtension(java.lang.String)
com.android.rockchip.camera2.view.TpRoiView: TpRoiView(android.content.Context,android.util.AttributeSet)
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.service.StreamingService initCore(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.StreamingService$HeartbeatListener)
com.android.rockchip.camera2.view.TpRoiView: boolean isROIEnabled()
com.android.rockchip.camera2.util.TouptekIspParam: TouptekIspParam(java.lang.String,int,int)
com.android.rockchip.camera2.util.SMBFileUploader: void uploadFile(java.lang.String,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener)
com.android.rockchip.camera2.util.SMBFileUploader: void testConnection(com.android.rockchip.camera2.util.SMBFileUploader$UploadListener)
com.android.rockchip.camera2.view.TpImageView: boolean isZoomEnabled()
com.android.rockchip.camera2.video.VideoEncoder: void startEncodingDecoding()
com.android.rockchip.camera2.rtsp.RTSPManager: boolean requestScreenPermission()
com.android.rockchip.camera2.video.TpImageLoader: boolean loadFullImage(java.lang.String,android.widget.ImageView)
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam getParamByIndex(int)
com.android.rockchip.camera2.view.TpTextureView: void lambda$scheduleResetGestureState$0()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$initControlsAutoHide$0()
com.android.rockchip.camera2.video.TpVideoSystem: void release()
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam[] $values()
com.android.rockchip.camera2.video.ImageDecoder: void initCache(android.content.Context)
com.android.rockchip.camera2.video.VideoEncoder: VideoEncoder()
com.android.rockchip.camera2.video.TpCaptureImage: void saveTiff(android.graphics.Bitmap,java.lang.String)
com.android.rockchip.camera2.video.TpCaptureImage: void lambda$saveImageAsync$2(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener,java.lang.String)
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: void onServiceDisconnected(android.content.ComponentName)
com.android.rockchip.camera2.view.TpVideoPlayerView: void setShowControls(boolean)
com.android.rockchip.camera2.view.TpVideoPlayerView: void updateTimeDisplay(long,long)
com.android.rockchip.camera2.video.TpImageLoader: void lambda$loadFullImage$3(android.graphics.Bitmap)
com.android.rockchip.camera2.video.TpVideoSystem: void stopRecording()
com.android.rockchip.camera2.util.HdmiService: HdmiService()
com.android.rockchip.camera2.util.TouptekIspParam: boolean isRangeReceived()
com.android.rockchip.camera2.view.TpVideoPlayerView: TpVideoPlayerView(android.content.Context,android.util.AttributeSet)
com.android.rockchip.camera2.view.TpTextureView: boolean isPanEnabled()
com.android.rockchip.camera2.service.StreamingService: void startRtspServiceWithRetry()
com.android.rockchip.camera2.view.TpRoiView: void drawControlHandles(android.graphics.Canvas,android.graphics.RectF)
com.android.rockchip.camera2.view.TpTextureView: void setDoubleTapEnabled(boolean)
com.android.rockchip.camera2.util.TouptekIspParam: long getCurrentLongValue(com.android.rockchip.camera2.util.TouptekIspParam)
com.android.rockchip.camera2.rtsp.RTSPManager: void stopStreaming()
com.android.rockchip.camera2.util.TouptekIspParam: int getCurrentValue(com.android.rockchip.camera2.util.TouptekIspParam)
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder$Builder onFileSizeLimitReached(java.lang.Runnable)
com.android.rockchip.camera2.view.TpVideoPlayerView: void updateProgressAndTime()
com.android.rockchip.camera2.service.StreamingService: void lambda$processSocketConnections$13(java.net.Socket)
com.android.rockchip.camera2.view.TpCustomProgressBar: boolean onTouchEvent(android.view.MotionEvent)
com.android.rockchip.camera2.util.touptek_serial_rk: void sendCommandToSerial(int,int,int)
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo: java.lang.String getIpAddress()
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String getUsername()
com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener: void onProgressChanged(com.android.rockchip.camera2.view.TpCustomProgressBar,int,boolean)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onError(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void lambda$onVideoPlaybackStarted$0()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$play$16()
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: void onServiceConnected(android.content.ComponentName,android.os.IBinder)
com.android.rockchip.camera2.view.TpTextureView: void initGestureDetectors(android.content.Context)
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String getShareName()
com.android.rockchip.camera2.util.HdmiService$HdmiListener: void onHdmiStatusChanged(boolean)
com.android.rockchip.camera2.rtsp.service.RTSPService: void onAuthError()
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: void stopEncoding()
com.android.rockchip.camera2.video.TpVideoSystem: void setTvContainer(android.view.ViewGroup)
com.android.rockchip.camera2.service.StreamingSocketService: void onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)
com.android.rockchip.camera2.util.NetworkManager$HotspotInfo: NetworkManager$HotspotInfo(boolean,java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void forceUpdateProgressToEnd(long)
com.android.rockchip.camera2.util.TouptekIspParam: void requestAllParamRanges(boolean)
com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener: void onStreamStopped()
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: boolean isEnabled()
com.android.rockchip.camera2.util.TouptekIspParam: void release()
com.android.rockchip.camera2.rtsp.config.RTSPConfig: java.lang.String getHost()
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String convertToActualPath(java.lang.String)
com.android.rockchip.camera2.util.touptek_serial_rk: void onSerialDataReceived(int[])
com.android.rockchip.camera2.video.TpVideoSystem: java.lang.String getStreamUrl()
com.android.rockchip.camera2.view.TpVideoPlayerView: void startProgressUpdater()
com.android.rockchip.camera2.util.HdmiService: boolean isSignalLocked(java.lang.String)
com.android.rockchip.camera2.view.TpRoiView: void moveRect(float,float)
com.android.rockchip.camera2.service.StreamingService$HeartbeatListener: void onStreamError(java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager setStreamType(com.android.rockchip.camera2.rtsp.RTSPManager$StreamType)
com.android.rockchip.camera2.view.TpImageView: TpImageView(android.content.Context,android.util.AttributeSet)
com.android.rockchip.camera2.rtsp.RTSPManager: void release()
com.android.rockchip.camera2.video.TpCaptureImage$Builder: com.android.rockchip.camera2.video.TpCaptureImage$Builder setCaptureCallback(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener)
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager setVideoEncoder(com.android.rockchip.camera2.video.VideoEncoder)
com.android.rockchip.camera2.rtsp.service.RTSPService: void onCreate()
com.android.rockchip.camera2.video.TpVideoSystem: void loadThumbnail(java.lang.String,android.widget.ImageView)
com.android.rockchip.camera2.service.StreamingService: void onTpctrlLost()
com.android.rockchip.camera2.view.TpImageView: boolean handlePanGesture(android.view.MotionEvent)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void requestKeyFrame()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$26(android.view.Surface,android.hardware.camera2.CameraDevice)
com.android.rockchip.camera2.view.TpVideoPlayerView: void cancelHideControls()
com.android.rockchip.camera2.video.TpVideoSystem$1: void onPlaybackCompleted()
com.android.rockchip.camera2.util.TouptekIspParam: int getMinValue(com.android.rockchip.camera2.util.TouptekIspParam)
com.android.rockchip.camera2.view.TpVideoPlayerView: void scheduleHideControls()
com.android.rockchip.camera2.video.ImageDecoder: java.lang.String generateCacheFileName(java.lang.String,int,int)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$31(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpVideoConfig: int getFrameRate()
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec[] $values()
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: SMBFileUploader$SMBConfig(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$33(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.util.TouptekIspParam$ParamData: java.lang.String toString()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$updateProgressAndTime$19()
com.android.rockchip.camera2.util.SMBFileUploader: void saveSettings()
com.android.rockchip.camera2.view.TpVideoPlayerView$1: boolean onScaleGestureDetected(android.view.MotionEvent)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void tryGetFormatFromEncoder()
com.android.rockchip.camera2.video.TpVideoConfig$Builder: com.android.rockchip.camera2.video.TpVideoConfig$Builder setBitRate(int)
com.android.rockchip.camera2.rtsp.service.RTSPService: void onDisconnect()
com.android.rockchip.camera2.video.VideoDecoder: void resetToStart()
com.android.rockchip.camera2.video.TpCaptureImage: void setCaptureCallback(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener)
com.android.rockchip.camera2.video.TpCameraManager$1: void onDisconnected(android.hardware.camera2.CameraDevice)
com.android.rockchip.camera2.video.VideoEncoder: void notifySurfaceAvailable(android.view.Surface)
com.android.rockchip.camera2.video.TpCameraManager: void configCameraOutputs(android.hardware.camera2.CameraDevice,android.view.Surface,android.view.Surface)
com.android.rockchip.camera2.video.VideoDecoder: void setPlaybackSpeed(float)
com.android.rockchip.camera2.service.StreamingSocketService: void lambda$sendIspParameterToTpctrl$2(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.video.ImageDecoder: void lambda$loadImageAsync$4(java.lang.String,android.widget.ImageView,android.graphics.Bitmap,long)
com.android.rockchip.camera2.video.TpVideoSystem$1: void lambda$onPlaybackCompleted$0(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: int getValue()
com.android.rockchip.camera2.view.TpRoiView: void setROIEnabled(boolean)
com.android.rockchip.camera2.view.TpRoiView: void applyMirrorFlipToROI()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void notifyStreamError(java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem: boolean isRecording()
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TvPreviewHelper getTvPreviewHelper()
com.android.rockchip.camera2.view.TpRoiView: void lambda$animateRectAppear$2(float,android.graphics.RectF,float,android.animation.ValueAnimator)
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean isControlsVisible()
com.android.rockchip.camera2.video.VideoEncoder: android.media.MediaFormat createMediaFormatFromTpVideoConfig(com.android.rockchip.camera2.video.TpVideoConfig,android.util.Size)
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.VideoDecoder getVideoDecoder()
com.android.rockchip.camera2.util.HdmiService: java.lang.String readHdmiStatusFile()
com.android.rockchip.camera2.service.StreamingSocketService: void lambda$captureAndSendImage$1(android.util.Size,java.util.concurrent.atomic.AtomicBoolean,java.util.concurrent.atomic.AtomicBoolean)
com.android.rockchip.camera2.util.TouptekIspParam: void setOnSerialStateChangedListener(com.android.rockchip.camera2.util.TouptekIspParam$OnSerialStateChangedListener)
com.android.rockchip.camera2.video.VideoDecoder: boolean isPlaybackCompleted()
com.android.rockchip.camera2.util.TouptekIspParam: void sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int,int)
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: RTSPServiceConnection(android.content.Context,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection$UrlCallback,java.lang.Runnable)
com.android.rockchip.camera2.view.TpVideoPlayerView: void updatePlayPauseButtonIcon(boolean)
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder setFrameRate(int)
com.android.rockchip.camera2.view.TpTextureView: boolean onTouchEvent(android.view.MotionEvent)
com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo: java.lang.String getSsid()
com.android.rockchip.camera2.video.TpVideoSystem: void startTvPreview()
com.android.rockchip.camera2.video.TpVideoSystem: void initStreamingServiceEarly()
com.android.rockchip.camera2.view.TpRoiView: void applyTransform(android.graphics.Matrix)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$35(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.view.TpVideoPlayerView: void play()
com.android.rockchip.camera2.view.TpVideoPlayerView$1: TpVideoPlayerView$1(com.android.rockchip.camera2.view.TpVideoPlayerView)
com.android.rockchip.camera2.view.TpVideoPlayerView: com.android.rockchip.camera2.view.TpTextureView getTextureView()
com.android.rockchip.camera2.service.StreamingService: void lambda$configureRtspCallbacks$1(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: long getCurrentPosition()
com.android.rockchip.camera2.rtsp.service.ProjectionData: android.content.Intent getData()
com.android.rockchip.video.R: R()
com.android.rockchip.camera2.video.TpVideoSystem: boolean resetCurrentVideoToStart()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$playVideo$14(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager onPermissionGranted(java.util.function.Consumer)
com.android.rockchip.camera2.rtsp.RTSPManager$1: void lambda$onStreamStarted$0(java.lang.String)
com.android.rockchip.camera2.view.TpTextureView: boolean performTranslate(float,float)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: RTSPConfig$Builder()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$checkCameraOperationPreconditions$20(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpCaptureImage: byte[] intToBytes(int)
com.android.rockchip.camera2.view.TpVideoPlayerView: void initComponents()
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder builder(android.content.Context,android.media.projection.MediaProjection)
com.android.rockchip.camera2.util.TouptekIspParam: void notifyDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.view.TpTextureView: void setTouchEventHandler(com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler)
com.android.rockchip.camera2.service.StreamingSocketService: boolean isRunning()
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean lambda$setupControlsInteractionDetection$9(android.view.View,android.view.MotionEvent)
com.android.rockchip.camera2.video.VideoEncoder: void notifyFileSizeLimitReached()
com.android.rockchip.camera2.video.ImageDecoder: void cleanupCache()
com.android.rockchip.camera2.util.touptek_serial_rk: void lambda$onDeviceStateChanged$0()
com.android.rockchip.camera2.view.TpCustomProgressBar: void lambda$animateThumbPress$0(float,float,float,float,float,float,android.animation.ValueAnimator)
com.android.rockchip.camera2.video.VideoEncoder: java.lang.String mapVideoCodecToMimeType(com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$showSpeedSelectionMenu$11(android.widget.PopupWindow)
com.android.rockchip.camera2.video.TpVideoSystem: void startCameraPreview()
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: TpVideoConfig$BitrateMode(java.lang.String,int,int)
com.android.rockchip.camera2.view.TpVideoPlayerView: int getSpeedIndex(float)
com.android.rockchip.camera2.video.TpImageLoader: void loadThumbnail(java.lang.String,android.widget.ImageView)
com.android.rockchip.camera2.util.TouptekIspParam: int getParamId()
com.android.rockchip.camera2.video.TpVideoSystem: void loadFullImage(java.lang.String,android.widget.ImageView)
com.android.rockchip.camera2.video.TpVideoConfig: int getBitRate()
com.android.rockchip.camera2.view.TpVideoPlayerView: void toggleControlsVisibility()
com.android.rockchip.camera2.video.VideoEncoder: void setOutputCallback(com.android.rockchip.camera2.video.VideoEncoder$VideoDataOutputCallback)
com.android.rockchip.camera2.video.ImageDecoder: android.graphics.Bitmap getBitmapFromCache(android.content.Context,java.lang.String,int,int)
com.android.rockchip.camera2.service.StreamingService: void initRtspManager()
com.android.rockchip.camera2.view.TpVideoPlayerView$2: TpVideoPlayerView$2(com.android.rockchip.camera2.view.TpVideoPlayerView)
com.android.rockchip.camera2.util.touptek_serial_rk: boolean isSerialConnected()
com.android.rockchip.camera2.view.TpVideoPlayerView: void initFromAttributes(android.util.AttributeSet)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void lambda$startScreenStream$0(java.lang.String)
com.android.rockchip.camera2.video.TvPreviewHelper: boolean isActive()
com.android.rockchip.camera2.rtsp.service.RTSPService: void onConnectionFailed(java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager getInstance()
com.android.rockchip.camera2.video.TpCaptureImage: java.nio.ByteBuffer copyImageToBuffer(android.media.Image)
com.android.rockchip.camera2.video.TvPreviewHelper: TvPreviewHelper(android.content.Context,android.view.ViewGroup)
com.android.rockchip.camera2.video.TpImageLoader: void lambda$saveToDiskCache$7(java.lang.String,android.graphics.Bitmap)
com.android.rockchip.camera2.service.StreamingSocketService: void sendJpegData(java.io.OutputStream,int,int,byte[])
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager setNetworkInterface(java.lang.String)
com.android.rockchip.camera2.video.TpCameraManager: boolean resetHdmiRxViaScript()
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder$Builder onSaveComplete(java.util.function.Consumer)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initCaptureHelper$22(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setKeyFrameInterval(int)
com.android.rockchip.camera2.rtsp.config.RTSPConfig: RTSPConfig(int,int,int,int,int,boolean,int)
com.android.rockchip.camera2.view.TpRoiView: com.android.rockchip.camera2.view.TpRoiView$ControlHandle getHandleAtPoint(float,float)
com.android.rockchip.camera2.video.TpVideoConfig: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec getCodec()
com.android.rockchip.camera2.rtsp.service.RTSPService$RTSPBinder: java.lang.String getRtspUrl()
com.android.rockchip.camera2.video.VideoDecoder: boolean isDecoding()
com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener: void onWifiStateChanged(boolean,java.lang.String)
com.android.rockchip.camera2.video.VideoEncoder: void stopRecording()
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager onStreamStopped(java.lang.Runnable)
com.android.rockchip.camera2.service.StreamingService: void onHeartbeatReceived()
com.android.rockchip.camera2.util.SMBFileUploader$UploadListener: void onUploadSuccess(java.lang.String)
com.android.rockchip.camera2.video.TpCaptureImage$Builder$1: void onError(java.lang.String)
com.android.rockchip.camera2.util.TouptekIspParam: void updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.video.TpVideoSystem: void initTvPreviewHelperIfNeeded()
com.android.rockchip.camera2.video.TpVideoSystem: boolean startStreaming()
com.android.rockchip.camera2.rtsp.RTSPManager$1: void lambda$onPermissionGranted$3(java.lang.String)
com.android.rockchip.camera2.view.TpRoiView: boolean containsPoint(android.graphics.RectF,float,float)
com.android.rockchip.camera2.service.StreamingService: boolean isRunning()
com.android.rockchip.camera2.util.NetworkManager: void openHotspotSettings()
com.android.rockchip.camera2.rtsp.service.RTSPService: void onNewBitrate(long)
com.android.rockchip.camera2.service.StreamingService: void lambda$startImageSocketService$14(java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$27(android.hardware.camera2.CameraDevice)
com.android.rockchip.camera2.video.TpVideoSystem: void notifyListener(com.android.rockchip.camera2.video.TpVideoSystem$ListenerAction)
com.android.rockchip.camera2.rtsp.RTSPManager: boolean startStreaming()
com.android.rockchip.camera2.video.TvPreviewHelper: void startPreview()
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType[] $values()
com.android.rockchip.camera2.video.TpVideoSystem: boolean stopStreaming()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$34(java.lang.String,java.lang.Exception)
com.android.rockchip.camera2.video.TpCameraManager: TpCameraManager(com.android.rockchip.camera2.video.TpCameraManager$Builder)
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig: java.lang.String getRemotePath()
com.android.rockchip.camera2.video.TpVideoSystem: TpVideoSystem(androidx.appcompat.app.AppCompatActivity)
com.android.rockchip.camera2.view.TpVideoPlayerView$2: void onStopTrackingTouch(com.android.rockchip.camera2.view.TpCustomProgressBar)
com.android.rockchip.camera2.video.VideoDecoder: void startDecoding()
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution valueOf(java.lang.String)
com.android.rockchip.camera2.service.StreamingService: void configureVideoEncoder()
com.android.rockchip.camera2.video.TpVideoSystem: void resumeVideo()
com.android.rockchip.camera2.video.TpVideoSystem: void stopTvPreview()
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemAdapter: void onError(java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem$StreamType: com.android.rockchip.camera2.video.TpVideoSystem$StreamType[] $values()
com.android.rockchip.camera2.video.TpVideoSystem: boolean updateResolution(int,int)
com.android.rockchip.camera2.view.TpImageView: void lambda$scheduleResetGestureState$0()
com.android.rockchip.camera2.view.TpVideoPlayerView: void setupTouchEventHandler()
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle valueOf(java.lang.String)
com.android.rockchip.camera2.video.TpCaptureImage: void onImageAvailable(android.media.ImageReader)
com.android.rockchip.camera2.service.StreamingService: boolean startRtspManually(com.android.rockchip.camera2.service.StreamingService$StreamType,java.lang.String)
com.android.rockchip.camera2.rtsp.service.ProjectionData: ProjectionData(int,android.content.Intent)
com.android.rockchip.camera2.video.TpVideoSystem$1: TpVideoSystem$1(com.android.rockchip.camera2.video.TpVideoSystem,java.lang.String)
com.android.rockchip.camera2.video.VideoEncoder$1: void handleFileSizeLimit()
com.android.rockchip.camera2.view.TpTextureView$GestureState: com.android.rockchip.camera2.view.TpTextureView$GestureState valueOf(java.lang.String)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void checkFormatTimeout()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$updatePlayPauseButtonIcon$10(android.widget.ImageButton,int)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$loadThumbnail$6(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void onVideoPlaybackCompleted(java.lang.String)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void stopScreenStream()
com.android.rockchip.camera2.video.TpVideoConfig$Builder: int calculateDefaultBitRate(int,int,int)
com.android.rockchip.camera2.util.touptek_serial_rk: int startMonitor()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$playVideo$17(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpVideoConfig: com.android.rockchip.camera2.video.TpVideoConfig createDefault4K()
com.android.rockchip.camera2.util.SMBFileUploader: void getRemoteDirectories(com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener)
com.android.rockchip.camera2.video.VideoDecoder: float getPlaybackSpeed()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean switchToScreenStream(com.android.rockchip.camera2.rtsp.service.ProjectionData)
com.android.rockchip.camera2.view.TpRoiView: void notifyROIChanged()
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int getFrameRate()
com.android.rockchip.camera2.util.TransformUtils: void applyZoom(android.widget.ImageView,android.graphics.Matrix,float,float,float)
com.android.rockchip.camera2.video.TpVideoSystem: void switchToTvMode()
com.android.rockchip.camera2.util.TouptekIspParam$ParamData: TouptekIspParam$ParamData(int,int,int,int,boolean)
com.android.rockchip.camera2.video.TpImageLoader: java.lang.String getCacheStats()
com.android.rockchip.camera2.video.TpVideoSystem: boolean startStreaming(com.android.rockchip.camera2.video.TpVideoSystem$StreamType)
com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener: void onStopTrackingTouch(com.android.rockchip.camera2.view.TpCustomProgressBar)
com.android.rockchip.camera2.video.TpCaptureImage: int detectFormatFromPath(java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager$1: void lambda$onStreamStopped$1()
com.android.rockchip.camera2.video.TpCaptureImage$Builder: com.android.rockchip.camera2.video.TpCaptureImage build()
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam[] values()
com.android.rockchip.camera2.video.TpCaptureImage: android.media.ImageReader getImageReader()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initialize$0(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpImageLoader: int lambda$checkAndCleanDiskCache$8(java.io.File,java.io.File)
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int getWidth()
com.android.rockchip.camera2.view.TpVideoPlayerView: com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener getVideoPlayerListener()
com.android.rockchip.camera2.video.VideoDecoder: void decodingTask()
com.android.rockchip.camera2.video.VideoEncoder: void initialize(android.util.Size,android.view.Surface)
com.android.rockchip.camera2.view.TpVideoPlayerView: void setVideoPath(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void stopProgressUpdater()
com.android.rockchip.camera2.service.StreamingService$StreamType: com.android.rockchip.camera2.service.StreamingService$StreamType[] values()
com.android.rockchip.camera2.video.ImageDecoder: void lambda$loadImageAsync$5(java.lang.String,android.widget.ImageView,android.graphics.Bitmap,long)
com.android.rockchip.camera2.view.TpTextureView: void setRoiView(com.android.rockchip.camera2.view.TpRoiView)
com.android.rockchip.camera2.video.ImageDecoder: ImageDecoder()
com.android.rockchip.camera2.service.StreamingSocketService$LogListener: void onLogMessage(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void stepFrame()
com.android.rockchip.camera2.view.TpImageView: TpImageView(android.content.Context,android.util.AttributeSet,int)
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode[] $values()
com.android.rockchip.camera2.view.TpTextureView: TpTextureView(android.content.Context,android.util.AttributeSet)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onStreamingStatusChanged(boolean,java.lang.String)
com.android.rockchip.camera2.view.TpTextureView: float getCurrentScale()
com.android.rockchip.camera2.view.TpRoiView: void lambda$updateROIFromParams$0()
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo: NetworkManager$NetworkInterfaceInfo(java.lang.String,java.lang.String,java.lang.String)
com.android.rockchip.camera2.view.TpImageView: void cancelResetGestureState()
com.android.rockchip.camera2.service.StreamingService: java.lang.String getStreamUrl()
com.android.rockchip.camera2.video.VideoDecoder: void safeSleep(long)
com.android.rockchip.camera2.util.TouptekIspParam: int saveAllDefaultValuesToLocal(boolean)
com.android.rockchip.camera2.view.TpRoiView: void lambda$notifyROIChanged$4()
com.android.rockchip.camera2.video.TpVideoConfig$Builder: com.android.rockchip.camera2.video.TpVideoConfig$Builder setKeyFrameInterval(int)
com.android.rockchip.camera2.view.TpVideoPlayerView: void updateSpeedSelectionDisplay(android.widget.TextView[],float[],java.lang.String[])
com.android.rockchip.camera2.view.TpRoiView: void updateROIFromParams(int,int,int,int)
com.android.rockchip.video.R$attr: R$attr()
com.android.rockchip.camera2.video.TpVideoSystem: boolean stepCurrentVideoFrame()
com.android.rockchip.camera2.video.TpVideoSystem: void setListener(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.util.TouptekIspParam: int getDefaultValue(com.android.rockchip.camera2.util.TouptekIspParam)
com.android.rockchip.camera2.service.StreamingSocketService: void sendIspParameterToTpctrl(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.view.TpImageView$GestureState: TpImageView$GestureState(java.lang.String,int)
com.android.rockchip.camera2.service.StreamingService: void stopService()
com.android.rockchip.camera2.video.TpCaptureImage: void lambda$saveImageAsync$4(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener)
com.android.rockchip.camera2.view.TpCustomProgressBar: int getMax()
com.android.rockchip.camera2.rtsp.service.RTSPService$RTSPBinder: com.android.rockchip.camera2.rtsp.service.RTSPService getService()
com.android.rockchip.camera2.view.TpRoiView: boolean onTouchEvent(android.view.MotionEvent)
com.android.rockchip.camera2.util.TouptekIspParam: boolean isSerialConnected()
com.android.rockchip.camera2.video.TpImageLoader: void saveToDiskCache(java.lang.String,android.graphics.Bitmap)
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void lambda$onError$4()
com.android.rockchip.camera2.service.StreamingService: void lambda$configureRtspCallbacks$5(java.lang.String)
com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener: void onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo: java.lang.String toString()
com.android.rockchip.camera2.view.TpTextureView$OnZoomChangeListener: void onZoomChanged(float,float,float)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setVideoBitrate(int)
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.service.StreamingService getStreamingService()
com.android.rockchip.camera2.util.FileStorageUtils$StorageListener: void onUsbDriveDisconnected(java.lang.String)
com.android.rockchip.camera2.service.StreamingSocketService: void captureAndSendImage(java.io.OutputStream,boolean)
com.android.rockchip.camera2.rtsp.service.RTSPService: void onAuthSuccess()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType getStreamType()
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution[] values()
com.android.rockchip.camera2.rtsp.RTSPManager: RTSPManager()
com.android.rockchip.camera2.video.VideoDecoder: void stepFrame()
com.android.rockchip.camera2.view.TpVideoPlayerView: void setupControlsInteractionDetection()
com.android.rockchip.camera2.video.TpVideoSystem: long getVideoDuration()
com.android.rockchip.camera2.util.NetworkManager$1: void lambda$onCapabilitiesChanged$2()
com.android.rockchip.camera2.service.StreamingService: void startSubServices()
com.android.rockchip.camera2.view.TpImageView: void setZoomEnabled(boolean)
com.android.rockchip.camera2.video.TpVideoSystem: boolean isVideoPlaying()
com.android.rockchip.camera2.view.TpTextureView: boolean isZoomEnabled()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$startRecording$2(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.service.StreamingService: void stopSocketServer()
com.android.rockchip.camera2.video.TpVideoConfig: int getWidth()
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.VideoEncoder getVideoEncoder()
com.android.rockchip.camera2.video.TpCaptureImage: android.graphics.Bitmap convertYUVToBitmap(java.nio.ByteBuffer,android.util.Size,android.util.Size)
com.android.rockchip.camera2.video.TpVideoSystem: void initialize(android.view.Surface)
com.android.rockchip.camera2.service.StreamingService$StreamType: com.android.rockchip.camera2.service.StreamingService$StreamType valueOf(java.lang.String)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: java.lang.String getRtspUrl()
com.android.rockchip.camera2.service.StreamingService$StreamType: com.android.rockchip.camera2.service.StreamingService$StreamType[] $values()
com.android.rockchip.camera2.view.TpVideoPlayerView: void recreateSurface()
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: boolean bind()
com.android.rockchip.camera2.util.HdmiService: void stop()
com.android.rockchip.camera2.video.TpImageLoader: android.graphics.Bitmap loadFromDiskCache(java.lang.String)
com.android.rockchip.camera2.service.StreamingService: boolean killTpctrlProcess()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void monitorClientState()
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType[] values()
com.android.rockchip.camera2.service.StreamingService: void startImageSocketService()
com.android.rockchip.camera2.video.TpCaptureImage$Builder$1: TpCaptureImage$Builder$1(com.android.rockchip.camera2.video.TpCaptureImage$Builder)
com.android.rockchip.camera2.video.TpVideoSystem: void updateVideoConfig(com.android.rockchip.camera2.video.TpVideoConfig)
com.android.rockchip.camera2.video.TpVideoSystem: void stopCameraPreview()
com.android.rockchip.camera2.video.ImageDecoder: void clearCache()
com.android.rockchip.camera2.service.StreamingService: void lambda$updateVideoEncoder$11(com.android.rockchip.camera2.video.VideoEncoder)
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: void release()
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder setKeyFrameInterval(int)
com.android.rockchip.camera2.video.TpVideoConfig: TpVideoConfig(com.android.rockchip.camera2.video.TpVideoConfig$Builder)
com.android.rockchip.camera2.view.TpTextureView: void cancelResetGestureState()
com.android.rockchip.camera2.video.TpCaptureImage$Builder: TpCaptureImage$Builder(android.util.Size)
com.android.rockchip.camera2.service.StreamingService: boolean isServicesRunning()
com.android.rockchip.camera2.util.TouptekIspParam: void setParamRange(com.android.rockchip.camera2.util.TouptekIspParam,boolean,int,int,int)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$releaseVideo$18(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder: com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder recordMicrophone(boolean)
com.android.rockchip.camera2.video.VideoEncoder: void lambda$stopRecording$2(java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager: void initStreamer()
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType getCurrentStreamType()
com.android.rockchip.camera2.util.NetworkManager: void pauseHotspotMonitoring()
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TpVideoSystem$StreamType getCurrentStreamType()
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String getInternalStoragePath(android.content.Context)
com.android.rockchip.camera2.video.TpVideoSystem: boolean seekCurrentVideoRelative(long)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void notifyStreamStopped()
com.android.rockchip.camera2.view.TpImageView: void setOnZoomChangeListener(com.android.rockchip.camera2.view.TpImageView$OnZoomChangeListener)
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType: com.android.rockchip.camera2.rtsp.RTSPManager$StreamType valueOf(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void seekTo(long)
com.android.rockchip.camera2.service.StreamingService: void lambda$startTpctrlConsole$8()
com.android.rockchip.camera2.video.ImageDecoder: int calculateInSampleSize(android.graphics.BitmapFactory$Options,int,int)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setRecordMic(boolean)
com.android.rockchip.camera2.util.touptek_serial_rk: boolean initializeSerial(int)
com.android.rockchip.camera2.view.TpImageView: boolean isPanEnabled()
com.android.rockchip.camera2.video.VideoDecoder: VideoDecoder(java.lang.String,android.view.Surface)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onVideoPlaybackStarted(java.lang.String)
com.android.rockchip.camera2.video.TpVideoConfig$Builder: com.android.rockchip.camera2.video.TpVideoConfig$Builder setCodec(com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec)
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec valueOf(java.lang.String)
com.android.rockchip.camera2.util.NetworkManager$HotspotInfo: boolean isEnabled()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void lambda$notifyStreamStopped$2()
com.android.rockchip.camera2.video.TpImageLoader: void lambda$loadThumbnail$1(java.lang.String,android.widget.ImageView,android.graphics.Bitmap,java.lang.String)
com.android.rockchip.camera2.video.TpCameraManager$1: void onError(android.hardware.camera2.CameraDevice,int)
com.android.rockchip.camera2.rtsp.service.RTSPService: android.os.IBinder onBind(android.content.Intent)
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int getHeight()
com.android.rockchip.camera2.service.StreamingService: void lambda$configureRtspCallbacks$0(java.lang.String)
com.android.rockchip.camera2.rtsp.service.RTSPService: boolean onUnbind(android.content.Intent)
com.android.rockchip.camera2.view.TpRoiView: void onDraw(android.graphics.Canvas)
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder: AudioEncoder$Builder(android.content.Context,android.media.projection.MediaProjection)
com.android.rockchip.camera2.view.TpRoiView: void setCameraResolution(int,int)
com.android.rockchip.camera2.view.TpCustomProgressBar: TpCustomProgressBar(android.content.Context)
com.android.rockchip.camera2.video.VideoEncoder: void notifyError(java.lang.String,java.lang.Exception)
com.android.rockchip.camera2.view.TpRoiView: void onDetachedFromWindow()
com.android.rockchip.camera2.util.TouptekIspParam$OnSerialStateChangedListener: void onSerialStateChanged(boolean)
com.android.rockchip.camera2.video.VideoEncoder$Builder: VideoEncoder$Builder()
com.android.rockchip.camera2.util.TransformUtils: void applyPan(android.widget.ImageView,android.graphics.Matrix,float,float)
com.android.rockchip.camera2.video.TpImageLoader: java.lang.String generateCacheKey(java.lang.String,int,int)
com.android.rockchip.camera2.view.TpRoiView: boolean loadROIParamsFromIsp()
com.android.rockchip.camera2.video.TpImageLoader: void lambda$loadThumbnail$0(android.graphics.Bitmap)
com.android.rockchip.camera2.video.VideoDecoder: void setPlaybackListener(com.android.rockchip.camera2.video.VideoDecoder$VideoDecoderListener)
com.android.rockchip.video.R$color: R$color()
com.android.rockchip.camera2.video.VideoEncoder: void release()
com.android.rockchip.camera2.view.TpVideoPlayerView: void previousVideo()
com.android.rockchip.camera2.video.TpCaptureImage$Builder$1: void onImageSaved(java.lang.String)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setResolution(com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution)
com.android.rockchip.camera2.view.TpTextureView: void scheduleResetGestureState()
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection$UrlCallback: void onUrl(java.lang.String)
com.android.rockchip.camera2.video.TpVideoConfig: com.android.rockchip.camera2.video.TpVideoConfig createDefault1080P()
com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener: void onStreamStarted(java.lang.String)
com.android.rockchip.camera2.util.SMBFileUploader: boolean isEnabled()
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: java.lang.String getMimeType()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$playVideo$16(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.service.StreamingService$HeartbeatListener: void onStreamStatusChanged(boolean,java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem: boolean startStreaming(com.android.rockchip.camera2.video.TpVideoSystem$StreamType,java.lang.String)
com.android.rockchip.camera2.video.TpCaptureImage: void requestCapture(android.util.Size,java.lang.String,int)
com.android.rockchip.camera2.view.TpVideoPlayerView: void startStatusSync()
com.android.rockchip.camera2.util.FileStorageUtils$StorageListener: void onUsbDriveConnected(java.lang.String)
com.android.rockchip.camera2.video.VideoEncoder: void lambda$stopRecording$1(java.lang.Exception)
com.android.rockchip.camera2.video.TpVideoConfig$Builder: com.android.rockchip.camera2.video.TpVideoConfig build()
com.android.rockchip.camera2.util.TouptekIspParam: void requestAllParamRanges()
com.android.rockchip.camera2.service.StreamingService: boolean isTpctrlRunning()
com.android.rockchip.camera2.view.TpVideoPlayerView: float getPlaybackSpeed()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void resetCameraStreamState()
com.android.rockchip.camera2.video.TpVideoSystem: boolean isTvMode()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean switchToCameraStream(com.android.rockchip.camera2.video.VideoEncoder)
com.android.rockchip.camera2.util.TouptekIspParam: void setParamsRangeReceived(boolean)
com.android.rockchip.camera2.video.TpVideoConfig: int getKeyFrameInterval()
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean isPlaying()
com.android.rockchip.camera2.video.TpImageLoader: void clearCache()
com.android.rockchip.camera2.rtsp.service.RTSPService: java.lang.String startStreaming(com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.service.ProjectionData)
com.android.rockchip.camera2.view.TpRoiView: void resizeRectByHandle(float,float)
com.android.rockchip.camera2.view.TpRoiView: void resetTransforms()
com.android.rockchip.camera2.util.NetworkManager: void setupHotspotMonitoring()
com.android.rockchip.camera2.view.TpImageView: void initGestureDetectors(android.content.Context)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$captureImage$4(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.view.TpTextureView: boolean handlePanGesture(android.view.MotionEvent)
com.android.rockchip.camera2.video.VideoDecoder: void lambda$stepFrame$0()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$30(android.view.Surface)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean startScreenStream()
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String createVideoPath(android.content.Context)
com.android.rockchip.camera2.service.StreamingService: void sendAllIspConfigs()
com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener: void onHotspotStateChanged(boolean,java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onVideoPlaybackCompleted(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener: void onNextVideo()
com.android.rockchip.camera2.video.TpVideoSystem: void seekVideoTo(long)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setPort(int)
com.android.rockchip.camera2.view.TpVideoPlayerView: TpVideoPlayerView(android.content.Context,android.util.AttributeSet,int)
com.android.rockchip.camera2.util.NetworkManager$HotspotInfo: java.lang.String getInfo()
com.android.rockchip.camera2.util.NetworkManager: void startMonitoring()
com.android.rockchip.camera2.view.TpVideoPlayerView: void setPlaybackSpeed(float)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$startStreaming$9(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$loadThumbnail$5(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.view.TpImageView: void onSizeChanged(int,int,int,int)
com.android.rockchip.camera2.rtsp.service.RTSPService: RTSPService()
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: void release()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$28(java.lang.Integer,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.ImageDecoder: android.graphics.Bitmap rotateBitmap(android.graphics.Bitmap,float)
com.android.rockchip.camera2.video.TpCaptureImage: byte[] convertYUV420ToNV21(java.nio.ByteBuffer,int,int)
com.android.rockchip.camera2.video.TpVideoSystem$2: TpVideoSystem$2(com.android.rockchip.camera2.video.TpVideoSystem)
com.android.rockchip.camera2.view.TpCustomProgressBar: int getProgress()
com.android.rockchip.camera2.util.NetworkManager: java.lang.String getInterfaceTypeDescription(java.lang.String)
com.android.rockchip.camera2.video.TpCameraManager: void stopBackgroundThread()
com.android.rockchip.camera2.video.ImageDecoder: android.graphics.Bitmap decodeSampledBitmapFromFile(java.lang.String,int,int)
com.android.rockchip.camera2.video.TpCameraManager: void startBackgroundThread()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$pause$17()
com.android.rockchip.camera2.view.TpTextureView: void setZoomEnabled(boolean)
com.android.rockchip.camera2.service.StreamingService: void updateVideoEncoder(com.android.rockchip.camera2.video.VideoEncoder)
com.android.rockchip.camera2.view.TpVideoPlayerView$2: void onStartTrackingTouch(com.android.rockchip.camera2.view.TpCustomProgressBar)
com.android.rockchip.camera2.service.StreamingSocketService: void handleIspParamCommand(java.io.InputStream)
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder build()
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: TpRoiView$ControlHandle(java.lang.String,int)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void stopStream()
com.android.rockchip.camera2.rtsp.config.RTSPConfig: void setHost(java.lang.String)
com.android.rockchip.camera2.view.TpCustomProgressBar: void setSecondaryProgress(int)
com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler: boolean onSingleTapDetected(android.view.MotionEvent)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$playVideo$15(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.VideoEncoder: void startStorageMonitoring()
com.android.rockchip.camera2.util.NetworkManager: void toggleWifi(boolean)
com.android.rockchip.camera2.view.TpTextureView: void setPanEnabled(boolean)
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder builder(android.media.projection.MediaProjection)
com.android.rockchip.camera2.view.TpRoiView: void onSizeChanged(int,int,int,int)
com.android.rockchip.camera2.video.TpCaptureImage$Builder: com.android.rockchip.camera2.video.TpCaptureImage$Builder onError(java.util.function.Consumer)
com.android.rockchip.camera2.view.TpVideoPlayerView: void showSpeedSelectionMenu()
com.android.rockchip.camera2.util.FileStorageUtils: FileStorageUtils()
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void onVideoPlaybackStarted(java.lang.String)
com.android.rockchip.camera2.video.VideoEncoder: void lambda$notifySaveComplete$0(java.lang.String)
com.android.rockchip.camera2.video.TpCaptureImage: void lambda$onImageAvailable$0(java.nio.ByteBuffer,android.util.Size)
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: void setupAudioRecord()
com.android.rockchip.camera2.video.TpVideoSystem: void initCaptureHelper()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean isStreaming()
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: void startEncoding(com.pedro.rtspserver.RtspServer,com.android.rockchip.camera2.rtsp.encoder.AudioEncoder)
com.android.rockchip.camera2.service.StreamingSocketService$1: void onError(java.lang.String)
com.android.rockchip.camera2.video.ImageDecoder: void updateFileInfo(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView$1: boolean onPanGestureDetected(android.view.MotionEvent)
com.android.rockchip.camera2.view.TpCustomProgressBar: void onMeasure(int,int)
com.android.rockchip.camera2.video.TpVideoConfig: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode getBitrateMode()
com.android.rockchip.camera2.view.TpImageView: float getMaxScale()
com.android.rockchip.camera2.video.TpVideoSystem: boolean playVideo(java.lang.String,android.view.Surface)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$38(java.lang.String)
com.android.rockchip.camera2.view.TpImageView: float getFitScreenScale()
com.android.rockchip.camera2.rtsp.config.RTSPConfig: com.android.rockchip.camera2.rtsp.config.RTSPConfig createDefaultConfig()
com.android.rockchip.camera2.video.TpImageLoader: android.graphics.Bitmap rotateBitmap(android.graphics.Bitmap,float)
com.android.rockchip.camera2.video.VideoDecoder: long getVideoDuration()
com.android.rockchip.camera2.util.touptek_serial_rk: void close()
com.android.rockchip.camera2.service.StreamingSocketService: void setLogListener(com.android.rockchip.camera2.service.StreamingSocketService$LogListener)
com.android.rockchip.camera2.service.StreamingSocketService: void start()
com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler: void onLongPressDetected(android.view.MotionEvent)
com.android.rockchip.camera2.video.ImageDecoder: long calculateCacheSize()
com.android.rockchip.camera2.util.TouptekIspParam: void addOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener)
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: void encodeAudio(com.pedro.rtspserver.RtspServer,long)
com.android.rockchip.camera2.rtsp.RTSPManager: java.lang.String getStreamUrl()
com.android.rockchip.camera2.rtsp.service.ProjectionData: int getResultCode()
com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler: boolean onScaleGestureDetected(android.view.MotionEvent)
com.android.rockchip.camera2.service.StreamingService: void lambda$startTpctrlConsole$7(java.lang.Process)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$loadFullImage$8(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.util.TouptekIspParam: void setParamMinValue(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.view.TpRoiView: void onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)
com.android.rockchip.camera2.util.HdmiService: com.android.rockchip.camera2.util.HdmiService getInstance()
com.android.rockchip.camera2.util.SMBFileUploader: void setConnectionParams(com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig)
com.android.rockchip.camera2.video.ImageDecoder: boolean loadImage(java.lang.String,android.widget.ImageView)
com.android.rockchip.camera2.service.StreamingService: void lambda$updateVideoEncoder$10(com.android.rockchip.camera2.service.StreamingService$StreamType,com.android.rockchip.camera2.video.VideoEncoder)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onVideoPlaybackStopped()
com.android.rockchip.camera2.service.StreamingService: void lambda$configureRtspCallbacks$6(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView$6: void lambda$onError$3()
com.android.rockchip.camera2.video.TpVideoSystem$StreamingOperation: boolean execute()
com.android.rockchip.camera2.view.TpVideoPlayerView$1: boolean onSingleTapDetected(android.view.MotionEvent)
com.android.rockchip.camera2.video.TpVideoSystem: void captureImage(java.lang.String)
com.android.rockchip.camera2.view.TpCustomProgressBar: int getSecondaryProgress()
com.android.rockchip.camera2.rtsp.RTSPManager: java.lang.String getIpAddressForInterface(java.lang.String)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean startCameraStream()
com.android.rockchip.camera2.service.StreamingService: void processSocketConnections()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$switchToCameraMode$13(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager initialize(androidx.appcompat.app.AppCompatActivity)
com.android.rockchip.camera2.view.TpVideoPlayerView: void pause()
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String generateUniqueFileName(java.lang.String)
com.android.rockchip.camera2.util.TouptekIspParam: void setParamDefault(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.view.TpTextureView: boolean isDoubleTapEnabled()
com.android.rockchip.camera2.util.TransformUtils: android.graphics.Matrix getCurrentTransformMatrix()
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder$Builder onSurfaceAvailable(java.util.function.Consumer)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$hideControls$21()
com.android.rockchip.camera2.video.VideoDecoder: boolean isFrameByFrame()
com.android.rockchip.camera2.util.TouptekIspParam: void removeOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener)
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: AudioEncoder(com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder)
com.android.rockchip.camera2.view.TpTextureView: com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler getTouchEventHandler()
com.android.rockchip.camera2.util.NetworkManager: void enableAndConnectWifi()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$releaseVideo$19(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpCaptureImage: void saveImageAsync(android.graphics.Bitmap,java.lang.String,int,com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener)
com.android.rockchip.camera2.video.TvPreviewHelper$1: void handleMessage(android.os.Message)
com.android.rockchip.camera2.view.TpRoiView: float[] unmapPoint(float,float)
com.android.rockchip.camera2.view.TpVideoPlayerView: void releaseVideoSystem()
com.android.rockchip.camera2.view.TpImageView$GestureState: com.android.rockchip.camera2.view.TpImageView$GestureState[] $values()
com.android.rockchip.camera2.util.FileStorageUtils: java.lang.String getExternalStoragePath(android.content.Context)
com.android.rockchip.camera2.view.TpVideoPlayerView: long getDuration()
com.android.rockchip.video.R$layout: R$layout()
com.android.rockchip.camera2.util.TouptekIspParam: void updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int,int)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onRecordingStopped(java.lang.String)
com.android.rockchip.camera2.rtsp.service.ProjectionData: android.media.projection.MediaProjection getMediaProjection(android.content.Context)
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode valueOf(java.lang.String)
com.android.rockchip.camera2.util.TransformUtils: TransformUtils()
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: ScreenVideoEncoder$Builder(android.media.projection.MediaProjection)
com.android.rockchip.camera2.util.TransformUtils: float calculateBoundaryCorrection(android.graphics.RectF,android.graphics.RectF,boolean)
com.android.rockchip.camera2.video.VideoDecoder$VideoDecoderListener: void onPlaybackCompleted()
com.android.rockchip.camera2.view.TpRoiView: void animateRectAppear()
com.android.rockchip.camera2.view.TpRoiView: void setROIParams(int,int,int,int)
com.android.rockchip.camera2.rtsp.service.RTSPService: void onDestroy()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$29(android.hardware.camera2.CameraDevice,java.lang.Integer)
com.android.rockchip.camera2.service.StreamingService: boolean isVideoEncoderReady()
com.android.rockchip.camera2.util.HdmiService$ReadThread: HdmiService$ReadThread(com.android.rockchip.camera2.util.HdmiService)
com.android.rockchip.camera2.view.TpVideoPlayerView$6: TpVideoPlayerView$6(com.android.rockchip.camera2.view.TpVideoPlayerView)
com.android.rockchip.camera2.video.TvPreviewHelper: void stopPreview()
com.android.rockchip.camera2.view.TpVideoPlayerView$1: void onLongPressDetected(android.view.MotionEvent)
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: void release()
com.android.rockchip.camera2.service.StreamingSocketService: void writeIntToBytes(byte[],int,int)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer$RtspConnectChecker: RTSPStreamer$RtspConnectChecker(com.android.rockchip.camera2.rtsp.service.RTSPStreamer)
com.android.rockchip.camera2.video.TpVideoConfig: android.util.Size getSize()
com.android.rockchip.camera2.video.TpCameraManager: com.android.rockchip.camera2.video.TpCameraManager$Builder builder(android.content.Context)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onRecordingStarted(java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem$2: void lambda$onStreamStatusChanged$0(boolean,java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler: boolean onPanGestureDetected(android.view.MotionEvent)
com.android.rockchip.camera2.service.StreamingService: void monitorHeartbeat()
com.android.rockchip.camera2.video.VideoEncoder: android.media.MediaFormat getEncoderOutputFormat()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: boolean startCameraStreaming(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener)
com.android.rockchip.camera2.util.NetworkManager: NetworkManager(android.content.Context,com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener,androidx.activity.result.ActivityResultLauncher)
com.android.rockchip.camera2.util.TouptekIspParam: void notifyLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long)
com.android.rockchip.camera2.view.TpCustomProgressBar: void updateProgressFromTouch(android.view.MotionEvent)
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder: com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder setBufferSize(int)
com.android.rockchip.camera2.view.TpVideoPlayerView: void onDetachedFromWindow()
com.android.rockchip.camera2.view.TpRoiView: void drawHandleCircles(android.graphics.Canvas,float,float)
com.android.rockchip.camera2.view.TpVideoPlayerView: void setupControlListeners()
com.android.rockchip.camera2.view.TpVideoPlayerView: boolean isPlaybackCompleted()
com.android.rockchip.camera2.service.StreamingSocketService$1: void onImageSaved(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void loadVideo()
com.android.rockchip.camera2.video.TpCaptureImage$Builder: com.android.rockchip.camera2.video.TpCaptureImage$Builder setImageOutputFormat(int)
com.android.rockchip.camera2.rtsp.service.RTSPService: void stopStreaming()
com.android.rockchip.camera2.util.TouptekIspParam: void saveToLocal(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.video.TpVideoSystem: boolean lambda$stopStreaming$11()
com.android.rockchip.camera2.service.StreamingService: void setStreamType(com.android.rockchip.camera2.service.StreamingService$StreamType)
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution: int getHeight()
com.android.rockchip.camera2.util.touptek_serial_rk: void setDeviceStateCallback(com.android.rockchip.camera2.util.touptek_serial_rk$DeviceStateCallback)
com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo: NetworkManager$WifiConnectionInfo(boolean,java.lang.String)
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec: TpVideoConfig$VideoCodec(java.lang.String,int,java.lang.String)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void updateConfig(com.android.rockchip.camera2.rtsp.config.RTSPConfig)
com.android.rockchip.camera2.view.TpVideoPlayerView: void fastRewind(int)
com.android.rockchip.camera2.util.TransformUtils: void applyZoom(android.view.TextureView,com.android.rockchip.camera2.view.TpRoiView,float,float,float)
com.android.rockchip.camera2.service.StreamingService: com.android.rockchip.camera2.rtsp.RTSPManager getRtspManager()
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder: ScreenVideoEncoder(com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder)
com.android.rockchip.camera2.util.NetworkManager$1: void lambda$onLost$1()
com.android.rockchip.camera2.view.TpTextureView: void init(android.content.Context)
com.android.rockchip.camera2.view.TpRoiView: void initAnimator()
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder: com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setResolution(int,int)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$play$15()
com.android.rockchip.camera2.video.TpVideoSystem: boolean checkCameraOperationPreconditions(java.lang.String)
com.android.rockchip.camera2.video.TpVideoConfig: int getHeight()
com.android.rockchip.camera2.video.ImageDecoder: void lambda$loadImage$2(android.widget.ImageView,android.graphics.Bitmap)
com.android.rockchip.camera2.video.TpVideoSystem: void updateConfigBitRate(int)
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam$ParamData getParamInfo(com.android.rockchip.camera2.util.TouptekIspParam)
com.android.rockchip.camera2.util.FileStorageUtils: void stopUsbDriveMonitor(android.content.Context)
com.android.rockchip.camera2.service.StreamingSocketService: void sendRedImage(java.io.OutputStream,boolean)
com.android.rockchip.camera2.video.TpCameraManager$Builder: com.android.rockchip.camera2.video.TpCameraManager$Builder onCameraError(java.util.function.BiConsumer)
com.android.rockchip.camera2.video.VideoDecoder: int selectVideoTrack(android.media.MediaExtractor)
com.android.rockchip.camera2.view.TpRoiView: void onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.view.TpTextureView: TpTextureView(android.content.Context,android.util.AttributeSet,int)
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder$Builder onError(java.util.function.BiConsumer)
com.android.rockchip.camera2.rtsp.RTSPManager: boolean hasScreenCapturePermission()
com.android.rockchip.video.R$style: R$style()
com.android.rockchip.camera2.view.TpVideoPlayerView: void updateProgress()
com.android.rockchip.camera2.video.TpVideoSystem: boolean setCurrentVideoPlaybackSpeed(float)
com.android.rockchip.camera2.service.StreamingService: void lambda$updateVideoEncoder$9(com.android.rockchip.camera2.video.VideoEncoder)
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder setBitrate(int)
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: RTSPStreamer(android.content.Context,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager$StreamType,com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener)
com.android.rockchip.camera2.video.VideoEncoder: void notifyStorageFull()
com.android.rockchip.camera2.service.StreamingService: void handleSocketClient(java.net.Socket)
com.android.rockchip.camera2.video.TpVideoSystem: void releaseVideo()
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$forceUpdateProgressToEnd$20(long)
com.android.rockchip.camera2.util.TouptekIspParam$1: void onDeviceStateChanged(boolean)
com.android.rockchip.camera2.video.TpVideoSystem: void switchToCameraMode()
com.android.rockchip.camera2.rtsp.RTSPManager$1: void onStreamStarted(java.lang.String)
com.android.rockchip.camera2.util.TouptekIspParam: void saveToLocal(com.android.rockchip.camera2.util.TouptekIspParam,long)
com.android.rockchip.camera2.video.VideoEncoder: void startRecording(java.lang.String)
com.android.rockchip.camera2.view.TpRoiView: TpRoiView(android.content.Context,android.util.AttributeSet,int)
com.android.rockchip.camera2.util.SMBFileUploader: void setEnabled(boolean)
com.android.rockchip.camera2.view.TpImageView: void setMaxScale(float)
com.android.rockchip.camera2.video.TpCaptureImage$Builder: com.android.rockchip.camera2.video.TpCaptureImage$Builder onImageSaved(java.util.function.Consumer)
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener: void onCameraStarted()
com.android.rockchip.camera2.video.VideoEncoder: void notifySaveComplete(java.lang.String)
com.android.rockchip.camera2.video.TpCaptureImage$1: void onError(java.lang.String)
com.android.rockchip.camera2.video.TpImageLoader: void lambda$clearCache$6()
com.android.rockchip.camera2.video.ImageDecoder: java.lang.String getFileExtension(java.lang.String)
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TpCaptureImage getImageCapture()
com.android.rockchip.camera2.video.VideoEncoder: boolean setBitrate(int)
com.android.rockchip.camera2.rtsp.service.RTSPService: void onConnectionStarted(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView$2: void onProgressChanged(com.android.rockchip.camera2.view.TpCustomProgressBar,int,boolean)
com.android.rockchip.camera2.service.StreamingSocketService: void onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.view.TpImageView$ScaleInfo: TpImageView$ScaleInfo()
com.android.rockchip.camera2.view.TpRoiView: void updateOriginalRect()
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int getKeyFrameInterval()
com.android.rockchip.camera2.util.TransformUtils: void resetTransform(android.view.TextureView,com.android.rockchip.camera2.view.TpRoiView)
com.android.rockchip.camera2.view.TpTextureView$GestureState: com.android.rockchip.camera2.view.TpTextureView$GestureState[] $values()
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo: java.lang.String getName()
com.android.rockchip.camera2.video.ImageDecoder: void putBitmapToCache(android.content.Context,java.lang.String,int,int,android.graphics.Bitmap)
com.android.rockchip.camera2.video.TpVideoSystem$2: void onStreamStatusChanged(boolean,java.lang.String)
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager onStreamError(java.util.function.Consumer)
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder$Builder setPreviewSurface(android.view.Surface)
com.android.rockchip.video.R$styleable: R$styleable()
com.android.rockchip.camera2.video.TpCaptureImage: void saveImageWithFormat(android.graphics.Bitmap,java.lang.String,int)
com.android.rockchip.camera2.util.TransformUtils: float getCurrentScale(android.view.TextureView)
com.android.rockchip.camera2.view.TpRoiView: void loadMirrorFlipState()
com.android.rockchip.camera2.util.TouptekIspParam: void handleReceivedData(com.android.rockchip.camera2.util.TouptekIspParam,long,boolean)
com.android.rockchip.camera2.video.ImageDecoder: void loadImageAsync(java.lang.String,android.widget.ImageView)
com.android.rockchip.camera2.view.TpVideoPlayerView: void fastForward(int)
com.android.rockchip.camera2.video.ImageDecoder: android.graphics.Bitmap extractVideoThumbnail(java.lang.String)
com.android.rockchip.camera2.view.TpRoiView: void animateRectDisappear()
com.android.rockchip.camera2.rtsp.service.RTSPService$RTSPBinder: RTSPService$RTSPBinder(com.android.rockchip.camera2.rtsp.service.RTSPService)
com.android.rockchip.camera2.service.StreamingService: void startService()
com.android.rockchip.camera2.video.TpCaptureImage: void lambda$saveImageAsync$5(int,android.graphics.Bitmap,java.lang.String,com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener)
com.android.rockchip.camera2.video.TpCaptureImage$1: void onImageSaved(java.lang.String)
com.android.rockchip.camera2.view.TpImageView$GestureState: com.android.rockchip.camera2.view.TpImageView$GestureState[] values()
com.android.rockchip.camera2.view.TpTextureView$GestureState: com.android.rockchip.camera2.view.TpTextureView$GestureState[] values()
com.android.rockchip.camera2.video.ImageDecoder: boolean isFileModified(java.lang.String,java.io.File)
com.android.rockchip.camera2.video.VideoEncoder$1: void handleStorageError(java.lang.String)
com.android.rockchip.camera2.view.TpTextureView: TpTextureView(android.content.Context)
com.android.rockchip.camera2.video.TpCaptureImage: void lambda$saveImageAsync$1(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener,java.lang.String)
com.android.rockchip.camera2.video.VideoDecoder: void seekRelative(long)
com.android.rockchip.camera2.rtsp.RTSPManager$1: void onStreamError(java.lang.String)
com.android.rockchip.camera2.view.TpVideoPlayerView: void stopStatusSync()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$switchToTvMode$12(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TvPreviewHelper: void initTvView()
com.android.rockchip.camera2.view.TpCustomProgressBar: void setOnProgressChangeListener(com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener)
com.android.rockchip.camera2.video.TpVideoSystem: boolean isCameraStarted()
com.android.rockchip.camera2.video.TpVideoSystem: java.lang.Object getAdvancedComponent(java.lang.Object,java.lang.String)
com.android.rockchip.camera2.util.NetworkManager: com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo getCurrentWifiState()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void lambda$notifyStreamError$3(java.lang.String)
com.android.rockchip.camera2.video.TpVideoConfig$Builder: void validateParameters()
com.android.rockchip.camera2.video.TpVideoSystem$StreamType: TpVideoSystem$StreamType(java.lang.String,int)
com.android.rockchip.camera2.view.TpVideoPlayerView: void resetToStart()
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TpCameraManager getCameraManager()
com.android.rockchip.camera2.util.touptek_serial_rk: touptek_serial_rk()
com.android.rockchip.video.R$drawable: R$drawable()
com.android.rockchip.camera2.video.TpVideoSystem: boolean updateBitRate(int)
com.android.rockchip.camera2.video.TpCaptureImage$Builder: com.android.rockchip.camera2.video.TpCaptureImage$Builder setImageFormat(int)
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder: com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder setResolution(int,int)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$setupControlListeners$4(android.view.View)
com.android.rockchip.camera2.video.VideoEncoder: int mapBitrateModeToEncoder(com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode)
com.android.rockchip.camera2.service.StreamingService: void lambda$stopRtspManually$17()
com.android.rockchip.camera2.util.FileStorageUtils: long getAvailableStorageSpace(java.lang.String)
com.android.rockchip.camera2.view.TpTextureView: float getMaxScale()
com.android.rockchip.camera2.view.TpImageView: void setDoubleTapEnabled(boolean)
com.android.rockchip.camera2.view.TpVideoPlayerView: void lambda$setupControlListeners$6(android.view.View)
com.android.rockchip.camera2.view.TpImageView: void scheduleResetGestureState()
com.android.rockchip.camera2.video.TpVideoSystem: TpVideoSystem(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.video.TpVideoConfig)
com.android.rockchip.camera2.util.HdmiService: void init()
com.android.rockchip.camera2.service.StreamingSocketService: void runServer()
com.android.rockchip.camera2.video.ImageDecoder: java.lang.String getCacheStats()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$32()
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder$Builder onStorageFull(java.lang.Runnable)
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder: com.android.rockchip.camera2.rtsp.encoder.AudioEncoder build()
com.android.rockchip.camera2.view.TpTextureView: void setOnZoomChangeListener(com.android.rockchip.camera2.view.TpTextureView$OnZoomChangeListener)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$executeStreamingOperation$40(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType: RTSPManager$StreamType(java.lang.String,int)
com.android.rockchip.camera2.view.TpImageView: TpImageView(android.content.Context)
com.android.rockchip.camera2.view.TpRoiView$ControlHandle: com.android.rockchip.camera2.view.TpRoiView$ControlHandle[] values()
com.android.rockchip.camera2.util.TouptekIspParam: boolean getIsDisableValue(com.android.rockchip.camera2.util.TouptekIspParam)
com.android.rockchip.camera2.view.TpImageView: void init(android.content.Context)
com.android.rockchip.camera2.util.NetworkManager: void setupNetworkMonitoring()
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager setConfig(com.android.rockchip.camera2.rtsp.config.RTSPConfig)
com.android.rockchip.camera2.video.TpImageLoader: android.graphics.Bitmap decodeSampledBitmapFromFile(java.lang.String,int,int)
com.android.rockchip.camera2.video.TpVideoSystem: com.android.rockchip.camera2.video.TpVideoConfig getVideoConfig()
com.android.rockchip.camera2.service.StreamingService: void lambda$startSubServices$15(java.lang.String)
com.android.rockchip.camera2.video.TpImageLoader: void lambda$loadFullImage$4(java.lang.String,android.widget.ImageView,android.graphics.Bitmap,java.lang.String)
com.android.rockchip.camera2.view.TpImageView: boolean isDoubleTapEnabled()
com.android.rockchip.camera2.service.StreamingService: void initStreaming(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.TpCaptureImage)
com.android.rockchip.camera2.util.TouptekIspParam: int getMaxValue(com.android.rockchip.camera2.util.TouptekIspParam)
com.android.rockchip.camera2.rtsp.RTSPManager: com.android.rockchip.camera2.rtsp.RTSPManager onStreamStarted(java.util.function.Consumer)
com.android.rockchip.camera2.video.TpVideoSystem: float getCurrentVideoPlaybackSpeed()
com.android.rockchip.camera2.video.TpCaptureImage: void saveBitmap(android.graphics.Bitmap,java.lang.String)
com.android.rockchip.camera2.service.StreamingSocketService: void stop()
com.android.rockchip.camera2.service.StreamingSocketService: android.os.IBinder onBind(android.content.Intent)
com.android.rockchip.camera2.video.TpVideoSystem$2: void onStreamError(java.lang.String)
com.android.rockchip.camera2.video.VideoEncoder$VideoDataOutputCallback: void onVideoFormatChanged(android.media.MediaFormat,java.nio.ByteBuffer,java.nio.ByteBuffer)
com.android.rockchip.camera2.rtsp.RTSPManager: boolean isStreaming()
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode: com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode[] values()
com.android.rockchip.camera2.view.TpImageView: boolean onTouchEvent(android.view.MotionEvent)
com.android.rockchip.camera2.video.TpVideoSystem: boolean lambda$startStreaming$10(com.android.rockchip.camera2.video.TpVideoSystem$StreamType,java.lang.String)
com.android.rockchip.camera2.view.TpRoiView: TpRoiView(android.content.Context)
com.android.rockchip.camera2.video.VideoEncoder: com.android.rockchip.camera2.video.VideoEncoder$Builder builder()
com.android.rockchip.camera2.view.TpVideoPlayerView: void resetControlsTimer()
com.android.rockchip.camera2.video.VideoDecoder: void togglePlayPause()
com.android.rockchip.camera2.video.VideoEncoder$Builder: com.android.rockchip.camera2.video.VideoEncoder$Builder setTpVideoConfig(com.android.rockchip.camera2.video.TpVideoConfig)
com.android.rockchip.camera2.video.TpVideoSystem$StreamType: com.android.rockchip.camera2.video.TpVideoSystem$StreamType[] values()
com.android.rockchip.camera2.util.SMBFileUploader: com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig getConnectionParams()
com.android.rockchip.camera2.view.TpRoiView: void initializeROIRect()
com.android.rockchip.camera2.util.NetworkManager: boolean isEthernetConnected()
com.android.rockchip.camera2.rtsp.service.RTSPStreamer: void stopCameraStream()
com.android.rockchip.camera2.rtsp.service.ProjectionData: boolean isValid()
com.android.rockchip.camera2.service.StreamingService: void writeIntToByteArray(byte[],int,int)
com.android.rockchip.camera2.service.StreamingSocketService: StreamingSocketService(com.android.rockchip.camera2.video.TpCaptureImage,java.lang.String)
com.android.rockchip.camera2.util.touptek_serial_rk$DeviceStateCallback: void onDeviceStateChanged(boolean)
com.android.rockchip.camera2.view.TpImageView: void setImageDrawable(android.graphics.drawable.Drawable)
com.android.rockchip.camera2.video.VideoDecoder: void stopDecoding()
com.android.rockchip.camera2.service.StreamingService: boolean stopRtspManually()
com.android.rockchip.camera2.video.TpCaptureImage: void requestCapture(android.util.Size,java.lang.String)
com.android.rockchip.camera2.view.TpCustomProgressBar: void onDraw(android.graphics.Canvas)
com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo: boolean isConnected()
com.android.rockchip.camera2.view.TpTextureView$ScaleInfo: TpTextureView$ScaleInfo()
com.android.rockchip.camera2.video.VideoEncoder: void lambda$startEncodingDecoding$3()
com.android.rockchip.camera2.rtsp.config.RTSPConfig: int getVideoBitrate()
com.android.rockchip.camera2.video.TpVideoSystem: boolean isStreaming()
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder: void setupEncoder()
com.android.rockchip.camera2.util.TouptekIspParam: void sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int)
com.android.rockchip.camera2.video.TpVideoSystem: long getCurrentVideoPosition()
com.android.rockchip.camera2.video.TpCameraManager$Builder: com.android.rockchip.camera2.video.TpCameraManager$Builder onCameraDisconnected(java.util.function.Consumer)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$stopRecording$3(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.video.TpVideoSystem: boolean executeStreamingOperation(com.android.rockchip.camera2.video.TpVideoSystem$StreamingOperation)
com.android.rockchip.camera2.view.TpVideoPlayerView: void stop()
com.android.rockchip.camera2.video.TpCameraManager: void openCamera()
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection: void unbind()
com.android.rockchip.camera2.util.HdmiService: int getFrameRate()
com.android.rockchip.camera2.view.TpVideoPlayerView: void initControlButtons()
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initVideoEncoder$37(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
com.android.rockchip.camera2.util.TouptekIspParam: com.android.rockchip.camera2.util.TouptekIspParam fromInt(int)
com.android.rockchip.camera2.video.TpVideoSystem: void lambda$initCaptureHelper$24(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener)
