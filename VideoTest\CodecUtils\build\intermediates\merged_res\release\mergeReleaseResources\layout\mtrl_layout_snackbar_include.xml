<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2015 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->

<view
  xmlns:android="http://schemas.android.com/apk/res/android"
  class="com.google.android.material.snackbar.SnackbarContentLayout"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:layout_gravity="bottom">

  <TextView
    android:id="@+id/snackbar_text"
    style="?attr/snackbarTextViewStyle"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:layout_gravity="center_vertical|left|start"/>

  <Button
    android:id="@+id/snackbar_action"
    style="?attr/snackbarButtonStyle"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical|right|end"
    android:minWidth="48dp"
    android:visibility="gone"/>

</view>
