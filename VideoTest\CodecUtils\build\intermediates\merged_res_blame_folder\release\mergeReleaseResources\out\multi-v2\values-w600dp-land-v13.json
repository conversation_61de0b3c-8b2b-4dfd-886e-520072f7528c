{"logs": [{"outputFile": "com.android.rockchip.video.CodecUtils-mergeReleaseResources-33:/values-w600dp-land-v13/values-w600dp-land-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\676e6cf5fb8bbd8d94b07d695c20fbc3\\transformed\\material-1.10.0\\res\\values-w600dp-land-v13\\values-w600dp-land-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "54", "endOffsets": "105"}}]}, {"outputFile": "com.android.rockchip.video.CodecUtils-release-35:/values-w600dp-land-v13/values-w600dp-land-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\676e6cf5fb8bbd8d94b07d695c20fbc3\\transformed\\material-1.10.0\\res\\values-w600dp-land-v13\\values-w600dp-land-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "54", "endOffsets": "105"}}]}]}