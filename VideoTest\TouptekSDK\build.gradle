plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace 'com.touptek'
    compileSdk 34

    defaultConfig {
        minSdk 31

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        // 指定支持的 ABI
        ndk {
            abiFilters 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    // 确保 .so 文件被包含在 AAR 中
    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs']
        }
    }
}

dependencies {
    implementation libs.kotlin.stdlib
    implementation libs.appcompat
    implementation libs.material
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

//    implementation files('libs/android.jar') // 用于生成javadoc文档
    //noinspection GradleDependency
    implementation("com.github.pedroSG94.RootEncoder:library:2.3.5")
    //noinspection GradleDependency
    implementation("com.github.pedroSG94:RTSP-Server:1.2.1")

    // JCIFS库，用于SMB文件传输
    implementation 'eu.agno3.jcifs:jcifs-ng:2.1.9'

    implementation 'com.github.bumptech.glide:glide:4.15.1'

    // TIFF格式支持库
    implementation 'io.github.beyka:Android-TiffBitmapFactory:0.9.9.1'
}
