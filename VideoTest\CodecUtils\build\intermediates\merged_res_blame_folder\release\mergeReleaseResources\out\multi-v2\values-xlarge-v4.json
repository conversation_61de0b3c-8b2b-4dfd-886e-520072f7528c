{"logs": [{"outputFile": "com.android.rockchip.video.CodecUtils-mergeReleaseResources-33:/values-xlarge-v4/values-xlarge-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4fc5f63fc0925efc93853f0f2ba4b8d\\transformed\\appcompat-1.6.1\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}}]}, {"outputFile": "com.android.rockchip.video.CodecUtils-release-35:/values-xlarge-v4/values-xlarge-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4fc5f63fc0925efc93853f0f2ba4b8d\\transformed\\appcompat-1.6.1\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}}]}]}