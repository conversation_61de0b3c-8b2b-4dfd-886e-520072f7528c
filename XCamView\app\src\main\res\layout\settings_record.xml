<!-- res/layout/settings_record.xml -->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:divider="@drawable/divider"
        android:showDividers="middle">

        <!-- 分辨率设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="分辨率设置："
                android:textSize="18sp"
                android:textStyle="bold"/>

            <RadioGroup
                android:id="@+id/resolution_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/radio_1080p"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="1080p (1920x1080)" />

                <RadioButton
                    android:id="@+id/radio_4k"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="4K (3840x2160)" />
            </RadioGroup>
        </LinearLayout>

        <!-- 视频质量设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="视频质量："
                android:textSize="18sp"
                android:textStyle="bold"/>

            <RadioGroup
                android:id="@+id/quality_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:weightSum="3">

                <RadioButton
                    android:id="@+id/radio_low"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="低" />

                <RadioButton
                    android:id="@+id/radio_medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="中"
                    android:checked="true" />

                <RadioButton
                    android:id="@+id/radio_high"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="高" />
            </RadioGroup>
        </LinearLayout>

        <!-- 录制模式设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="录制模式："
                android:textSize="18sp"
                android:textStyle="bold"/>

            <RadioGroup
                android:id="@+id/mode_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/radio_normal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="正常模式"
                    android:checked="true" />

                <RadioButton
                    android:id="@+id/radio_low_latency"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="低延时模式" />
            </RadioGroup>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="低延时模式适用于实时预览和推流"
                android:textSize="14sp"
                android:textColor="?android:attr/textColorSecondary"
                android:layout_marginTop="8dp"/>
        </LinearLayout>

        <!-- 功能按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="3"
            android:layout_marginTop="16dp"
            android:padding="16dp"
            android:background="@drawable/border_box">

            <Button
                android:id="@+id/btnApplySettings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="应用设置"
                android:layout_marginEnd="8dp"/>

            <Button
                android:id="@+id/btnResetDefault"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="恢复默认"
                android:layout_marginHorizontal="8dp"/>

        </LinearLayout>
    </LinearLayout>
</ScrollView>