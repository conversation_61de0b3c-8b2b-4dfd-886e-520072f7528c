{"logs": [{"outputFile": "com.android.rockchip.mediacodecnew.app-mergeDebugResources-35:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\737b03f0fc1ad0fd308d8e116bf71918\\transformed\\material-1.12.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1038,1103,1197,1267,1326,1416,1480,1549,1607,1676,1736,1800,1912,1971,2030,2085,2160,2283,2363,2446,2540,2627,2711,2844,2926,3007,3138,3225,3307,3365,3421,3487,3562,3642,3713,3792,3859,3934,4011,4075,4182,4276,4346,4435,4528,4602,4677,4767,4823,4902,4969,5053,5137,5199,5263,5326,5392,5492,5599,5693,5801,5863,5923,6003,6088,6169", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "254,332,410,488,586,675,775,894,977,1033,1098,1192,1262,1321,1411,1475,1544,1602,1671,1731,1795,1907,1966,2025,2080,2155,2278,2358,2441,2535,2622,2706,2839,2921,3002,3133,3220,3302,3360,3416,3482,3557,3637,3708,3787,3854,3929,4006,4070,4177,4271,4341,4430,4523,4597,4672,4762,4818,4897,4964,5048,5132,5194,5258,5321,5387,5487,5594,5688,5796,5858,5918,5998,6083,6164,6238"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3114,3192,3270,3368,4178,4278,4397,4480,4536,4601,4695,4765,4824,4914,4978,5047,5105,5174,5234,5298,5410,5469,5528,5583,5658,5781,5861,5944,6038,6125,6209,6342,6424,6505,6636,6723,6805,6863,6919,6985,7060,7140,7211,7290,7357,7432,7509,7573,7680,7774,7844,7933,8026,8100,8175,8265,8321,8400,8467,8551,8635,8697,8761,8824,8890,8990,9097,9191,9299,9361,9421,9587,9672,9753", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,77,77,77,97,88,99,118,82,55,64,93,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,82,93,86,83,132,81,80,130,86,81,57,55,65,74,79,70,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79,84,80,73", "endOffsets": "304,3109,3187,3265,3363,3452,4273,4392,4475,4531,4596,4690,4760,4819,4909,4973,5042,5100,5169,5229,5293,5405,5464,5523,5578,5653,5776,5856,5939,6033,6120,6204,6337,6419,6500,6631,6718,6800,6858,6914,6980,7055,7135,7206,7285,7352,7427,7504,7568,7675,7769,7839,7928,8021,8095,8170,8260,8316,8395,8462,8546,8630,8692,8756,8819,8885,8985,9092,9186,9294,9356,9416,9496,9667,9748,9822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c629d2bab069b69ff70afa7604a76b8d\\transformed\\core-1.13.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3457,3555,3657,3759,3863,3966,4064,9827", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "3550,3652,3754,3858,3961,4059,4173,9923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5ee0973f6a00ce88be66ccc82fc087bc\\transformed\\appcompat-1.7.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,423,529,638,724,828,948,1025,1100,1192,1286,1381,1475,1576,1670,1766,1860,1952,2044,2129,2237,2343,2445,2556,2657,2773,2938,9501", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "418,524,633,719,823,943,1020,1095,1187,1281,1376,1470,1571,1665,1761,1855,1947,2039,2124,2232,2338,2440,2551,2652,2768,2933,3031,9582"}}]}]}