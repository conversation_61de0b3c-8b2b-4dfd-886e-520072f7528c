  Animator android.animation  AnimatorListenerAdapter android.animation  TimeInterpolator android.animation  
ValueAnimator android.animation  addListener android.animation.Animator  addUpdateListener android.animation.Animator  apply android.animation.Animator  cancel android.animation.Animator  start android.animation.Animator  Animator )android.animation.AnimatorListenerAdapter  instantCheckBounds )android.animation.AnimatorListenerAdapter  syncCurrentScale )android.animation.AnimatorListenerAdapter  DecelerateInterpolator android.animation.ValueAnimator  addListener android.animation.ValueAnimator  addUpdateListener android.animation.ValueAnimator  
animatedValue android.animation.ValueAnimator  apply android.animation.ValueAnimator  cancel android.animation.ValueAnimator  duration android.animation.ValueAnimator  getANIMATEDValue android.animation.ValueAnimator  getAPPLY android.animation.ValueAnimator  getAnimatedValue android.animation.ValueAnimator  getApply android.animation.ValueAnimator  getDURATION android.animation.ValueAnimator  getDuration android.animation.ValueAnimator  getINSTANTCheckBounds android.animation.ValueAnimator  getINTERPOLATOR android.animation.ValueAnimator  getInstantCheckBounds android.animation.ValueAnimator  getInterpolator android.animation.ValueAnimator  	getMATRIX android.animation.ValueAnimator  	getMatrix android.animation.ValueAnimator  getSAVEDMatrix android.animation.ValueAnimator  getSYNCCurrentScale android.animation.ValueAnimator  getSavedMatrix android.animation.ValueAnimator  getSyncCurrentScale android.animation.ValueAnimator  getUPDATEImageMatrix android.animation.ValueAnimator  getUpdateImageMatrix android.animation.ValueAnimator  instantCheckBounds android.animation.ValueAnimator  interpolator android.animation.ValueAnimator  matrix android.animation.ValueAnimator  ofFloat android.animation.ValueAnimator  savedMatrix android.animation.ValueAnimator  setAnimatedValue android.animation.ValueAnimator  setDuration android.animation.ValueAnimator  setInterpolator android.animation.ValueAnimator  start android.animation.ValueAnimator  syncCurrentScale android.animation.ValueAnimator  updateImageMatrix android.animation.ValueAnimator  <SAM-CONSTRUCTOR> 6android.animation.ValueAnimator.AnimatorUpdateListener  SuppressLint android.annotation  Context android.content  Bitmap android.graphics  Matrix android.graphics  RectF android.graphics  equals android.graphics.Bitmap  	getHEIGHT android.graphics.Bitmap  	getHeight android.graphics.Bitmap  getWIDTH android.graphics.Bitmap  getWidth android.graphics.Bitmap  height android.graphics.Bitmap  	setHeight android.graphics.Bitmap  setWidth android.graphics.Bitmap  width android.graphics.Bitmap  MSCALE_X android.graphics.Matrix  MSCALE_Y android.graphics.Matrix  	getValues android.graphics.Matrix  mapRect android.graphics.Matrix  	postScale android.graphics.Matrix  
postTranslate android.graphics.Matrix  reset android.graphics.Matrix  set android.graphics.Matrix  bottom android.graphics.RectF  height android.graphics.RectF  left android.graphics.RectF  right android.graphics.RectF  top android.graphics.RectF  width android.graphics.RectF  Drawable android.graphics.drawable  getINTRINSICHeight "android.graphics.drawable.Drawable  getINTRINSICWidth "android.graphics.drawable.Drawable  getIntrinsicHeight "android.graphics.drawable.Drawable  getIntrinsicWidth "android.graphics.drawable.Drawable  getLET "android.graphics.drawable.Drawable  getLet "android.graphics.drawable.Drawable  intrinsicHeight "android.graphics.drawable.Drawable  intrinsicWidth "android.graphics.drawable.Drawable  let "android.graphics.drawable.Drawable  setIntrinsicHeight "android.graphics.drawable.Drawable  setIntrinsicWidth "android.graphics.drawable.Drawable  AttributeSet android.util  Log android.util  d android.util.Log  w android.util.Log  GestureDetector android.view  MotionEvent android.view  ScaleGestureDetector android.view  View android.view  ViewConfiguration android.view  SimpleOnGestureListener android.view.GestureDetector  onTouchEvent android.view.GestureDetector  Boolean 4android.view.GestureDetector.SimpleOnGestureListener  Float 4android.view.GestureDetector.SimpleOnGestureListener  Log 4android.view.GestureDetector.SimpleOnGestureListener  	MAX_SCALE 4android.view.GestureDetector.SimpleOnGestureListener  MotionEvent 4android.view.GestureDetector.SimpleOnGestureListener  	baseScale 4android.view.GestureDetector.SimpleOnGestureListener  getCurrentScaleFactor 4android.view.GestureDetector.SimpleOnGestureListener  instantScaleTo 4android.view.GestureDetector.SimpleOnGestureListener  isAtMinimumScale 4android.view.GestureDetector.SimpleOnGestureListener  
isInitialized 4android.view.GestureDetector.SimpleOnGestureListener  	isScaling 4android.view.GestureDetector.SimpleOnGestureListener  matrix 4android.view.GestureDetector.SimpleOnGestureListener  min 4android.view.GestureDetector.SimpleOnGestureListener  onSingleTapListener 4android.view.GestureDetector.SimpleOnGestureListener  startFlingAnimation 4android.view.GestureDetector.SimpleOnGestureListener  syncCurrentScale 4android.view.GestureDetector.SimpleOnGestureListener  updateImageMatrix 4android.view.GestureDetector.SimpleOnGestureListener  getX android.view.InputEvent  getY android.view.InputEvent  
ACTION_CANCEL android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  ACTION_POINTER_DOWN android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  actionMasked android.view.MotionEvent  getACTIONMasked android.view.MotionEvent  getActionMasked android.view.MotionEvent  getPOINTERCount android.view.MotionEvent  getPointerCount android.view.MotionEvent  getX android.view.MotionEvent  getY android.view.MotionEvent  pointerCount android.view.MotionEvent  setActionMasked android.view.MotionEvent  setPointerCount android.view.MotionEvent  setX android.view.MotionEvent  setY android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  SimpleOnScaleGestureListener !android.view.ScaleGestureDetector  currentSpan !android.view.ScaleGestureDetector  focusX !android.view.ScaleGestureDetector  focusY !android.view.ScaleGestureDetector  getCURRENTSpan !android.view.ScaleGestureDetector  getCurrentSpan !android.view.ScaleGestureDetector  	getFocusX !android.view.ScaleGestureDetector  	getFocusY !android.view.ScaleGestureDetector  getISInProgress !android.view.ScaleGestureDetector  getIsInProgress !android.view.ScaleGestureDetector  getSCALEFactor !android.view.ScaleGestureDetector  getScaleFactor !android.view.ScaleGestureDetector  isInProgress !android.view.ScaleGestureDetector  onTouchEvent !android.view.ScaleGestureDetector  scaleFactor !android.view.ScaleGestureDetector  setCurrentSpan !android.view.ScaleGestureDetector  	setFocusX !android.view.ScaleGestureDetector  	setFocusY !android.view.ScaleGestureDetector  
setInProgress !android.view.ScaleGestureDetector  setScaleFactor !android.view.ScaleGestureDetector  Boolean >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  Log >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  	MAX_SCALE >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  SCALE_SENSITIVITY >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  ScaleGestureDetector >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  coerceIn >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  currentScale >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  dynamicMinScale >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  instantCheckBounds >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  
isInitialized >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  	isScaling >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  matrix >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  syncCurrentScale >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  updateImageMatrix >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener  Animator android.view.View  AnimatorListenerAdapter android.view.View  AttributeSet android.view.View  Bitmap android.view.View  Boolean android.view.View  Context android.view.View  DecelerateInterpolator android.view.View  Drawable android.view.View  	Exception android.view.View  Float android.view.View  
FloatArray android.view.View  GestureDetector android.view.View  Int android.view.View  JvmOverloads android.view.View  Log android.view.View  	MAX_SCALE android.view.View  Matrix android.view.View  MotionEvent android.view.View  RectF android.view.View  SCALE_SENSITIVITY android.view.View  ScaleGestureDetector android.view.View  	ScaleType android.view.View  SuppressLint android.view.View  Unit android.view.View  
ValueAnimator android.view.View  ViewConfiguration android.view.View  abs android.view.View  animateTranslate android.view.View  apply android.view.View  	baseScale android.view.View  cancelAnimations android.view.View  captureDrawableDimensions android.view.View  coerceIn android.view.View  !configureScaleDetectorSensitivity android.view.View  currentScale android.view.View  dynamicMinScale android.view.View  getCurrentScaleFactor android.view.View  getDisplayRect android.view.View  getDistance android.view.View  getValue android.view.View  handleCustomScale android.view.View  instantCheckBounds android.view.View  instantScaleTo android.view.View  isAtMinimumScale android.view.View  
isInitialized android.view.View  	isScaling android.view.View  java android.view.View  lazy android.view.View  let android.view.View  matrix android.view.View  min android.view.View  onDetachedFromWindow android.view.View  onSingleTapListener android.view.View  
onSizeChanged android.view.View  performClick android.view.View  post android.view.View  provideDelegate android.view.View  savedMatrix android.view.View  setImageBitmap android.view.View  setImageDrawable android.view.View  setOnTouchListener android.view.View  setupGestureDetectors android.view.View  setupTouchListener android.view.View  sqrt android.view.View  startFlingAnimation android.view.View  syncCurrentScale android.view.View  updateBitmapDimensions android.view.View  updateImageMatrix android.view.View  updateMatrix android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnTouchListener  get android.view.ViewConfiguration  getSCALEDTouchSlop android.view.ViewConfiguration  getScaledTouchSlop android.view.ViewConfiguration  scaledTouchSlop android.view.ViewConfiguration  setScaledTouchSlop android.view.ViewConfiguration  DecelerateInterpolator android.view.animation  Animator android.widget.ImageView  AnimatorListenerAdapter android.widget.ImageView  AttributeSet android.widget.ImageView  Bitmap android.widget.ImageView  Boolean android.widget.ImageView  Context android.widget.ImageView  DecelerateInterpolator android.widget.ImageView  Drawable android.widget.ImageView  	Exception android.widget.ImageView  Float android.widget.ImageView  
FloatArray android.widget.ImageView  GestureDetector android.widget.ImageView  Int android.widget.ImageView  JvmOverloads android.widget.ImageView  Log android.widget.ImageView  	MAX_SCALE android.widget.ImageView  Matrix android.widget.ImageView  MotionEvent android.widget.ImageView  RectF android.widget.ImageView  SCALE_SENSITIVITY android.widget.ImageView  ScaleGestureDetector android.widget.ImageView  	ScaleType android.widget.ImageView  SuppressLint android.widget.ImageView  Unit android.widget.ImageView  
ValueAnimator android.widget.ImageView  ViewConfiguration android.widget.ImageView  abs android.widget.ImageView  animateTranslate android.widget.ImageView  apply android.widget.ImageView  	baseScale android.widget.ImageView  cancelAnimations android.widget.ImageView  captureDrawableDimensions android.widget.ImageView  coerceIn android.widget.ImageView  !configureScaleDetectorSensitivity android.widget.ImageView  currentScale android.widget.ImageView  dynamicMinScale android.widget.ImageView  getCurrentScaleFactor android.widget.ImageView  getDisplayRect android.widget.ImageView  getDistance android.widget.ImageView  getValue android.widget.ImageView  handleCustomScale android.widget.ImageView  instantCheckBounds android.widget.ImageView  instantScaleTo android.widget.ImageView  isAtMinimumScale android.widget.ImageView  
isInitialized android.widget.ImageView  	isScaling android.widget.ImageView  java android.widget.ImageView  lazy android.widget.ImageView  let android.widget.ImageView  matrix android.widget.ImageView  min android.widget.ImageView  onDetachedFromWindow android.widget.ImageView  onSingleTapListener android.widget.ImageView  
onSizeChanged android.widget.ImageView  performClick android.widget.ImageView  post android.widget.ImageView  provideDelegate android.widget.ImageView  savedMatrix android.widget.ImageView  setImageBitmap android.widget.ImageView  setImageDrawable android.widget.ImageView  setOnTouchListener android.widget.ImageView  setupGestureDetectors android.widget.ImageView  setupTouchListener android.widget.ImageView  sqrt android.widget.ImageView  startFlingAnimation android.widget.ImageView  syncCurrentScale android.widget.ImageView  updateBitmapDimensions android.widget.ImageView  updateImageMatrix android.widget.ImageView  updateMatrix android.widget.ImageView  MATRIX "android.widget.ImageView.ScaleType  AppCompatImageView androidx.appcompat.widget  Animator ,androidx.appcompat.widget.AppCompatImageView  AnimatorListenerAdapter ,androidx.appcompat.widget.AppCompatImageView  AttributeSet ,androidx.appcompat.widget.AppCompatImageView  Bitmap ,androidx.appcompat.widget.AppCompatImageView  Boolean ,androidx.appcompat.widget.AppCompatImageView  Context ,androidx.appcompat.widget.AppCompatImageView  DecelerateInterpolator ,androidx.appcompat.widget.AppCompatImageView  Drawable ,androidx.appcompat.widget.AppCompatImageView  	Exception ,androidx.appcompat.widget.AppCompatImageView  Float ,androidx.appcompat.widget.AppCompatImageView  
FloatArray ,androidx.appcompat.widget.AppCompatImageView  GestureDetector ,androidx.appcompat.widget.AppCompatImageView  Int ,androidx.appcompat.widget.AppCompatImageView  JvmOverloads ,androidx.appcompat.widget.AppCompatImageView  Log ,androidx.appcompat.widget.AppCompatImageView  	MAX_SCALE ,androidx.appcompat.widget.AppCompatImageView  Matrix ,androidx.appcompat.widget.AppCompatImageView  MotionEvent ,androidx.appcompat.widget.AppCompatImageView  RectF ,androidx.appcompat.widget.AppCompatImageView  SCALE_SENSITIVITY ,androidx.appcompat.widget.AppCompatImageView  ScaleGestureDetector ,androidx.appcompat.widget.AppCompatImageView  	ScaleType ,androidx.appcompat.widget.AppCompatImageView  SuppressLint ,androidx.appcompat.widget.AppCompatImageView  Unit ,androidx.appcompat.widget.AppCompatImageView  
ValueAnimator ,androidx.appcompat.widget.AppCompatImageView  ViewConfiguration ,androidx.appcompat.widget.AppCompatImageView  abs ,androidx.appcompat.widget.AppCompatImageView  animateTranslate ,androidx.appcompat.widget.AppCompatImageView  apply ,androidx.appcompat.widget.AppCompatImageView  	baseScale ,androidx.appcompat.widget.AppCompatImageView  cancelAnimations ,androidx.appcompat.widget.AppCompatImageView  captureDrawableDimensions ,androidx.appcompat.widget.AppCompatImageView  coerceIn ,androidx.appcompat.widget.AppCompatImageView  !configureScaleDetectorSensitivity ,androidx.appcompat.widget.AppCompatImageView  currentScale ,androidx.appcompat.widget.AppCompatImageView  dynamicMinScale ,androidx.appcompat.widget.AppCompatImageView  getCurrentScaleFactor ,androidx.appcompat.widget.AppCompatImageView  getDisplayRect ,androidx.appcompat.widget.AppCompatImageView  getDistance ,androidx.appcompat.widget.AppCompatImageView  getValue ,androidx.appcompat.widget.AppCompatImageView  handleCustomScale ,androidx.appcompat.widget.AppCompatImageView  instantCheckBounds ,androidx.appcompat.widget.AppCompatImageView  instantScaleTo ,androidx.appcompat.widget.AppCompatImageView  isAtMinimumScale ,androidx.appcompat.widget.AppCompatImageView  
isInitialized ,androidx.appcompat.widget.AppCompatImageView  	isScaling ,androidx.appcompat.widget.AppCompatImageView  java ,androidx.appcompat.widget.AppCompatImageView  lazy ,androidx.appcompat.widget.AppCompatImageView  let ,androidx.appcompat.widget.AppCompatImageView  matrix ,androidx.appcompat.widget.AppCompatImageView  min ,androidx.appcompat.widget.AppCompatImageView  onDetachedFromWindow ,androidx.appcompat.widget.AppCompatImageView  onSingleTapListener ,androidx.appcompat.widget.AppCompatImageView  
onSizeChanged ,androidx.appcompat.widget.AppCompatImageView  performClick ,androidx.appcompat.widget.AppCompatImageView  post ,androidx.appcompat.widget.AppCompatImageView  provideDelegate ,androidx.appcompat.widget.AppCompatImageView  savedMatrix ,androidx.appcompat.widget.AppCompatImageView  setImageBitmap ,androidx.appcompat.widget.AppCompatImageView  setImageDrawable ,androidx.appcompat.widget.AppCompatImageView  setOnTouchListener ,androidx.appcompat.widget.AppCompatImageView  setupGestureDetectors ,androidx.appcompat.widget.AppCompatImageView  setupTouchListener ,androidx.appcompat.widget.AppCompatImageView  sqrt ,androidx.appcompat.widget.AppCompatImageView  startFlingAnimation ,androidx.appcompat.widget.AppCompatImageView  syncCurrentScale ,androidx.appcompat.widget.AppCompatImageView  updateBitmapDimensions ,androidx.appcompat.widget.AppCompatImageView  updateImageMatrix ,androidx.appcompat.widget.AppCompatImageView  updateMatrix ,androidx.appcompat.widget.AppCompatImageView  Boolean com.touptek.ui  DecelerateInterpolator com.touptek.ui  	Exception com.touptek.ui  Float com.touptek.ui  
FloatArray com.touptek.ui  GestureDetector com.touptek.ui  Int com.touptek.ui  JvmOverloads com.touptek.ui  Log com.touptek.ui  	MAX_SCALE com.touptek.ui  Matrix com.touptek.ui  MotionEvent com.touptek.ui  RectF com.touptek.ui  SCALE_SENSITIVITY com.touptek.ui  ScaleGestureDetector com.touptek.ui  	ScaleType com.touptek.ui  TpImageView com.touptek.ui  Unit com.touptek.ui  
ValueAnimator com.touptek.ui  ViewConfiguration com.touptek.ui  abs com.touptek.ui  apply com.touptek.ui  	baseScale com.touptek.ui  coerceIn com.touptek.ui  currentScale com.touptek.ui  dynamicMinScale com.touptek.ui  getCurrentScaleFactor com.touptek.ui  getValue com.touptek.ui  instantCheckBounds com.touptek.ui  instantScaleTo com.touptek.ui  isAtMinimumScale com.touptek.ui  
isInitialized com.touptek.ui  	isScaling com.touptek.ui  java com.touptek.ui  lazy com.touptek.ui  let com.touptek.ui  matrix com.touptek.ui  min com.touptek.ui  onSingleTapListener com.touptek.ui  provideDelegate com.touptek.ui  savedMatrix com.touptek.ui  sqrt com.touptek.ui  startFlingAnimation com.touptek.ui  syncCurrentScale com.touptek.ui  updateImageMatrix com.touptek.ui  Animator com.touptek.ui.TpImageView  AnimatorListenerAdapter com.touptek.ui.TpImageView  AttributeSet com.touptek.ui.TpImageView  Bitmap com.touptek.ui.TpImageView  Boolean com.touptek.ui.TpImageView  CUSTOM_SCALE_THRESHOLD com.touptek.ui.TpImageView  Context com.touptek.ui.TpImageView  DecelerateInterpolator com.touptek.ui.TpImageView  Drawable com.touptek.ui.TpImageView  	Exception com.touptek.ui.TpImageView  Float com.touptek.ui.TpImageView  
FloatArray com.touptek.ui.TpImageView  GestureDetector com.touptek.ui.TpImageView  Int com.touptek.ui.TpImageView  JvmOverloads com.touptek.ui.TpImageView  Log com.touptek.ui.TpImageView  	MAX_SCALE com.touptek.ui.TpImageView  MIN_SCALE_SPAN com.touptek.ui.TpImageView  Matrix com.touptek.ui.TpImageView  MotionEvent com.touptek.ui.TpImageView  RectF com.touptek.ui.TpImageView  SCALE_SENSITIVITY com.touptek.ui.TpImageView  ScaleGestureDetector com.touptek.ui.TpImageView  	ScaleType com.touptek.ui.TpImageView  SuppressLint com.touptek.ui.TpImageView  Unit com.touptek.ui.TpImageView  
ValueAnimator com.touptek.ui.TpImageView  ViewConfiguration com.touptek.ui.TpImageView  abs com.touptek.ui.TpImageView  animateTranslate com.touptek.ui.TpImageView  apply com.touptek.ui.TpImageView  	baseScale com.touptek.ui.TpImageView  cancelAnimations com.touptek.ui.TpImageView  captureDrawableDimensions com.touptek.ui.TpImageView  coerceIn com.touptek.ui.TpImageView  !configureScaleDetectorSensitivity com.touptek.ui.TpImageView  context com.touptek.ui.TpImageView  currentScale com.touptek.ui.TpImageView  drawable com.touptek.ui.TpImageView  dynamicMinScale com.touptek.ui.TpImageView  
flingAnimator com.touptek.ui.TpImageView  gestureDetector com.touptek.ui.TpImageView  getABS com.touptek.ui.TpImageView  getAPPLY com.touptek.ui.TpImageView  getAbs com.touptek.ui.TpImageView  getApply com.touptek.ui.TpImageView  getCOERCEIn com.touptek.ui.TpImageView  
getCONTEXT com.touptek.ui.TpImageView  getCoerceIn com.touptek.ui.TpImageView  
getContext com.touptek.ui.TpImageView  getCurrentScaleFactor com.touptek.ui.TpImageView  getDRAWABLE com.touptek.ui.TpImageView  getDisplayRect com.touptek.ui.TpImageView  getDistance com.touptek.ui.TpImageView  getDrawable com.touptek.ui.TpImageView  getGETValue com.touptek.ui.TpImageView  getGetValue com.touptek.ui.TpImageView  	getHEIGHT com.touptek.ui.TpImageView  	getHeight com.touptek.ui.TpImageView  getIMAGEMatrix com.touptek.ui.TpImageView  getImageMatrix com.touptek.ui.TpImageView  getLAZY com.touptek.ui.TpImageView  getLET com.touptek.ui.TpImageView  getLazy com.touptek.ui.TpImageView  getLet com.touptek.ui.TpImageView  getMIN com.touptek.ui.TpImageView  getMin com.touptek.ui.TpImageView  getPROVIDEDelegate com.touptek.ui.TpImageView  getProvideDelegate com.touptek.ui.TpImageView  getSCALEType com.touptek.ui.TpImageView  getSQRT com.touptek.ui.TpImageView  getScaleType com.touptek.ui.TpImageView  getSqrt com.touptek.ui.TpImageView  getValue com.touptek.ui.TpImageView  getWIDTH com.touptek.ui.TpImageView  getWidth com.touptek.ui.TpImageView  handleCustomScale com.touptek.ui.TpImageView  height com.touptek.ui.TpImageView  imageHeight com.touptek.ui.TpImageView  imageMatrix com.touptek.ui.TpImageView  
imageWidth com.touptek.ui.TpImageView  instantCheckBounds com.touptek.ui.TpImageView  instantScaleTo com.touptek.ui.TpImageView  isAtMinimumScale com.touptek.ui.TpImageView  isCustomScaling com.touptek.ui.TpImageView  
isInitialized com.touptek.ui.TpImageView  	isScaling com.touptek.ui.TpImageView  
isZoomEnabled com.touptek.ui.TpImageView  java com.touptek.ui.TpImageView  lastDistance com.touptek.ui.TpImageView  
lastFocusX com.touptek.ui.TpImageView  
lastFocusY com.touptek.ui.TpImageView  lazy com.touptek.ui.TpImageView  let com.touptek.ui.TpImageView  matrix com.touptek.ui.TpImageView  matrixChangeListener com.touptek.ui.TpImageView  measurementTouchHandler com.touptek.ui.TpImageView  min com.touptek.ui.TpImageView  onSingleTapListener com.touptek.ui.TpImageView  performClick com.touptek.ui.TpImageView  post com.touptek.ui.TpImageView  provideDelegate com.touptek.ui.TpImageView  savedMatrix com.touptek.ui.TpImageView  
scaleAnimator com.touptek.ui.TpImageView  
scaleDetector com.touptek.ui.TpImageView  	scaleType com.touptek.ui.TpImageView  
setContext com.touptek.ui.TpImageView  setDrawable com.touptek.ui.TpImageView  	setHeight com.touptek.ui.TpImageView  setImageMatrix com.touptek.ui.TpImageView  setOnTouchListener com.touptek.ui.TpImageView  setScaleType com.touptek.ui.TpImageView  setWidth com.touptek.ui.TpImageView  setupGestureDetectors com.touptek.ui.TpImageView  setupTouchListener com.touptek.ui.TpImageView  sqrt com.touptek.ui.TpImageView  startFlingAnimation com.touptek.ui.TpImageView  syncCurrentScale com.touptek.ui.TpImageView  updateBitmapDimensions com.touptek.ui.TpImageView  updateImageMatrix com.touptek.ui.TpImageView  updateMatrix com.touptek.ui.TpImageView  
viewHeight com.touptek.ui.TpImageView  	viewWidth com.touptek.ui.TpImageView  width com.touptek.ui.TpImageView  getSYNCCurrentScale Jcom.touptek.ui.TpImageView.animateTranslate.<anonymous>.<no name provided>  getSyncCurrentScale Jcom.touptek.ui.TpImageView.animateTranslate.<anonymous>.<no name provided>  getBASEScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getBaseScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getCOERCEIn Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getCURRENTScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getCoerceIn Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getCurrentScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getDYNAMICMinScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getDynamicMinScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getGETCurrentScaleFactor Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getGetCurrentScaleFactor Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getINSTANTCheckBounds Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getINSTANTScaleTo Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getISAtMinimumScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getISInitialized Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getISScaling Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getInstantCheckBounds Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getInstantScaleTo Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getIsAtMinimumScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getIsInitialized Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getIsScaling Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  	getMATRIX Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getMIN Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  	getMatrix Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getMin Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getONSingleTapListener Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getOnSingleTapListener Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getSTARTFlingAnimation Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getSYNCCurrentScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getStartFlingAnimation Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getSyncCurrentScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getUPDATEImageMatrix Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getUpdateImageMatrix Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  isAtMinimumScale Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  
isInitialized Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  	isScaling Ccom.touptek.ui.TpImageView.setupGestureDetectors.<no name provided>  getINSTANTCheckBounds Mcom.touptek.ui.TpImageView.startFlingAnimation.<anonymous>.<no name provided>  getInstantCheckBounds Mcom.touptek.ui.TpImageView.startFlingAnimation.<anonymous>.<no name provided>  getSYNCCurrentScale Mcom.touptek.ui.TpImageView.startFlingAnimation.<anonymous>.<no name provided>  getSyncCurrentScale Mcom.touptek.ui.TpImageView.startFlingAnimation.<anonymous>.<no name provided>  DecelerateInterpolator 	java.lang  	Exception 	java.lang  
FloatArray 	java.lang  GestureDetector 	java.lang  Log 	java.lang  	MAX_SCALE 	java.lang  Matrix 	java.lang  MotionEvent 	java.lang  RectF 	java.lang  SCALE_SENSITIVITY 	java.lang  ScaleGestureDetector 	java.lang  	ScaleType 	java.lang  
ValueAnimator 	java.lang  ViewConfiguration 	java.lang  abs 	java.lang  apply 	java.lang  	baseScale 	java.lang  coerceIn 	java.lang  currentScale 	java.lang  dynamicMinScale 	java.lang  getCurrentScaleFactor 	java.lang  getValue 	java.lang  instantCheckBounds 	java.lang  instantScaleTo 	java.lang  isAtMinimumScale 	java.lang  
isInitialized 	java.lang  	isScaling 	java.lang  java 	java.lang  lazy 	java.lang  let 	java.lang  matrix 	java.lang  min 	java.lang  onSingleTapListener 	java.lang  provideDelegate 	java.lang  savedMatrix 	java.lang  sqrt 	java.lang  startFlingAnimation 	java.lang  syncCurrentScale 	java.lang  updateImageMatrix 	java.lang  getDeclaredField java.lang.Class  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  Field java.lang.reflect  setInt "java.lang.reflect.AccessibleObject  getISAccessible java.lang.reflect.Field  getIsAccessible java.lang.reflect.Field  isAccessible java.lang.reflect.Field  
setAccessible java.lang.reflect.Field  setInt java.lang.reflect.Field  Any kotlin  Boolean kotlin  DecelerateInterpolator kotlin  	Exception kotlin  Float kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  GestureDetector kotlin  Int kotlin  JvmOverloads kotlin  Lazy kotlin  Log kotlin  Long kotlin  	MAX_SCALE kotlin  Matrix kotlin  MotionEvent kotlin  Nothing kotlin  RectF kotlin  SCALE_SENSITIVITY kotlin  ScaleGestureDetector kotlin  	ScaleType kotlin  String kotlin  Unit kotlin  
ValueAnimator kotlin  ViewConfiguration kotlin  abs kotlin  apply kotlin  	baseScale kotlin  coerceIn kotlin  currentScale kotlin  dynamicMinScale kotlin  getCurrentScaleFactor kotlin  getValue kotlin  instantCheckBounds kotlin  instantScaleTo kotlin  isAtMinimumScale kotlin  
isInitialized kotlin  	isScaling kotlin  java kotlin  lazy kotlin  let kotlin  matrix kotlin  min kotlin  onSingleTapListener kotlin  provideDelegate kotlin  savedMatrix kotlin  sqrt kotlin  startFlingAnimation kotlin  syncCurrentScale kotlin  updateImageMatrix kotlin  getCOERCEIn kotlin.Float  getCoerceIn kotlin.Float  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  DecelerateInterpolator kotlin.annotation  	Exception kotlin.annotation  
FloatArray kotlin.annotation  GestureDetector kotlin.annotation  JvmOverloads kotlin.annotation  Log kotlin.annotation  	MAX_SCALE kotlin.annotation  Matrix kotlin.annotation  MotionEvent kotlin.annotation  RectF kotlin.annotation  SCALE_SENSITIVITY kotlin.annotation  ScaleGestureDetector kotlin.annotation  	ScaleType kotlin.annotation  
ValueAnimator kotlin.annotation  ViewConfiguration kotlin.annotation  abs kotlin.annotation  apply kotlin.annotation  	baseScale kotlin.annotation  coerceIn kotlin.annotation  currentScale kotlin.annotation  dynamicMinScale kotlin.annotation  getCurrentScaleFactor kotlin.annotation  getValue kotlin.annotation  instantCheckBounds kotlin.annotation  instantScaleTo kotlin.annotation  isAtMinimumScale kotlin.annotation  
isInitialized kotlin.annotation  	isScaling kotlin.annotation  java kotlin.annotation  lazy kotlin.annotation  let kotlin.annotation  matrix kotlin.annotation  min kotlin.annotation  onSingleTapListener kotlin.annotation  provideDelegate kotlin.annotation  savedMatrix kotlin.annotation  sqrt kotlin.annotation  startFlingAnimation kotlin.annotation  syncCurrentScale kotlin.annotation  updateImageMatrix kotlin.annotation  DecelerateInterpolator kotlin.collections  	Exception kotlin.collections  
FloatArray kotlin.collections  GestureDetector kotlin.collections  JvmOverloads kotlin.collections  Log kotlin.collections  	MAX_SCALE kotlin.collections  Matrix kotlin.collections  MotionEvent kotlin.collections  RectF kotlin.collections  SCALE_SENSITIVITY kotlin.collections  ScaleGestureDetector kotlin.collections  	ScaleType kotlin.collections  
ValueAnimator kotlin.collections  ViewConfiguration kotlin.collections  abs kotlin.collections  apply kotlin.collections  	baseScale kotlin.collections  coerceIn kotlin.collections  currentScale kotlin.collections  dynamicMinScale kotlin.collections  getCurrentScaleFactor kotlin.collections  getValue kotlin.collections  instantCheckBounds kotlin.collections  instantScaleTo kotlin.collections  isAtMinimumScale kotlin.collections  
isInitialized kotlin.collections  	isScaling kotlin.collections  java kotlin.collections  lazy kotlin.collections  let kotlin.collections  matrix kotlin.collections  min kotlin.collections  onSingleTapListener kotlin.collections  provideDelegate kotlin.collections  savedMatrix kotlin.collections  sqrt kotlin.collections  startFlingAnimation kotlin.collections  syncCurrentScale kotlin.collections  updateImageMatrix kotlin.collections  DecelerateInterpolator kotlin.comparisons  	Exception kotlin.comparisons  
FloatArray kotlin.comparisons  GestureDetector kotlin.comparisons  JvmOverloads kotlin.comparisons  Log kotlin.comparisons  	MAX_SCALE kotlin.comparisons  Matrix kotlin.comparisons  MotionEvent kotlin.comparisons  RectF kotlin.comparisons  SCALE_SENSITIVITY kotlin.comparisons  ScaleGestureDetector kotlin.comparisons  	ScaleType kotlin.comparisons  
ValueAnimator kotlin.comparisons  ViewConfiguration kotlin.comparisons  abs kotlin.comparisons  apply kotlin.comparisons  	baseScale kotlin.comparisons  coerceIn kotlin.comparisons  currentScale kotlin.comparisons  dynamicMinScale kotlin.comparisons  getCurrentScaleFactor kotlin.comparisons  getValue kotlin.comparisons  instantCheckBounds kotlin.comparisons  instantScaleTo kotlin.comparisons  isAtMinimumScale kotlin.comparisons  
isInitialized kotlin.comparisons  	isScaling kotlin.comparisons  java kotlin.comparisons  lazy kotlin.comparisons  let kotlin.comparisons  matrix kotlin.comparisons  min kotlin.comparisons  onSingleTapListener kotlin.comparisons  provideDelegate kotlin.comparisons  savedMatrix kotlin.comparisons  sqrt kotlin.comparisons  startFlingAnimation kotlin.comparisons  syncCurrentScale kotlin.comparisons  updateImageMatrix kotlin.comparisons  DecelerateInterpolator 	kotlin.io  	Exception 	kotlin.io  
FloatArray 	kotlin.io  GestureDetector 	kotlin.io  JvmOverloads 	kotlin.io  Log 	kotlin.io  	MAX_SCALE 	kotlin.io  Matrix 	kotlin.io  MotionEvent 	kotlin.io  RectF 	kotlin.io  SCALE_SENSITIVITY 	kotlin.io  ScaleGestureDetector 	kotlin.io  	ScaleType 	kotlin.io  
ValueAnimator 	kotlin.io  ViewConfiguration 	kotlin.io  abs 	kotlin.io  apply 	kotlin.io  	baseScale 	kotlin.io  coerceIn 	kotlin.io  currentScale 	kotlin.io  dynamicMinScale 	kotlin.io  getCurrentScaleFactor 	kotlin.io  getValue 	kotlin.io  instantCheckBounds 	kotlin.io  instantScaleTo 	kotlin.io  isAtMinimumScale 	kotlin.io  
isInitialized 	kotlin.io  	isScaling 	kotlin.io  java 	kotlin.io  lazy 	kotlin.io  let 	kotlin.io  matrix 	kotlin.io  min 	kotlin.io  onSingleTapListener 	kotlin.io  provideDelegate 	kotlin.io  savedMatrix 	kotlin.io  sqrt 	kotlin.io  startFlingAnimation 	kotlin.io  syncCurrentScale 	kotlin.io  updateImageMatrix 	kotlin.io  DecelerateInterpolator 
kotlin.jvm  	Exception 
kotlin.jvm  
FloatArray 
kotlin.jvm  GestureDetector 
kotlin.jvm  JvmOverloads 
kotlin.jvm  Log 
kotlin.jvm  	MAX_SCALE 
kotlin.jvm  Matrix 
kotlin.jvm  MotionEvent 
kotlin.jvm  RectF 
kotlin.jvm  SCALE_SENSITIVITY 
kotlin.jvm  ScaleGestureDetector 
kotlin.jvm  	ScaleType 
kotlin.jvm  
ValueAnimator 
kotlin.jvm  ViewConfiguration 
kotlin.jvm  abs 
kotlin.jvm  apply 
kotlin.jvm  	baseScale 
kotlin.jvm  coerceIn 
kotlin.jvm  currentScale 
kotlin.jvm  dynamicMinScale 
kotlin.jvm  getCurrentScaleFactor 
kotlin.jvm  getValue 
kotlin.jvm  instantCheckBounds 
kotlin.jvm  instantScaleTo 
kotlin.jvm  isAtMinimumScale 
kotlin.jvm  
isInitialized 
kotlin.jvm  	isScaling 
kotlin.jvm  java 
kotlin.jvm  lazy 
kotlin.jvm  let 
kotlin.jvm  matrix 
kotlin.jvm  min 
kotlin.jvm  onSingleTapListener 
kotlin.jvm  provideDelegate 
kotlin.jvm  savedMatrix 
kotlin.jvm  sqrt 
kotlin.jvm  startFlingAnimation 
kotlin.jvm  syncCurrentScale 
kotlin.jvm  updateImageMatrix 
kotlin.jvm  DecelerateInterpolator kotlin.math  	Exception kotlin.math  
FloatArray kotlin.math  GestureDetector kotlin.math  JvmOverloads kotlin.math  Log kotlin.math  	MAX_SCALE kotlin.math  Matrix kotlin.math  MotionEvent kotlin.math  RectF kotlin.math  SCALE_SENSITIVITY kotlin.math  ScaleGestureDetector kotlin.math  	ScaleType kotlin.math  
ValueAnimator kotlin.math  ViewConfiguration kotlin.math  abs kotlin.math  apply kotlin.math  	baseScale kotlin.math  coerceIn kotlin.math  currentScale kotlin.math  dynamicMinScale kotlin.math  getCurrentScaleFactor kotlin.math  getValue kotlin.math  instantCheckBounds kotlin.math  instantScaleTo kotlin.math  isAtMinimumScale kotlin.math  
isInitialized kotlin.math  	isScaling kotlin.math  java kotlin.math  lazy kotlin.math  let kotlin.math  matrix kotlin.math  min kotlin.math  onSingleTapListener kotlin.math  provideDelegate kotlin.math  savedMatrix kotlin.math  sqrt kotlin.math  startFlingAnimation kotlin.math  syncCurrentScale kotlin.math  updateImageMatrix kotlin.math  DecelerateInterpolator 
kotlin.ranges  	Exception 
kotlin.ranges  
FloatArray 
kotlin.ranges  GestureDetector 
kotlin.ranges  JvmOverloads 
kotlin.ranges  Log 
kotlin.ranges  	MAX_SCALE 
kotlin.ranges  Matrix 
kotlin.ranges  MotionEvent 
kotlin.ranges  RectF 
kotlin.ranges  SCALE_SENSITIVITY 
kotlin.ranges  ScaleGestureDetector 
kotlin.ranges  	ScaleType 
kotlin.ranges  
ValueAnimator 
kotlin.ranges  ViewConfiguration 
kotlin.ranges  abs 
kotlin.ranges  apply 
kotlin.ranges  	baseScale 
kotlin.ranges  coerceIn 
kotlin.ranges  currentScale 
kotlin.ranges  dynamicMinScale 
kotlin.ranges  getCurrentScaleFactor 
kotlin.ranges  getValue 
kotlin.ranges  instantCheckBounds 
kotlin.ranges  instantScaleTo 
kotlin.ranges  isAtMinimumScale 
kotlin.ranges  
isInitialized 
kotlin.ranges  	isScaling 
kotlin.ranges  java 
kotlin.ranges  lazy 
kotlin.ranges  let 
kotlin.ranges  matrix 
kotlin.ranges  min 
kotlin.ranges  onSingleTapListener 
kotlin.ranges  provideDelegate 
kotlin.ranges  savedMatrix 
kotlin.ranges  sqrt 
kotlin.ranges  startFlingAnimation 
kotlin.ranges  syncCurrentScale 
kotlin.ranges  updateImageMatrix 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  DecelerateInterpolator kotlin.sequences  	Exception kotlin.sequences  
FloatArray kotlin.sequences  GestureDetector kotlin.sequences  JvmOverloads kotlin.sequences  Log kotlin.sequences  	MAX_SCALE kotlin.sequences  Matrix kotlin.sequences  MotionEvent kotlin.sequences  RectF kotlin.sequences  SCALE_SENSITIVITY kotlin.sequences  ScaleGestureDetector kotlin.sequences  	ScaleType kotlin.sequences  
ValueAnimator kotlin.sequences  ViewConfiguration kotlin.sequences  abs kotlin.sequences  apply kotlin.sequences  	baseScale kotlin.sequences  coerceIn kotlin.sequences  currentScale kotlin.sequences  dynamicMinScale kotlin.sequences  getCurrentScaleFactor kotlin.sequences  getValue kotlin.sequences  instantCheckBounds kotlin.sequences  instantScaleTo kotlin.sequences  isAtMinimumScale kotlin.sequences  
isInitialized kotlin.sequences  	isScaling kotlin.sequences  java kotlin.sequences  lazy kotlin.sequences  let kotlin.sequences  matrix kotlin.sequences  min kotlin.sequences  onSingleTapListener kotlin.sequences  provideDelegate kotlin.sequences  savedMatrix kotlin.sequences  sqrt kotlin.sequences  startFlingAnimation kotlin.sequences  syncCurrentScale kotlin.sequences  updateImageMatrix kotlin.sequences  DecelerateInterpolator kotlin.text  	Exception kotlin.text  
FloatArray kotlin.text  GestureDetector kotlin.text  JvmOverloads kotlin.text  Log kotlin.text  	MAX_SCALE kotlin.text  Matrix kotlin.text  MotionEvent kotlin.text  RectF kotlin.text  SCALE_SENSITIVITY kotlin.text  ScaleGestureDetector kotlin.text  	ScaleType kotlin.text  
ValueAnimator kotlin.text  ViewConfiguration kotlin.text  abs kotlin.text  apply kotlin.text  	baseScale kotlin.text  coerceIn kotlin.text  currentScale kotlin.text  dynamicMinScale kotlin.text  getCurrentScaleFactor kotlin.text  getValue kotlin.text  instantCheckBounds kotlin.text  instantScaleTo kotlin.text  isAtMinimumScale kotlin.text  
isInitialized kotlin.text  	isScaling kotlin.text  java kotlin.text  lazy kotlin.text  let kotlin.text  matrix kotlin.text  min kotlin.text  onSingleTapListener kotlin.text  provideDelegate kotlin.text  savedMatrix kotlin.text  sqrt kotlin.text  startFlingAnimation kotlin.text  syncCurrentScale kotlin.text  updateImageMatrix kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          