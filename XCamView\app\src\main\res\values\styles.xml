<resources>
    <!-- Base application theme. -->
    <style name="FullScreenDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>



    //
    <style name="ModernSettingTab">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:background">@drawable/grey_background</item>
    </style>

    <!-- 操作按钮样式 -->
    <style name="ModernActionButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:textColor">@color/white_background</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="backgroundTint">@color/gray_text_disabled</item>
    </style>

</resources>
