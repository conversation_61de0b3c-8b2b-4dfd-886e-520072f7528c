<!-- res/layout/fragment_format_settings.xml -->
<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:divider="@drawable/divider"
        android:showDividers="middle">
        <!-- 图片格式设置 - 带边框的区块 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="模式选择"
                android:textSize="18sp"
                android:textStyle="bold"/>             <!-- 加粗标题 -->

            <RadioGroup
                android:id="@+id/mode_radio_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <RadioButton
                    android:id="@+id/radio_A"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="自动测量"/>

                <RadioButton
                    android:id="@+id/radio_B"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="手动测量"/>
            </RadioGroup>
        </LinearLayout>


    </LinearLayout>


</ScrollView>