{"logs": [{"outputFile": "com.android.rockchip.mediacodecnew.app-mergeDebugResources-35:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5ee0973f6a00ce88be66ccc82fc087bc\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,9488", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,9564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c629d2bab069b69ff70afa7604a76b8d\\transformed\\core-1.13.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3433,3527,3630,3727,3829,3931,4029,9803", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3522,3625,3722,3824,3926,4024,4146,9899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\737b03f0fc1ad0fd308d8e116bf71918\\transformed\\material-1.12.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1060,1123,1214,1281,1340,1430,1493,1558,1622,1691,1753,1807,1922,1980,2041,2095,2168,2295,2381,2463,2562,2647,2731,2864,2939,3015,3148,3234,3315,3369,3421,3487,3560,3640,3711,3791,3862,3938,4017,4086,4193,4289,4367,4462,4558,4632,4707,4806,4857,4939,5006,5093,5183,5245,5309,5372,5439,5541,5646,5743,5845,5903,5959,6037,6123,6198", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77,85,74,72", "endOffsets": "263,337,409,491,597,695,794,914,998,1055,1118,1209,1276,1335,1425,1488,1553,1617,1686,1748,1802,1917,1975,2036,2090,2163,2290,2376,2458,2557,2642,2726,2859,2934,3010,3143,3229,3310,3364,3416,3482,3555,3635,3706,3786,3857,3933,4012,4081,4188,4284,4362,4457,4553,4627,4702,4801,4852,4934,5001,5088,5178,5240,5304,5367,5434,5536,5641,5738,5840,5898,5954,6032,6118,6193,6266"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3001,3075,3147,3229,3335,4151,4250,4370,4454,4511,4574,4665,4732,4791,4881,4944,5009,5073,5142,5204,5258,5373,5431,5492,5546,5619,5746,5832,5914,6013,6098,6182,6315,6390,6466,6599,6685,6766,6820,6872,6938,7011,7091,7162,7242,7313,7389,7468,7537,7644,7740,7818,7913,8009,8083,8158,8257,8308,8390,8457,8544,8634,8696,8760,8823,8890,8992,9097,9194,9296,9354,9410,9569,9655,9730", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77,85,74,72", "endOffsets": "313,3070,3142,3224,3330,3428,4245,4365,4449,4506,4569,4660,4727,4786,4876,4939,5004,5068,5137,5199,5253,5368,5426,5487,5541,5614,5741,5827,5909,6008,6093,6177,6310,6385,6461,6594,6680,6761,6815,6867,6933,7006,7086,7157,7237,7308,7384,7463,7532,7639,7735,7813,7908,8004,8078,8153,8252,8303,8385,8452,8539,8629,8691,8755,8818,8885,8987,9092,9189,9291,9349,9405,9483,9650,9725,9798"}}]}]}