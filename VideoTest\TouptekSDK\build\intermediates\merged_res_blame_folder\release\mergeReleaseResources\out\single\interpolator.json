[{"merged": "com.touptek.TouptekSDK-release-35:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml", "source": "com.touptek.TouptekSDK-appcompat-1.6.1-30:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml", "source": "com.touptek.TouptekSDK-appcompat-1.6.1-30:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml", "source": "com.touptek.TouptekSDK-appcompat-1.6.1-30:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml", "source": "com.touptek.TouptekSDK-appcompat-1.6.1-30:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/interpolator/mtrl_linear.xml", "source": "com.touptek.TouptekSDK-material-1.10.0-15:/interpolator/mtrl_linear.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml", "source": "com.touptek.TouptekSDK-appcompat-1.6.1-30:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml", "source": "com.touptek.TouptekSDK-appcompat-1.6.1-30:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/interpolator/fast_out_slow_in.xml", "source": "com.touptek.TouptekSDK-appcompat-1.6.1-30:/interpolator/fast_out_slow_in.xml"}]