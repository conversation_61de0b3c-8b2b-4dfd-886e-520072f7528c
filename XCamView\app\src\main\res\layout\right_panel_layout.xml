<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#FFFFFFFF"
    android:padding="8dp"
    android:showDividers="middle">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical|center"
        android:padding="0dp"
        android:minHeight="40dp">

        <!-- 在顶部操作按钮栏内添加 -->

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:backgroundTint="#00000000"
            android:contentDescription="返回"
            android:scaleType="centerInside"
            android:src="@drawable/home_n"
            android:padding="4dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"/>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1"/>

        <ImageButton
            android:id="@+id/btn_create_folder"
            android:layout_width="44dp"
            android:backgroundTint="#00000000"
            android:contentDescription="创建文件夹"
            android:padding="2dp"
            android:layout_weight="0"
            android:scaleType="centerInside"
            android:src="@drawable/add_n"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="0dp"
            android:layout_height="match_parent"/>

    </LinearLayout>

    <!-- 文件夹列表分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#808080"
        android:layout_marginVertical="4dp"/>

    <!-- 文件夹标题和新建标签容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <!-- 文件夹标题 -->
        <TextView
            android:id="@+id/tv_folder_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="U盘文件夹"
            android:textColor="#FF000000"
            android:textSize="16sp"
            android:textStyle="bold"/>

        <!-- 新建标签 -->
<!--        <TextView-->
<!--            android:id="@+id/tv_new_folder"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="+"-->
<!--            android:textColor="#FF0078D7"-->
<!--            android:textSize="40sp"-->
<!--        android:drawablePadding="4dp"-->
<!--        android:padding="2dp"-->
<!--        android:clickable="true"-->
<!--        android:focusable="true"-->
<!--        android:background="?android:attr/selectableItemBackground"/>-->
    </LinearLayout>

    <!-- 文件夹列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_folder_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="vertical"/>
</LinearLayout>