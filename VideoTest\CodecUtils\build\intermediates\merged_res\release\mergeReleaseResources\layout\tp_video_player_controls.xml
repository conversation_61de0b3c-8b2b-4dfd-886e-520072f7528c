<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/tp_video_controls_background"
    android:paddingStart="20dp"
    android:paddingEnd="20dp"
    android:paddingTop="16dp"
    android:paddingBottom="20dp"
    android:layout_margin="12dp">

    <!-- 进度条区域 - 优化视觉效果 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:layout_marginHorizontal="12dp"
        android:gravity="center_vertical"
        android:paddingVertical="8dp">

        <!-- 当前时间 -->
        <TextView
            android:id="@+id/tv_current_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="#FFFFFFFF"
            android:textSize="12sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2"
            android:minWidth="40dp"
            android:gravity="center"
            android:layout_marginEnd="8dp" />

        <!-- 自定义现代化进度条 - 优化动画效果 -->
        <com.android.rockchip.camera2.view.TpCustomProgressBar
            android:id="@+id/seekbar_progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingTop="8dp"
            android:paddingBottom="8dp" />

        <!-- 总时间 -->
        <TextView
            android:id="@+id/tv_total_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="#FFFFFFFF"
            android:textSize="12sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2"
            android:minWidth="40dp"
            android:gravity="center"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- 优化的现代化底部控制按钮行 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:paddingHorizontal="12dp"
        android:paddingVertical="8dp">

        <!-- 上一个视频按钮 -->
        <ImageButton
            android:id="@+id/btn_previous_video"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/tp_video_button_background"
            android:src="@drawable/ic_skip_previous_white_24"
            android:scaleType="center"
            android:elevation="4dp"
            android:stateListAnimator="@null"
            android:contentDescription="上一个视频" />

        <!-- 播放/暂停按钮（特殊强调） -->
        <ImageButton
            android:id="@+id/btn_play_pause"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/tp_video_play_button_background"
            android:src="@drawable/ic_play_arrow_white_24"
            android:scaleType="center"
            android:elevation="6dp"
            android:stateListAnimator="@null"
            android:contentDescription="播放/暂停" />

        <!-- 下一个视频按钮 -->
        <ImageButton
            android:id="@+id/btn_next_video"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/tp_video_button_background"
            android:src="@drawable/ic_skip_next_white_24"
            android:scaleType="center"
            android:elevation="4dp"
            android:stateListAnimator="@null"
            android:contentDescription="下一个视频" />

        <!-- 逐帧播放按钮 -->
        <ImageButton
            android:id="@+id/btn_step_frame"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/tp_video_button_background"
            android:src="@drawable/ic_step_frame_white_24"
            android:scaleType="center"
            android:elevation="4dp"
            android:stateListAnimator="@null"
            android:contentDescription="逐帧播放" />

        <!-- 快退按钮 -->
        <ImageButton
            android:id="@+id/btn_rewind"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/tp_video_button_background"
            android:src="@drawable/ic_fast_rewind_white_24"
            android:scaleType="center"
            android:elevation="4dp"
            android:stateListAnimator="@null"
            android:contentDescription="快退" />

        <!-- 快进按钮 -->
        <ImageButton
            android:id="@+id/btn_forward"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/tp_video_button_background"
            android:src="@drawable/ic_fast_forward_white_24"
            android:scaleType="center"
            android:elevation="4dp"
            android:stateListAnimator="@null"
            android:contentDescription="快进" />

        <!-- 设置按钮（圆形特殊样式） -->
        <ImageButton
            android:id="@+id/btn_settings"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:background="@drawable/tp_video_settings_button_background"
            android:src="@drawable/ic_settings_white_24"
            android:scaleType="center"
            android:elevation="4dp"
            android:stateListAnimator="@null"
            android:contentDescription="设置" />

    </LinearLayout>

</LinearLayout>
