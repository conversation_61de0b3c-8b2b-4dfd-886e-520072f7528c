<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2021 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
  <item
      android:left="2dp"
      android:right="2dp">
    <shape
        android:shape="rectangle">
      <!-- Color is assigned programmatically with the value of "tabIndicatorColor". -->
      <solid android:color="@android:color/white"/>
      <corners
          android:bottomLeftRadius="0dp"
          android:bottomRightRadius="0dp"
          android:topLeftRadius="3dp"
          android:topRightRadius="3dp"/>
      <size android:height="@dimen/m3_comp_primary_navigation_tab_active_indicator_height"/>
    </shape>
  </item>
</layer-list>
