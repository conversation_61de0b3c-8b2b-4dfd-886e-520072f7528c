{"logs": [{"outputFile": "com.android.rockchip.mediacodecnew.app-mergeDebugResources-36:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c629d2bab069b69ff70afa7604a76b8d\\transformed\\core-1.13.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3435,3530,3632,3729,3826,3932,4050,9806", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3525,3627,3724,3821,3927,4045,4160,9902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5ee0973f6a00ce88be66ccc82fc087bc\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,646,733,837,953,1036,1114,1205,1298,1393,1487,1587,1680,1775,1869,1960,2051,2137,2240,2345,2446,2550,2659,2767,2927,9484", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,641,728,832,948,1031,1109,1200,1293,1388,1482,1582,1675,1770,1864,1955,2046,2132,2235,2340,2441,2545,2654,2762,2922,3021,9564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\737b03f0fc1ad0fd308d8e116bf71918\\transformed\\material-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1047,1112,1206,1271,1330,1417,1479,1541,1601,1667,1729,1783,1895,1952,2013,2067,2139,2265,2351,2429,2522,2608,2692,2831,2912,2993,3128,3218,3300,3353,3405,3471,3543,3627,3698,3778,3853,3929,4002,4077,4175,4260,4335,4427,4521,4595,4668,4762,4814,4896,4965,5050,5137,5199,5263,5326,5398,5501,5606,5701,5804,5861,5917,5997,6078,6156", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "264,343,419,498,588,673,779,895,978,1042,1107,1201,1266,1325,1412,1474,1536,1596,1662,1724,1778,1890,1947,2008,2062,2134,2260,2346,2424,2517,2603,2687,2826,2907,2988,3123,3213,3295,3348,3400,3466,3538,3622,3693,3773,3848,3924,3997,4072,4170,4255,4330,4422,4516,4590,4663,4757,4809,4891,4960,5045,5132,5194,5258,5321,5393,5496,5601,5696,5799,5856,5912,5992,6073,6151,6229"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3105,3181,3260,3350,4165,4271,4387,4470,4534,4599,4693,4758,4817,4904,4966,5028,5088,5154,5216,5270,5382,5439,5500,5554,5626,5752,5838,5916,6009,6095,6179,6318,6399,6480,6615,6705,6787,6840,6892,6958,7030,7114,7185,7265,7340,7416,7489,7564,7662,7747,7822,7914,8008,8082,8155,8249,8301,8383,8452,8537,8624,8686,8750,8813,8885,8988,9093,9188,9291,9348,9404,9569,9650,9728", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "314,3100,3176,3255,3345,3430,4266,4382,4465,4529,4594,4688,4753,4812,4899,4961,5023,5083,5149,5211,5265,5377,5434,5495,5549,5621,5747,5833,5911,6004,6090,6174,6313,6394,6475,6610,6700,6782,6835,6887,6953,7025,7109,7180,7260,7335,7411,7484,7559,7657,7742,7817,7909,8003,8077,8150,8244,8296,8378,8447,8532,8619,8681,8745,8808,8880,8983,9088,9183,9286,9343,9399,9479,9645,9723,9801"}}]}]}