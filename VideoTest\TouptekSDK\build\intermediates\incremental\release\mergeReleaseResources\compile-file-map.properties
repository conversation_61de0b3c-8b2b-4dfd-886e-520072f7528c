#Wed Jul 30 09:49:23 CST 2025
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_fade_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_fade_in.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_fade_out.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_fade_out.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_grow_fade_in_from_bottom.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_grow_fade_in_from_bottom.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_popup_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_popup_enter.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_popup_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_popup_exit.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_shrink_fade_out_from_bottom.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_shrink_fade_out_from_bottom.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_slide_in_bottom.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_slide_in_bottom.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_slide_in_top.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_slide_in_top.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_slide_out_bottom.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_slide_out_bottom.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_slide_out_top.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_slide_out_top.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_tooltip_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_tooltip_enter.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/abc_tooltip_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\abc_tooltip_exit.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_checkbox_to_checked_box_inner_merged_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_checkbox_to_checked_box_outer_merged_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_checkbox_to_checked_icon_null_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_checkbox_to_checked_icon_null_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_checkbox_to_unchecked_box_inner_merged_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_checkbox_to_unchecked_check_path_merged_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_checkbox_to_unchecked_icon_null_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_radio_to_off_mtrl_dot_group_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_radio_to_off_mtrl_ring_outer_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_radio_to_off_mtrl_ring_outer_path_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_radio_to_on_mtrl_dot_group_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_radio_to_on_mtrl_ring_outer_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\btn_radio_to_on_mtrl_ring_outer_path_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_btn_colored_borderless_text_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_btn_colored_borderless_text_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_btn_colored_text_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_btn_colored_text_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_color_highlight_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_color_highlight_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_tint_btn_checkable.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_tint_btn_checkable.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_tint_default.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_tint_default.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_tint_edittext.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_tint_edittext.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_tint_seek_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_tint_seek_thumb.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_tint_spinner.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_tint_spinner.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color-v23/abc_tint_switch_track.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v23\\abc_tint_switch_track.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_background_cache_hint_selector_material_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_background_cache_hint_selector_material_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_background_cache_hint_selector_material_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_background_cache_hint_selector_material_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_hint_foreground_material_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_hint_foreground_material_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_hint_foreground_material_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_hint_foreground_material_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_primary_text_disable_only_material_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_primary_text_disable_only_material_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_primary_text_disable_only_material_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_primary_text_disable_only_material_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_primary_text_material_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_primary_text_material_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_primary_text_material_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_primary_text_material_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_search_url_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_search_url_text.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_secondary_text_material_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_secondary_text_material_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/abc_secondary_text_material_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\abc_secondary_text_material_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/switch_thumb_material_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\switch_thumb_material_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/color/switch_thumb_material_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\switch_thumb_material_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_btn_check_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_btn_check_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_list_divider_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_list_focused_holo.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_list_focused_holo.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_list_longpressed_holo.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_list_longpressed_holo.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_list_pressed_holo_dark.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_list_pressed_holo_light.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_list_selector_disabled_holo_dark.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_list_selector_disabled_holo_light.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_popup_background_mtrl_mult.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_switch_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_text_select_handle_left_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_text_select_handle_middle_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_text_select_handle_middle_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_text_select_handle_right_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_textfield_default_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-hdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-ldrtl-hdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-hdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-ldrtl-mdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-mdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-ldrtl-xhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-ldrtl-xxhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-ldrtl-xxxhdpi-v17/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldrtl-xxxhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_list_divider_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_list_focused_holo.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_list_focused_holo.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_list_longpressed_holo.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_list_longpressed_holo.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_list_pressed_holo_dark.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_list_pressed_holo_light.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_dark.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_light.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_popup_background_mtrl_mult.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_switch_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_text_select_handle_left_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_text_select_handle_middle_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_text_select_handle_middle_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_text_select_handle_right_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_textfield_default_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-mdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-v21/abc_action_bar_item_background_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\abc_action_bar_item_background_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-v21/abc_btn_colored_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\abc_btn_colored_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-v21/abc_dialog_material_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\abc_dialog_material_background.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-v21/abc_edit_text_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\abc_edit_text_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-v21/abc_list_divider_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\abc_list_divider_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-v23/abc_control_background_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23\\abc_control_background_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-watch-v20/abc_dialog_material_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-watch-v20\\abc_dialog_material_background.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_btn_check_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_btn_check_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_list_divider_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_list_focused_holo.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_list_focused_holo.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_list_longpressed_holo.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_list_longpressed_holo.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_list_pressed_holo_dark.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_list_pressed_holo_light.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_list_selector_disabled_holo_dark.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_list_selector_disabled_holo_light.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_popup_background_mtrl_mult.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_switch_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_text_select_handle_left_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_text_select_handle_middle_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_text_select_handle_middle_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_text_select_handle_right_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_textfield_default_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xhdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_ab_share_pack_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_cab_background_top_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_ic_commit_search_api_mtrl_alpha.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_list_divider_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_list_divider_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_list_focused_holo.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_list_focused_holo.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_list_longpressed_holo.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_list_longpressed_holo.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_list_pressed_holo_dark.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_list_pressed_holo_dark.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_list_pressed_holo_light.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_list_pressed_holo_light.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_list_selector_disabled_holo_dark.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_list_selector_disabled_holo_dark.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_list_selector_disabled_holo_light.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_list_selector_disabled_holo_light.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_menu_hardkey_panel_mtrl_mult.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_popup_background_mtrl_mult.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_popup_background_mtrl_mult.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_scrubber_control_off_mtrl_alpha.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_scrubber_primary_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_scrubber_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_text_select_handle_left_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_text_select_handle_middle_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_text_select_handle_middle_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_text_select_handle_right_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_textfield_activated_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_textfield_default_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_textfield_default_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_textfield_search_activated_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxhdpi-v4/abc_textfield_search_default_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_btn_check_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_btn_check_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_btn_radio_to_on_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_btn_radio_to_on_mtrl_015.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_btn_switch_to_on_mtrl_00001.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_btn_switch_to_on_mtrl_00012.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_000.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_scrubber_control_to_pressed_mtrl_005.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_spinner_mtrl_am_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_switch_track_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_tab_indicator_mtrl_alpha.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_text_select_handle_left_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_text_select_handle_left_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable-xxxhdpi-v4/abc_text_select_handle_right_mtrl.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\abc_text_select_handle_right_mtrl.png
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_btn_borderless_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_btn_borderless_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_btn_check_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_btn_check_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_btn_check_material_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_btn_check_material_anim.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_btn_default_mtrl_shape.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_btn_default_mtrl_shape.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_btn_radio_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_btn_radio_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_btn_radio_material_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_btn_radio_material_anim.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_cab_background_internal_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_cab_background_internal_bg.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_cab_background_top_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_cab_background_top_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_ab_back_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_ab_back_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_arrow_drop_right_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_arrow_drop_right_black_24dp.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_clear_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_clear_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_go_search_api_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_go_search_api_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_menu_copy_mtrl_am_alpha.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_menu_copy_mtrl_am_alpha.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_menu_cut_mtrl_alpha.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_menu_cut_mtrl_alpha.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_menu_overflow_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_menu_overflow_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_menu_paste_mtrl_am_alpha.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_menu_paste_mtrl_am_alpha.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_menu_selectall_mtrl_alpha.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_menu_selectall_mtrl_alpha.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_menu_share_mtrl_alpha.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_menu_share_mtrl_alpha.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_search_api_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_search_api_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ic_voice_search_api_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ic_voice_search_api_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_item_background_holo_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_item_background_holo_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_item_background_holo_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_item_background_holo_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_list_selector_background_transition_holo_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_list_selector_background_transition_holo_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_list_selector_background_transition_holo_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_list_selector_background_transition_holo_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_list_selector_holo_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_list_selector_holo_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_list_selector_holo_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_list_selector_holo_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ratingbar_indicator_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ratingbar_indicator_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ratingbar_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ratingbar_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_ratingbar_small_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_ratingbar_small_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_seekbar_thumb_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_seekbar_thumb_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_seekbar_tick_mark_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_seekbar_tick_mark_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_seekbar_track_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_seekbar_track_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_spinner_textfield_background_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_spinner_textfield_background_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_star_black_48dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_star_black_48dp.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_star_half_black_48dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_star_half_black_48dp.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_switch_thumb_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_switch_thumb_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_tab_indicator_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_tab_indicator_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_text_cursor_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_text_cursor_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/abc_textfield_search_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_textfield_search_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/btn_checkbox_checked_mtrl.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\btn_checkbox_checked_mtrl.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\btn_checkbox_checked_to_unchecked_mtrl_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/btn_checkbox_unchecked_mtrl.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\btn_checkbox_unchecked_mtrl.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\btn_checkbox_unchecked_to_checked_mtrl_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/btn_radio_off_mtrl.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\btn_radio_off_mtrl.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/btn_radio_off_to_on_mtrl_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\btn_radio_off_to_on_mtrl_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/btn_radio_on_mtrl.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\btn_radio_on_mtrl.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/btn_radio_on_to_off_mtrl_animation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\btn_radio_on_to_off_mtrl_animation.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/test_level_drawable.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\test_level_drawable.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/tooltip_frame_dark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tooltip_frame_dark.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/drawable/tooltip_frame_light.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tooltip_frame_light.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_0.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_1.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/interpolator/btn_radio_to_off_mtrl_animation_interpolator_0.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator\\btn_radio_to_off_mtrl_animation_interpolator_0.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/interpolator/btn_radio_to_on_mtrl_animation_interpolator_0.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator\\btn_radio_to_on_mtrl_animation_interpolator_0.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/interpolator/fast_out_slow_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator\\fast_out_slow_in.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout-v26/abc_screen_toolbar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v26\\abc_screen_toolbar.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout-watch-v20/abc_alert_dialog_button_bar_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-watch-v20\\abc_alert_dialog_button_bar_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout-watch-v20/abc_alert_dialog_title_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-watch-v20\\abc_alert_dialog_title_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_action_bar_title_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_action_bar_title_item.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_action_bar_up_container.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_action_bar_up_container.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_action_menu_item_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_action_menu_item_layout.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_action_menu_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_action_menu_layout.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_action_mode_bar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_action_mode_bar.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_action_mode_close_item_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_action_mode_close_item_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_activity_chooser_view.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_activity_chooser_view.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_activity_chooser_view_list_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_activity_chooser_view_list_item.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_alert_dialog_button_bar_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_alert_dialog_button_bar_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_alert_dialog_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_alert_dialog_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_alert_dialog_title_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_alert_dialog_title_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_cascading_menu_item_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_cascading_menu_item_layout.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_dialog_title_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_dialog_title_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_expanded_menu_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_expanded_menu_layout.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_list_menu_item_checkbox.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_list_menu_item_checkbox.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_list_menu_item_icon.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_list_menu_item_icon.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_list_menu_item_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_list_menu_item_layout.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_list_menu_item_radio.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_list_menu_item_radio.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_popup_menu_header_item_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_popup_menu_header_item_layout.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_popup_menu_item_layout.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_popup_menu_item_layout.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_screen_content_include.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_screen_content_include.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_screen_simple.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_screen_simple.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_screen_simple_overlay_action_mode.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_screen_simple_overlay_action_mode.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_search_dropdown_item_icons_2line.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_search_dropdown_item_icons_2line.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_search_view.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_search_view.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_select_dialog_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_select_dialog_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/abc_tooltip.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\abc_tooltip.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/select_dialog_item_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\select_dialog_item_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/select_dialog_multichoice_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\select_dialog_multichoice_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/select_dialog_singlechoice_material.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\select_dialog_singlechoice_material.xml
com.touptek.TouptekSDK-appcompat-1.6.1-30\:/layout/support_simple_spinner_dropdown_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\support_simple_spinner_dropdown_item.xml
com.touptek.TouptekSDK-appcompat-resources-1.6.1-17\:/drawable/abc_vector_test.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\abc_vector_test.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-hdpi-v4/notification_bg_low_normal.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_low_normal.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-hdpi-v4/notification_bg_low_pressed.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_low_pressed.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-hdpi-v4/notification_bg_normal.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_normal.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-hdpi-v4/notification_bg_normal_pressed.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_normal_pressed.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-hdpi-v4/notify_panel_notification_icon_bg.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notify_panel_notification_icon_bg.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-mdpi-v4/notification_bg_low_normal.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_low_normal.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-mdpi-v4/notification_bg_low_pressed.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-mdpi-v4/notification_bg_normal.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_normal.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-v21/notification_action_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\notification_action_background.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-xhdpi-v4/notification_bg_low_normal.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_low_normal.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-xhdpi-v4/notification_bg_low_pressed.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_low_pressed.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-xhdpi-v4/notification_bg_normal.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_normal.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_normal_pressed.9.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notify_panel_notification_icon_bg.png
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable/notification_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_bg.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable/notification_bg_low.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_bg_low.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable/notification_icon_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_icon_background.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/drawable/notification_tile_bg.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_tile_bg.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/layout-v21/notification_action.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_action.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/layout-v21/notification_action_tombstone.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_action_tombstone.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/layout-v21/notification_template_custom_big.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_template_custom_big.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/layout-v21/notification_template_icon_group.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_template_icon_group.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/layout/custom_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\custom_dialog.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/layout/notification_template_part_chronometer.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_part_chronometer.xml
com.touptek.TouptekSDK-core-1.9.0-23\:/layout/notification_template_part_time.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_part_time.xml
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/analog_tv_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\analog_tv_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/android_view_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\android_view_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/basic_deformation_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\basic_deformation_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/beauty_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\beauty_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/black_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\black_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/blur_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\blur_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/brightness_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\brightness_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/camera_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\camera_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/cartoon_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\cartoon_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/chroma_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\chroma_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/circle_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\circle_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/color_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\color_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/contrast_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\contrast_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/duotone_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\duotone_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/earlybird_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\earlybird_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/edge_detection_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\edge_detection_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/exposure_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\exposure_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/fire_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\fire_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/fxaa.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\fxaa.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/fxaa_pc.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\fxaa_pc.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/gamma_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\gamma_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/glitch_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\glitch_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/grey_scale_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\grey_scale_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/halftone_lines_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\halftone_lines_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/image70s_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\image70s_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/lamoish_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\lamoish_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/money_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\money_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/negative_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\negative_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/object_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\object_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/object_vertex.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\object_vertex.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/pixelated_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\pixelated_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/polygonization_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\polygonization_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/rainbow_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\rainbow_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/rgb_saturation_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\rgb_saturation_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/ripple_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\ripple_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/saturation_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\saturation_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/sepia_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\sepia_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/sharpness_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\sharpness_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/simple_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\simple_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/simple_vertex.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\simple_vertex.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/snow_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\snow_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/surface_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\surface_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/swirl_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\swirl_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/temperature_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\temperature_fragment.glsl
com.touptek.TouptekSDK-encoder-2.3.5-31\:/raw/zebra_fragment.glsl=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\raw\\zebra_fragment.glsl
com.touptek.TouptekSDK-fragment-1.3.6-12\:/anim-v21/fragment_fast_out_extra_slow_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\fragment_fast_out_extra_slow_in.xml
com.touptek.TouptekSDK-fragment-1.3.6-12\:/animator/fragment_close_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_close_enter.xml
com.touptek.TouptekSDK-fragment-1.3.6-12\:/animator/fragment_close_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_close_exit.xml
com.touptek.TouptekSDK-fragment-1.3.6-12\:/animator/fragment_fade_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_fade_enter.xml
com.touptek.TouptekSDK-fragment-1.3.6-12\:/animator/fragment_fade_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_fade_exit.xml
com.touptek.TouptekSDK-fragment-1.3.6-12\:/animator/fragment_open_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_open_enter.xml
com.touptek.TouptekSDK-fragment-1.3.6-12\:/animator/fragment_open_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_open_exit.xml
com.touptek.TouptekSDK-main-36\:/color/tp_video_button_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\tp_video_button_text_color.xml
com.touptek.TouptekSDK-main-36\:/drawable/ic_fast_forward_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_fast_forward_white_24.xml
com.touptek.TouptekSDK-main-36\:/drawable/ic_fast_rewind_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_fast_rewind_white_24.xml
com.touptek.TouptekSDK-main-36\:/drawable/ic_pause_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_pause_white_24.xml
com.touptek.TouptekSDK-main-36\:/drawable/ic_play_arrow_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_play_arrow_white_24.xml
com.touptek.TouptekSDK-main-36\:/drawable/ic_settings_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_settings_white_24.xml
com.touptek.TouptekSDK-main-36\:/drawable/ic_skip_next_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_skip_next_white_24.xml
com.touptek.TouptekSDK-main-36\:/drawable/ic_skip_previous_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_skip_previous_white_24.xml
com.touptek.TouptekSDK-main-36\:/drawable/ic_step_frame_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_step_frame_white_24.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_speed_dropdown_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_speed_dropdown_background.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_speed_item_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_speed_item_background.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_speed_item_selected_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_speed_item_selected_background.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_video_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_video_button_background.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_video_controls_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_video_controls_background.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_video_play_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_video_play_button_background.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_video_progress_drawable.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_video_progress_drawable.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_video_progress_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_video_progress_thumb.xml
com.touptek.TouptekSDK-main-36\:/drawable/tp_video_settings_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\tp_video_settings_button_background.xml
com.touptek.TouptekSDK-main-36\:/layout/dialog_tp_test.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\dialog_tp_test.xml
com.touptek.TouptekSDK-main-36\:/layout/tp_speed_dropdown_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\tp_speed_dropdown_menu.xml
com.touptek.TouptekSDK-main-36\:/layout/tp_video_player_controls.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\tp_video_player_controls.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/design_bottom_sheet_slide_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\design_bottom_sheet_slide_in.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/design_bottom_sheet_slide_out.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\design_bottom_sheet_slide_out.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/m3_bottom_sheet_slide_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\m3_bottom_sheet_slide_in.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/m3_bottom_sheet_slide_out.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\m3_bottom_sheet_slide_out.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/m3_side_sheet_enter_from_left.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\m3_side_sheet_enter_from_left.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/m3_side_sheet_enter_from_right.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\m3_side_sheet_enter_from_right.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/m3_side_sheet_exit_to_left.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\m3_side_sheet_exit_to_left.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/m3_side_sheet_exit_to_right.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\m3_side_sheet_exit_to_right.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/mtrl_bottom_sheet_slide_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\mtrl_bottom_sheet_slide_in.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim-v21/mtrl_bottom_sheet_slide_out.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\mtrl_bottom_sheet_slide_out.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/design_snackbar_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\design_snackbar_in.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/design_snackbar_out.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\design_snackbar_out.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/linear_indeterminate_line1_head_interpolator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\linear_indeterminate_line1_head_interpolator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/linear_indeterminate_line1_tail_interpolator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\linear_indeterminate_line1_tail_interpolator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/linear_indeterminate_line2_head_interpolator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\linear_indeterminate_line2_head_interpolator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/linear_indeterminate_line2_tail_interpolator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\linear_indeterminate_line2_tail_interpolator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/m3_motion_fade_enter.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\m3_motion_fade_enter.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/m3_motion_fade_exit.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\m3_motion_fade_exit.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/anim/mtrl_card_lowers_interpolator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\mtrl_card_lowers_interpolator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator-v21/design_appbar_state_list_animator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator-v21\\design_appbar_state_list_animator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator-v21/m3_appbar_state_list_animator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator-v21\\m3_appbar_state_list_animator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/design_fab_hide_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\design_fab_hide_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/design_fab_show_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\design_fab_show_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_btn_elevated_btn_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_btn_elevated_btn_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_btn_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_btn_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_card_elevated_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_card_elevated_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_card_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_card_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_chip_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_chip_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_elevated_chip_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_elevated_chip_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_extended_fab_change_size_collapse_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_extended_fab_change_size_collapse_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_extended_fab_change_size_expand_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_extended_fab_change_size_expand_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_extended_fab_hide_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_extended_fab_hide_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_extended_fab_show_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_extended_fab_show_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/m3_extended_fab_state_list_animator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\m3_extended_fab_state_list_animator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_btn_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_btn_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_btn_unelevated_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_btn_unelevated_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_card_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_card_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_chip_state_list_anim.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_chip_state_list_anim.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_extended_fab_change_size_collapse_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_extended_fab_change_size_collapse_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_extended_fab_change_size_expand_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_extended_fab_change_size_expand_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_extended_fab_hide_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_extended_fab_hide_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_extended_fab_show_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_extended_fab_show_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_extended_fab_state_list_animator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_extended_fab_state_list_animator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_fab_hide_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_fab_hide_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_fab_show_motion_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_fab_show_motion_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_fab_transformation_sheet_collapse_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_fab_transformation_sheet_collapse_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/animator/mtrl_fab_transformation_sheet_expand_spec.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\mtrl_fab_transformation_sheet_expand_spec.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-night-v8/material_timepicker_button_stroke.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night-v8\\material_timepicker_button_stroke.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-night-v8/material_timepicker_clockface.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night-v8\\material_timepicker_clockface.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-night-v8/material_timepicker_modebutton_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-night-v8\\material_timepicker_modebutton_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_dark_default_color_primary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_dark_default_color_primary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_dark_default_color_secondary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_dark_default_color_secondary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_dark_highlighted_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_dark_highlighted_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_dark_hint_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_dark_hint_foreground.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_dark_primary_text_disable_only.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_dark_primary_text_disable_only.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_default_color_primary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_default_color_primary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_default_color_secondary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_default_color_secondary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_highlighted_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_highlighted_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_hint_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_hint_foreground.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_dynamic_primary_text_disable_only.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_dynamic_primary_text_disable_only.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral12.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral12.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral17.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral17.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral22.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral22.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral24.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral4.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral4.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral6.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral6.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral87.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral87.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral92.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral92.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral94.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral94.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral96.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral96.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color-v31/m3_ref_palette_dynamic_neutral98.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color-v31\\m3_ref_palette_dynamic_neutral98.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/design_box_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\design_box_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/design_error.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\design_error.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/design_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\design_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_appbar_overlay_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_appbar_overlay_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_assist_chip_icon_tint_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_assist_chip_icon_tint_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_assist_chip_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_assist_chip_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_bottom_sheet_drag_handle_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_bottom_sheet_drag_handle_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_button_background_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_button_background_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_button_foreground_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_button_foreground_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_button_outline_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_button_outline_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_button_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_button_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_button_ripple_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_button_ripple_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_calendar_item_disabled_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_calendar_item_disabled_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_calendar_item_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_calendar_item_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_card_foreground_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_card_foreground_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_card_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_card_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_card_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_card_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_checkbox_button_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_checkbox_button_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_checkbox_button_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_checkbox_button_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_chip_assist_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_chip_assist_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_chip_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_chip_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_chip_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_chip_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_chip_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_chip_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_chip_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_chip_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_dark_default_color_primary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_dark_default_color_primary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_dark_default_color_secondary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_dark_default_color_secondary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_dark_highlighted_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_dark_highlighted_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_dark_hint_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_dark_hint_foreground.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_dark_primary_text_disable_only.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_dark_primary_text_disable_only.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_default_color_primary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_default_color_primary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_default_color_secondary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_default_color_secondary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_efab_ripple_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_efab_ripple_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_elevated_chip_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_elevated_chip_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_fab_efab_background_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_fab_efab_background_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_fab_efab_foreground_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_fab_efab_foreground_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_fab_ripple_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_fab_ripple_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_filled_icon_button_container_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_filled_icon_button_container_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_highlighted_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_highlighted_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_hint_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_hint_foreground.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_icon_button_icon_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_icon_button_icon_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_bar_item_with_indicator_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_bar_item_with_indicator_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_bar_item_with_indicator_label_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_bar_item_with_indicator_label_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_bar_ripple_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_bar_ripple_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_item_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_item_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_item_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_item_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_item_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_item_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_item_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_item_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_rail_item_with_indicator_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_rail_item_with_indicator_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_rail_item_with_indicator_label_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_rail_item_with_indicator_label_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_navigation_rail_ripple_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_navigation_rail_ripple_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_popupmenu_overlay_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_popupmenu_overlay_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_primary_text_disable_only.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_primary_text_disable_only.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_radiobutton_button_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_radiobutton_button_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_radiobutton_ripple_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_radiobutton_ripple_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_selection_control_ripple_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_selection_control_ripple_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_simple_item_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_simple_item_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_slider_active_track_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_slider_active_track_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_slider_halo_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_slider_halo_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_slider_inactive_track_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_slider_inactive_track_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_slider_thumb_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_slider_thumb_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_switch_thumb_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_switch_thumb_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_switch_track_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_switch_track_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_tabs_icon_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_tabs_icon_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_tabs_icon_color_secondary.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_tabs_icon_color_secondary.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_tabs_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_tabs_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_tabs_ripple_color_secondary.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_tabs_ripple_color_secondary.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_tabs_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_tabs_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_tabs_text_color_secondary.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_tabs_text_color_secondary.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_text_button_background_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_text_button_background_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_text_button_foreground_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_text_button_foreground_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_text_button_ripple_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_text_button_ripple_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_textfield_filled_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_textfield_filled_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_textfield_indicator_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_textfield_indicator_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_textfield_input_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_textfield_input_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_textfield_label_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_textfield_label_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_textfield_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_textfield_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_button_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_button_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_button_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_button_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_button_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_button_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_clock_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_clock_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_display_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_display_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_display_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_display_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_display_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_display_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_secondary_text_button_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_secondary_text_button_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_secondary_text_button_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_secondary_text_button_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_timepicker_time_input_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_timepicker_time_input_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/m3_tonal_button_ripple_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\m3_tonal_button_ripple_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_cursor_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_cursor_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_divider_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_divider_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_background_disabled.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_background_disabled.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_background_emphasis_high_type.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_background_emphasis_high_type.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_background_emphasis_medium.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_background_emphasis_medium.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_primary_disabled.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_primary_disabled.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_primary_emphasis_high_type.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_primary_emphasis_high_type.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_primary_emphasis_medium.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_primary_emphasis_medium.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_surface_disabled.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_surface_disabled.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_surface_emphasis_high_type.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_surface_emphasis_high_type.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_surface_emphasis_medium.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_surface_emphasis_medium.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_on_surface_stroke.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_on_surface_stroke.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized__highlighted_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized__highlighted_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized__highlighted_text_inverse.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized__highlighted_text_inverse.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized_color_primary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized_color_primary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized_color_primary_text_inverse.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized_color_primary_text_inverse.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized_color_secondary_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized_color_secondary_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized_color_secondary_text_inverse.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized_color_secondary_text_inverse.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized_hint_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized_hint_foreground.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized_hint_foreground_inverse.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized_hint_foreground_inverse.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized_primary_inverse_text_disable_only.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized_primary_inverse_text_disable_only.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_personalized_primary_text_disable_only.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_personalized_primary_text_disable_only.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_slider_active_tick_marks_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_slider_active_tick_marks_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_slider_active_track_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_slider_active_track_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_slider_halo_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_slider_halo_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_slider_inactive_tick_marks_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_slider_inactive_tick_marks_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_slider_inactive_track_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_slider_inactive_track_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_slider_thumb_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_slider_thumb_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_timepicker_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_timepicker_button_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_timepicker_button_stroke.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_timepicker_button_stroke.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_timepicker_clock_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_timepicker_clock_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_timepicker_clockface.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_timepicker_clockface.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/material_timepicker_modebutton_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\material_timepicker_modebutton_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_btn_bg_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_btn_bg_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_btn_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_btn_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_btn_stroke_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_btn_stroke_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_btn_text_btn_bg_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_btn_text_btn_bg_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_btn_text_btn_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_btn_text_btn_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_btn_text_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_btn_text_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_calendar_item_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_calendar_item_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_calendar_selected_range.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_calendar_selected_range.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_card_view_foreground.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_card_view_foreground.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_card_view_ripple.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_card_view_ripple.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_chip_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_chip_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_chip_close_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_chip_close_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_chip_surface_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_chip_surface_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_chip_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_chip_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_choice_chip_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_choice_chip_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_choice_chip_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_choice_chip_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_choice_chip_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_choice_chip_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_error.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_error.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_fab_bg_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_fab_bg_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_fab_icon_text_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_fab_icon_text_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_fab_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_fab_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_filled_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_filled_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_filled_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_filled_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_filled_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_filled_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_indicator_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_indicator_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_navigation_bar_colored_item_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_navigation_bar_colored_item_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_navigation_bar_colored_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_navigation_bar_colored_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_navigation_bar_item_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_navigation_bar_item_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_navigation_bar_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_navigation_bar_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_navigation_item_background_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_navigation_item_background_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_navigation_item_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_navigation_item_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_navigation_item_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_navigation_item_text_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_on_primary_text_btn_text_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_on_primary_text_btn_text_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_on_surface_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_on_surface_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_outlined_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_outlined_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_outlined_stroke_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_outlined_stroke_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_popupmenu_overlay_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_popupmenu_overlay_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_switch_thumb_icon_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_switch_thumb_icon_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_switch_thumb_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_switch_thumb_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_switch_track_decoration_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_switch_track_decoration_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_switch_track_tint.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_switch_track_tint.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_tabs_colored_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_tabs_colored_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_tabs_icon_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_tabs_icon_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_tabs_icon_color_selector_colored.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_tabs_icon_color_selector_colored.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_tabs_legacy_text_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_tabs_legacy_text_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_tabs_ripple_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_tabs_ripple_color.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/color/mtrl_text_btn_text_color_selector.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\color\\mtrl_text_btn_text_color_selector.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v21/material_cursor_drawable.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\material_cursor_drawable.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v21/mtrl_navigation_bar_item_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\mtrl_navigation_bar_item_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v23/m3_appbar_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23\\m3_appbar_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v23/m3_popupmenu_background_overlay.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23\\m3_popupmenu_background_overlay.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v23/m3_radiobutton_ripple.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23\\m3_radiobutton_ripple.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v23/m3_selection_control_ripple.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23\\m3_selection_control_ripple.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v23/m3_tabs_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23\\m3_tabs_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v23/m3_tabs_transparent_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23\\m3_tabs_transparent_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable-v23/mtrl_popupmenu_background_overlay.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v23\\mtrl_popupmenu_background_overlay.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/avd_hide_password.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\avd_hide_password.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/avd_show_password.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\avd_show_password.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/design_fab_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\design_fab_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/design_ic_visibility.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\design_ic_visibility.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/design_ic_visibility_off.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\design_ic_visibility_off.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/design_password_eye.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\design_password_eye.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/design_snackbar_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\design_snackbar_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_arrow_back_black_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_arrow_back_black_24.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_clear_black_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_clear_black_24.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_clock_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_clock_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_keyboard_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_keyboard_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_m3_chip_check.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_m3_chip_check.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_m3_chip_checked_circle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_m3_chip_checked_circle.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_m3_chip_close.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_m3_chip_close.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_mtrl_checked_circle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_mtrl_checked_circle.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_mtrl_chip_checked_black.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_mtrl_chip_checked_black.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_mtrl_chip_checked_circle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_mtrl_chip_checked_circle.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_mtrl_chip_close_circle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_mtrl_chip_close_circle.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/ic_search_black_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\ic_search_black_24.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/m3_avd_hide_password.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\m3_avd_hide_password.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/m3_avd_show_password.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\m3_avd_show_password.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/m3_bottom_sheet_drag_handle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\m3_bottom_sheet_drag_handle.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/m3_password_eye.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\m3_password_eye.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/m3_tabs_line_indicator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\m3_tabs_line_indicator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/m3_tabs_rounded_line_indicator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\m3_tabs_rounded_line_indicator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/material_ic_calendar_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\material_ic_calendar_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/material_ic_clear_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\material_ic_clear_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/material_ic_edit_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\material_ic_edit_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/material_ic_keyboard_arrow_left_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\material_ic_keyboard_arrow_left_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/material_ic_keyboard_arrow_right_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\material_ic_keyboard_arrow_right_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/material_ic_menu_arrow_down_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\material_ic_menu_arrow_down_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/material_ic_menu_arrow_up_black_24dp.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\material_ic_menu_arrow_up_black_24dp.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_bottomsheet_drag_handle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_bottomsheet_drag_handle.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_checked_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_checked_unchecked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_icon.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_icon.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_icon_checked_indeterminate.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_icon_checked_indeterminate.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_icon_checked_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_icon_checked_unchecked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_icon_indeterminate_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_icon_indeterminate_checked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_icon_indeterminate_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_icon_indeterminate_unchecked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_icon_unchecked_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_icon_unchecked_checked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_icon_unchecked_indeterminate.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_icon_unchecked_indeterminate.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_checkbox_button_unchecked_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_checkbox_button_unchecked_checked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_dialog_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_dialog_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_dropdown_arrow.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_dropdown_arrow.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_ic_arrow_drop_down.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_ic_arrow_drop_down.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_ic_arrow_drop_up.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_ic_arrow_drop_up.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_ic_cancel.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_ic_cancel.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_ic_check_mark.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_ic_check_mark.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_ic_checkbox_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_ic_checkbox_checked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_ic_checkbox_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_ic_checkbox_unchecked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_ic_error.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_ic_error.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_ic_indeterminate.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_ic_indeterminate.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_popupmenu_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_popupmenu_background.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_checked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_checked_pressed.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_checked_pressed.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_checked_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_checked_unchecked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_pressed.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_pressed.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_pressed_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_pressed_checked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_pressed_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_pressed_unchecked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_unchecked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_unchecked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_unchecked_checked.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_unchecked_checked.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_thumb_unchecked_pressed.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_thumb_unchecked_pressed.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_track.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_track.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_switch_track_decoration.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_switch_track_decoration.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/mtrl_tabs_default_indicator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\mtrl_tabs_default_indicator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/drawable/navigation_empty_icon.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\navigation_empty_icon.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/m3_sys_motion_easing_emphasized.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\m3_sys_motion_easing_emphasized.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/m3_sys_motion_easing_emphasized_accelerate.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\m3_sys_motion_easing_emphasized_accelerate.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/m3_sys_motion_easing_emphasized_decelerate.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\m3_sys_motion_easing_emphasized_decelerate.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/m3_sys_motion_easing_linear.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\m3_sys_motion_easing_linear.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/m3_sys_motion_easing_standard.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\m3_sys_motion_easing_standard.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/m3_sys_motion_easing_standard_accelerate.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\m3_sys_motion_easing_standard_accelerate.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/m3_sys_motion_easing_standard_decelerate.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\m3_sys_motion_easing_standard_decelerate.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/mtrl_fast_out_linear_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\mtrl_fast_out_linear_in.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/mtrl_fast_out_slow_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\mtrl_fast_out_slow_in.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator-v21/mtrl_linear_out_slow_in.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator-v21\\mtrl_linear_out_slow_in.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/interpolator/mtrl_linear.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\interpolator\\mtrl_linear.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout-land/material_clock_period_toggle_land.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-land\\material_clock_period_toggle_land.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout-land/material_timepicker.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-land\\material_timepicker.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout-land/mtrl_picker_header_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-land\\mtrl_picker_header_dialog.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout-sw600dp-v13/design_layout_snackbar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-sw600dp-v13\\design_layout_snackbar.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout-sw600dp-v13/mtrl_layout_snackbar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-sw600dp-v13\\mtrl_layout_snackbar.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout-v26/mtrl_calendar_month.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v26\\mtrl_calendar_month.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_bottom_navigation_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_bottom_navigation_item.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_bottom_sheet_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_bottom_sheet_dialog.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_layout_snackbar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_layout_snackbar.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_layout_snackbar_include.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_layout_snackbar_include.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_layout_tab_icon.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_layout_tab_icon.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_layout_tab_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_layout_tab_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_menu_item_action_area.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_menu_item_action_area.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_navigation_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_navigation_item.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_navigation_item_header.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_navigation_item_header.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_navigation_item_separator.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_navigation_item_separator.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_navigation_item_subheader.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_navigation_item_subheader.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_navigation_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_navigation_menu.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_navigation_menu_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_navigation_menu_item.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_text_input_end_icon.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_text_input_end_icon.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/design_text_input_start_icon.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\design_text_input_start_icon.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/m3_alert_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\m3_alert_dialog.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/m3_alert_dialog_actions.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\m3_alert_dialog_actions.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/m3_alert_dialog_title.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\m3_alert_dialog_title.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/m3_auto_complete_simple_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\m3_auto_complete_simple_item.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/m3_side_sheet_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\m3_side_sheet_dialog.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_chip_input_combo.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_chip_input_combo.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_clock_display.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_clock_display.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_clock_display_divider.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_clock_display_divider.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_clock_period_toggle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_clock_period_toggle.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_clockface_textview.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_clockface_textview.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_clockface_view.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_clockface_view.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_radial_view_group.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_radial_view_group.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_textinput_timepicker.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_textinput_timepicker.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_time_chip.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_time_chip.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_time_input.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_time_input.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_timepicker.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_timepicker.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_timepicker_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_timepicker_dialog.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/material_timepicker_textinput_display.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\material_timepicker_textinput_display.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_alert_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_alert_dialog.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_alert_dialog_actions.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_alert_dialog_actions.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_alert_dialog_title.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_alert_dialog_title.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_alert_select_dialog_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_alert_select_dialog_item.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_alert_select_dialog_multichoice.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_alert_select_dialog_multichoice.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_alert_select_dialog_singlechoice.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_alert_select_dialog_singlechoice.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_auto_complete_simple_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_auto_complete_simple_item.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_day.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_day.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_day_of_week.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_day_of_week.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_days_of_week.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_days_of_week.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_horizontal.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_horizontal.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_month_labeled.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_month_labeled.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_month_navigation.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_month_navigation.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_months.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_months.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_vertical.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_vertical.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_calendar_year.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_calendar_year.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_layout_snackbar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_layout_snackbar.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_layout_snackbar_include.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_layout_snackbar_include.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_navigation_rail_item.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_navigation_rail_item.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_actions.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_actions.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_dialog.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_fullscreen.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_fullscreen.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_header_dialog.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_header_dialog.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_header_fullscreen.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_header_fullscreen.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_header_selection_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_header_selection_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_header_title_text.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_header_title_text.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_header_toggle.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_header_toggle.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_text_input_date.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_text_input_date.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_picker_text_input_date_range.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_picker_text_input_date_range.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_search_bar.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_search_bar.xml
com.touptek.TouptekSDK-material-1.10.0-15\:/layout/mtrl_search_view.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\mtrl_search_view.xml
