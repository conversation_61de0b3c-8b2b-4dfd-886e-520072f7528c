<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical"
    android:padding="16dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="20dp" />

    <!-- 拍照按钮 -->
    <ImageButton
        android:id="@+id/btn_take_photo"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/snap_n"
        android:contentDescription="@string/take_photo" />

    <!-- 间距 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="16dp" />

    <!-- 录像按钮 -->
    <ImageButton
        android:id="@+id/btn_record_video"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/record_start_n"
        android:contentDescription="@string/record_video" />

    <!-- 间距 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="16dp" />

    <!-- 静止按钮 -->
    <ImageButton
        android:id="@+id/btn_pause"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/freeze_n"
        android:contentDescription="@string/pause" />

    <!-- 间距 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="16dp" />

    <!-- 文件夹按钮 -->
    <ImageButton
        android:id="@+id/btn_folder"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/ic_fold"
        android:contentDescription="@string/folder" />

    <!-- 间距 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="16dp" />

    <!-- 放大按钮 -->
    <ImageButton
        android:id="@+id/btn_zoom_in"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/zoomin_n"
        android:contentDescription="@string/zoom_in" />

    <!-- 间距 -->
<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="16dp" />-->

    <!-- 缩小按钮 -->
    <ImageButton
        android:id="@+id/btn_zoom_out"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/zoomout_n"
        android:contentDescription="@string/zoom_out" />

    <!-- 间距 -->
<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="16dp" />-->

    <!-- 设置按钮 -->
    <ImageButton
        android:id="@+id/btn_settings"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/config_n"
        android:contentDescription="@string/settings" />

    <!-- 间距 -->
<!--    <View-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="16dp" />-->

    <!-- 关于按钮 -->
    <ImageButton
        android:id="@+id/btn_about"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/about_n"
        android:contentDescription="@string/about" />

    <!-- 间距 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="433dp" />

    <!-- 绘图按钮 -->
    <ImageButton
        android:id="@+id/btn_draw"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/measure_n"
        android:contentDescription="@string/draw" />

    <!-- 间距 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="16dp" />

    <!-- 菜单按钮 -->
    <ImageButton
        android:id="@+id/btn_menu"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/isp_n"
        android:contentDescription="@string/menu" />


</LinearLayout>


