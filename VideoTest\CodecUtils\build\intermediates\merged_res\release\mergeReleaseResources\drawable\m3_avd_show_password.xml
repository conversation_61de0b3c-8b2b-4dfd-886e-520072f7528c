<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="NewApi">

  <aapt:attr name="android:drawable">

    <vector
        android:height="24dp"
        android:viewportHeight="24"
        android:viewportWidth="24"
        android:width="24dp">

      <path
          android:name="strike_through"
          android:pathData="@string/path_password_strike_through"
          android:strokeColor="@android:color/white"
          android:strokeLineCap="square"
          android:strokeWidth="1.8"/>

      <group>

        <clip-path
            android:name="eye_mask"
            android:pathData="@string/path_password_eye_mask_strike_through"/>

        <path
            android:fillColor="@android:color/white"
            android:name="eye"
            android:pathData="@string/path_password_eye"/>

      </group>

    </vector>

  </aapt:attr>

  <target android:name="eye_mask">

    <aapt:attr name="android:animation">

      <objectAnimator
        android:duration="?attr/motionDurationShort4"
        android:interpolator="?attr/motionEasingStandardInterpolator"
          android:propertyName="pathData"
          android:valueFrom="@string/path_password_eye_mask_strike_through"
          android:valueTo="@string/path_password_eye_mask_visible"
          android:valueType="pathType"/>

    </aapt:attr>

  </target>

  <target android:name="strike_through">

    <aapt:attr name="android:animation">

      <objectAnimator
        android:duration="?attr/motionDurationShort4"
        android:interpolator="?attr/motionEasingStandardInterpolator"
          android:propertyName="trimPathEnd"
          android:valueFrom="1"
          android:valueTo="0"/>

    </aapt:attr>

  </target>

</animated-vector>
