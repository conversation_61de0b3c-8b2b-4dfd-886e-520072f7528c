{"logs": [{"outputFile": "com.android.rockchip.mediacodecnew.app-mergeDebugResources-35:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\737b03f0fc1ad0fd308d8e116bf71918\\transformed\\material-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1129,1195,1287,1364,1427,1535,1595,1661,1717,1788,1848,1902,2021,2078,2140,2194,2269,2393,2481,2558,2652,2736,2819,2964,3049,3135,3268,3356,3434,3488,3542,3608,3682,3760,3831,3913,3985,4062,4135,4205,4314,4407,4479,4571,4667,4741,4817,4913,4966,5048,5115,5202,5289,5351,5415,5478,5547,5655,5760,5861,5964,6022,6080,6160,6246,6322", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "322,401,481,563,665,759,855,981,1062,1124,1190,1282,1359,1422,1530,1590,1656,1712,1783,1843,1897,2016,2073,2135,2189,2264,2388,2476,2553,2647,2731,2814,2959,3044,3130,3263,3351,3429,3483,3537,3603,3677,3755,3826,3908,3980,4057,4130,4200,4309,4402,4474,4566,4662,4736,4812,4908,4961,5043,5110,5197,5284,5346,5410,5473,5542,5650,5755,5856,5959,6017,6075,6155,6241,6317,6394"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3107,3186,3266,3348,3450,4269,4365,4491,4572,4634,4700,4792,4869,4932,5040,5100,5166,5222,5293,5353,5407,5526,5583,5645,5699,5774,5898,5986,6063,6157,6241,6324,6469,6554,6640,6773,6861,6939,6993,7047,7113,7187,7265,7336,7418,7490,7567,7640,7710,7819,7912,7984,8076,8172,8246,8322,8418,8471,8553,8620,8707,8794,8856,8920,8983,9052,9160,9265,9366,9469,9527,9585,9752,9838,9914", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,116", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "372,3181,3261,3343,3445,3539,4360,4486,4567,4629,4695,4787,4864,4927,5035,5095,5161,5217,5288,5348,5402,5521,5578,5640,5694,5769,5893,5981,6058,6152,6236,6319,6464,6549,6635,6768,6856,6934,6988,7042,7108,7182,7260,7331,7413,7485,7562,7635,7705,7814,7907,7979,8071,8167,8241,8317,8413,8466,8548,8615,8702,8789,8851,8915,8978,9047,9155,9260,9361,9464,9522,9580,9660,9833,9909,9986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c629d2bab069b69ff70afa7604a76b8d\\transformed\\core-1.13.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "39,40,41,42,43,44,45,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3544,3642,3744,3842,3946,4050,4152,9991", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3637,3739,3837,3941,4045,4147,4264,10087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5ee0973f6a00ce88be66ccc82fc087bc\\transformed\\appcompat-1.7.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2520,2625,2739,2842,3011,9665", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2515,2620,2734,2837,3006,3102,9747"}}]}]}