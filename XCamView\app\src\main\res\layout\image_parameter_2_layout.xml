<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/rounded_border"
    android:layout_margin="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="307dp"
                android:layout_height="match_parent"
                android:text="@string/title_sharpness_text"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/text_sharpness_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="80"
                android:textAlignment="viewEnd"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <ImageButton
                android:id="@+id/btn_sharpness_reduce"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/sub_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>

            <SeekBar
                android:id="@+id/seekbar_sharpness_tv"
                android:layout_width="255dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:min="1"
                android:max="100"
                android:progressBackgroundTint="@color/grey_background"
                android:progressTint="@color/colorISPBlue"
                android:thumbTint="@color/colorISPBlue" />

            <ImageButton
                android:id="@+id/btn_sharpness_add"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/add_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="307dp"
                android:layout_height="match_parent"
                android:text="@string/title_denoise_text"
                android:textColor="#333333"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/text_denoise_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="80"
                android:textAlignment="viewEnd"
                android:textColor="#333333"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <ImageButton
                android:id="@+id/btn_denoise_reduce"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/sub_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>

            <SeekBar
                android:id="@+id/seekbar_denoise_tv"
                android:layout_width="255dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:min="1"
                android:max="100"
                android:progressBackgroundTint="@color/grey_background"
                android:progressTint="@color/colorISPBlue"
                android:thumbTint="@color/colorISPBlue" />

            <ImageButton
                android:id="@+id/btn_denoise_add"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/add_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:orientation="vertical"
        android:gravity="center">

        <Button
            android:id="@+id/btn_Default_image_parameter_2"
            android:layout_width="200dp"
            android:layout_height="28dp"
            android:text="Default"
            android:background="@drawable/btn_rounded_default" />
    </LinearLayout>
    </LinearLayout>


</LinearLayout>