package com.touptek.xcamview.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import kotlin.apply
import kotlin.collections.forEach
import kotlin.collections.forEachIndexed
import kotlin.collections.getOrNull
import kotlin.let
import kotlin.math.pow
import kotlin.math.sqrt
import kotlin.text.format

class MeasurementOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val linePaint = Paint().apply {
        color = Color.RED
        strokeWidth = 5f
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    private val handlePaint = Paint().apply {
        color = Color.RED
        strokeWidth = 3f
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    private val handleFillPaint = Paint().apply {
        color = Color.WHITE
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val HANDLE_RADIUS = 20f

    private val textPaint = Paint().apply {
        color = Color.WHITE
        textSize = 40f
        isAntiAlias = true
    }

    private var startPoint: PointF? = null
    private var endPoint: PointF? = null
    private var currentMode: Mode = Mode.LINE

    private val shapes = mutableListOf<Shape>()

    enum class Mode {
        LINE, CIRCLE
    }

    fun setMode(mode: Mode) {
        currentMode = mode
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Draw all saved shapes
        shapes.forEach { shape ->
            when (shape) {
                is Line -> {
                    canvas.drawLine(shape.start.x, shape.start.y, shape.end.x, shape.end.y, linePaint)
                    drawMeasurementText(canvas, shape.start, shape.end)
                }
                is Circle -> {
                    val radius = sqrt(
                        (shape.center.x - shape.point.x).pow(2) +
                        (shape.center.y - shape.point.y).pow(2)
                    )
                    canvas.drawCircle(shape.center.x, shape.center.y, radius, linePaint)
                    drawCircleMeasurementText(canvas, shape.center, radius)
                }
            }
        }

        // Draw current shape being drawn
        startPoint?.let { start ->
            endPoint?.let { end ->
                when (currentMode) {
                    Mode.LINE -> {
                        canvas.drawLine(start.x, start.y, end.x, end.y, linePaint)
                        drawMeasurementText(canvas, start, end)
                    }
                    Mode.CIRCLE -> {
                        val radius = sqrt((start.x - end.x).pow(2) + (start.y - end.y).pow(2))
                        canvas.drawCircle(start.x, start.y, radius, linePaint)
                        drawCircleMeasurementText(canvas, start, radius)
                    }
                }
            }
        }

        // Draw handles for existing lines
        shapes.forEach { shape ->
            if (shape is Line) {
                // Draw start handle
                canvas.drawCircle(shape.start.x, shape.start.y, HANDLE_RADIUS, handleFillPaint)
                canvas.drawCircle(shape.start.x, shape.start.y, HANDLE_RADIUS, handlePaint)

                // Draw end handle
                canvas.drawCircle(shape.end.x, shape.end.y, HANDLE_RADIUS, handleFillPaint)
                canvas.drawCircle(shape.end.x, shape.end.y, HANDLE_RADIUS, handlePaint)
            }
        }
    }

    private fun drawMeasurementText(canvas: Canvas, start: PointF, end: PointF) {
        val distance = sqrt((end.x - start.x).pow(2) + (end.y - start.y).pow(2))
        val text = "%.1fpx".format(distance)
        val midX = (start.x + end.x) / 2
        val midY = (start.y + end.y) / 2

        // Draw background for better visibility
        val textBounds = Rect()
        textPaint.getTextBounds(text, 0, text.length, textBounds)
        canvas.drawRect(
            midX - textBounds.width() / 2 - 10,
            midY - textBounds.height() - 10,
            midX + textBounds.width() / 2 + 10,
            midY + 10,
            Paint().apply {
                color = Color.BLACK
                alpha = 150
            }
        )

        canvas.drawText(text, midX - textBounds.width() / 2, midY, textPaint)
    }

    private fun drawCircleMeasurementText(canvas: Canvas, center: PointF, radius: Float) {
        val text = "R: %.1fpx".format(radius)
        val textBounds = Rect()
        textPaint.getTextBounds(text, 0, text.length, textBounds)

        // Draw background
        canvas.drawRect(
            center.x - textBounds.width() / 2 - 10,
            center.y - radius - textBounds.height() - 20,
            center.x + textBounds.width() / 2 + 10,
            center.y - radius - 10,
            Paint().apply {
                color = Color.BLACK
                alpha = 150
            }
        )

        canvas.drawText(text,
            center.x - textBounds.width() / 2,
            center.y - radius - 10,
            textPaint
        )
    }

    private var selectedHandle: Int = -1 // -1: none, 0: start, 1: end
    private var selectedLineIndex: Int = -1 // 当前选中的线段索引

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val x = event.x
        val y = event.y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 检查是否点击了已有线段的端点
                shapes.forEachIndexed { index, shape ->
                    if (shape is Line) {
                        if (isInHandle(x, y, shape.start)) {
                            selectedLineIndex = index
                            selectedHandle = 0
                            return true
                        } else if (isInHandle(x, y, shape.end)) {
                            selectedLineIndex = index
                            selectedHandle = 1
                            return true
                        }
                    }
                }

                // 否则开始绘制新线段
                selectedLineIndex = -1
                startPoint = PointF(x, y)
                endPoint = PointF(x, y)
                invalidate()
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (selectedHandle != -1 && selectedLineIndex != -1) {
                    // 移动选中线段的端点
                    shapes.getOrNull(selectedLineIndex)?.let { shape ->
                        if (shape is Line) {
                            when (selectedHandle) {
                                0 -> shapes[selectedLineIndex] = Line(PointF(x, y), shape.end)
                                1 -> shapes[selectedLineIndex] = Line(shape.start, PointF(x, y))
                            }
                        }
                    }
                } else {
                    // 绘制新线段
                    endPoint = PointF(x, y)
                }
                invalidate()
                return true
            }

            MotionEvent.ACTION_UP -> {
                if (selectedHandle == -1) {
                    endPoint = PointF(x, y)
                    startPoint?.let { start ->
                        endPoint?.let { end ->
                            when (currentMode) {
                                Mode.LINE -> shapes.add(Line(start, end))
                                Mode.CIRCLE -> shapes.add(Circle(start, end))
                            }
                        }
                    }
                }
                selectedHandle = -1
                selectedLineIndex = -1
                startPoint = null
                endPoint = null
                invalidate()
                return true
            }
            else -> return super.onTouchEvent(event)
        }
    }

    private fun isInHandle(x: Float, y: Float, point: PointF): Boolean {
        return sqrt((x - point.x).pow(2) + (y - point.y).pow(2)) <= HANDLE_RADIUS
    }

    fun clearAll() {
        shapes.clear()
        invalidate()
    }

    sealed class Shape
    data class Line(val start: PointF, val end: PointF) : Shape()
    data class Circle(val center: PointF, val point: PointF) : Shape()
}