{"logs": [{"outputFile": "com.android.rockchip.video.CodecUtils-release-35:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\676e6cf5fb8bbd8d94b07d695c20fbc3\\transformed\\material-1.10.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1018,1112,1182,1244,1331,1394,1459,1518,1583,1644,1701,1820,1878,1939,1996,2067,2197,2283,2361,2499,2574,2645,2795,2892,2970,3025,3081,3147,3227,3317,3403,3488,3567,3644,3714,3789,3901,3989,4062,4162,4261,4335,4411,4518,4572,4662,4735,4826,4922,4984,5048,5111,5182,5281,5379,5471,5567,5625,5685", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "264,342,418,498,577,656,756,868,948,1013,1107,1177,1239,1326,1389,1454,1513,1578,1639,1696,1815,1873,1934,1991,2062,2192,2278,2356,2494,2569,2640,2790,2887,2965,3020,3076,3142,3222,3312,3398,3483,3562,3639,3709,3784,3896,3984,4057,4157,4256,4330,4406,4513,4567,4657,4730,4821,4917,4979,5043,5106,5177,5276,5374,5466,5562,5620,5680,5763"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3161,3241,3320,3399,3499,3611,3691,3756,3850,3920,3982,4069,4132,4197,4256,4321,4382,4439,4558,4616,4677,4734,4805,4935,5021,5099,5237,5312,5383,5533,5630,5708,5763,5819,5885,5965,6055,6141,6226,6305,6382,6452,6527,6639,6727,6800,6900,6999,7073,7149,7256,7310,7400,7473,7564,7660,7722,7786,7849,7920,8019,8117,8209,8305,8363,8423", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "314,3080,3156,3236,3315,3394,3494,3606,3686,3751,3845,3915,3977,4064,4127,4192,4251,4316,4377,4434,4553,4611,4672,4729,4800,4930,5016,5094,5232,5307,5378,5528,5625,5703,5758,5814,5880,5960,6050,6136,6221,6300,6377,6447,6522,6634,6722,6795,6895,6994,7068,7144,7251,7305,7395,7468,7559,7655,7717,7781,7844,7915,8014,8112,8204,8300,8358,8418,8501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a62aa56eb7c2c5ac23be6fb0044947f3\\transformed\\core-1.9.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8590", "endColumns": "100", "endOffsets": "8686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4fc5f63fc0925efc93853f0f2ba4b8d\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,8506", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,8585"}}]}, {"outputFile": "com.android.rockchip.video.CodecUtils-mergeReleaseResources-33:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\676e6cf5fb8bbd8d94b07d695c20fbc3\\transformed\\material-1.10.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1018,1112,1182,1244,1331,1394,1459,1518,1583,1644,1701,1820,1878,1939,1996,2067,2197,2283,2361,2499,2574,2645,2795,2892,2970,3025,3081,3147,3227,3317,3403,3488,3567,3644,3714,3789,3901,3989,4062,4162,4261,4335,4411,4518,4572,4662,4735,4826,4922,4984,5048,5111,5182,5281,5379,5471,5567,5625,5685", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "264,342,418,498,577,656,756,868,948,1013,1107,1177,1239,1326,1389,1454,1513,1578,1639,1696,1815,1873,1934,1991,2062,2192,2278,2356,2494,2569,2640,2790,2887,2965,3020,3076,3142,3222,3312,3398,3483,3562,3639,3709,3784,3896,3984,4057,4157,4256,4330,4406,4513,4567,4657,4730,4821,4917,4979,5043,5106,5177,5276,5374,5466,5562,5620,5680,5763"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3161,3241,3320,3399,3499,3611,3691,3756,3850,3920,3982,4069,4132,4197,4256,4321,4382,4439,4558,4616,4677,4734,4805,4935,5021,5099,5237,5312,5383,5533,5630,5708,5763,5819,5885,5965,6055,6141,6226,6305,6382,6452,6527,6639,6727,6800,6900,6999,7073,7149,7256,7310,7400,7473,7564,7660,7722,7786,7849,7920,8019,8117,8209,8305,8363,8423", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "314,3080,3156,3236,3315,3394,3494,3606,3686,3751,3845,3915,3977,4064,4127,4192,4251,4316,4377,4434,4553,4611,4672,4729,4800,4930,5016,5094,5232,5307,5378,5528,5625,5703,5758,5814,5880,5960,6050,6136,6221,6300,6377,6447,6522,6634,6722,6795,6895,6994,7068,7144,7251,7305,7395,7468,7559,7655,7717,7781,7844,7915,8014,8112,8204,8300,8358,8418,8501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a62aa56eb7c2c5ac23be6fb0044947f3\\transformed\\core-1.9.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8590", "endColumns": "100", "endOffsets": "8686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4fc5f63fc0925efc93853f0f2ba4b8d\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,8506", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,8585"}}]}]}