<!-- 在 res/layout 目录下创建 popup_menu_layout.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="@android:color/transparent">

    <ImageButton
        android:id="@+id/btn_scene"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/scenechange_n"
        android:contentDescription="Exposure"
        android:layout_marginEnd="16dp"/>

    <ImageButton
        android:id="@+id/btn_exposure"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/exposure_n"
        android:contentDescription="Exposure"
        android:layout_marginEnd="16dp"/>

    <ImageButton
        android:id="@+id/btn_white_balance"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/ic_wb_n"
        android:contentDescription="White Balance"
        android:layout_marginEnd="16dp"/>

    <ImageButton
        android:id="@+id/btn_color_adjustment"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/ic_color_adjust"
        android:contentDescription="Color Adjustment"
        android:layout_marginEnd="16dp"/>

    <ImageButton
        android:id="@+id/btn_image_processing"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/imageprocess_n"
        android:contentDescription="Image Processing"
        android:layout_marginEnd="16dp"/>

    <ImageButton
        android:id="@+id/btn_flip"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/flip_n"
        android:contentDescription="Flip"
        android:layout_marginEnd="16dp"/>

    <ImageButton
        android:id="@+id/btn_power_frequency"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/hz_n"
        android:contentDescription="Power Frequency" />

</LinearLayout>