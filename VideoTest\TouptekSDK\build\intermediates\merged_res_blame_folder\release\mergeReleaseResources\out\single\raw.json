[{"merged": "com.touptek.TouptekSDK-release-35:/raw/earlybird_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/earlybird_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/contrast_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/contrast_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/duotone_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/duotone_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/zebra_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/zebra_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/surface_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/surface_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/black_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/black_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/saturation_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/saturation_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/cartoon_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/cartoon_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/sepia_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/sepia_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/chroma_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/chroma_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/gamma_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/gamma_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/color_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/color_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/simple_vertex.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/simple_vertex.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/edge_detection_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/edge_detection_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/glitch_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/glitch_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/money_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/money_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/fire_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/fire_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/android_view_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/android_view_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/basic_deformation_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/basic_deformation_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/camera_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/camera_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/analog_tv_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/analog_tv_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/circle_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/circle_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/simple_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/simple_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/image70s_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/image70s_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/grey_scale_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/grey_scale_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/negative_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/negative_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/rainbow_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/rainbow_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/ripple_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/ripple_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/sharpness_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/sharpness_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/rgb_saturation_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/rgb_saturation_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/brightness_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/brightness_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/exposure_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/exposure_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/object_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/object_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/pixelated_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/pixelated_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/blur_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/blur_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/fxaa.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/fxaa.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/beauty_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/beauty_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/lamoish_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/lamoish_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/halftone_lines_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/halftone_lines_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/snow_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/snow_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/fxaa_pc.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/fxaa_pc.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/object_vertex.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/object_vertex.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/polygonization_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/polygonization_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/swirl_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/swirl_fragment.glsl"}, {"merged": "com.touptek.TouptekSDK-release-35:/raw/temperature_fragment.glsl", "source": "com.touptek.TouptekSDK-encoder-2.3.5-31:/raw/temperature_fragment.glsl"}]