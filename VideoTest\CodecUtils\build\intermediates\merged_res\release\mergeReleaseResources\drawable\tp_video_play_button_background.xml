<?xml version="1.0" encoding="utf-8"?>
<!-- 播放/暂停按钮特殊强调样式 -->
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#40FFFFFF">
    
    <item android:id="@android:id/background">
        <selector>
            <!-- 按下状态 -->
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <solid android:color="#FF555555" />
                    <corners android:radius="20dp" />
                    <stroke android:width="3dp" android:color="#FFFFFFFF" />
                </shape>
            </item>

            <!-- 禁用状态 -->
            <item android:state_enabled="false">
                <shape android:shape="rectangle">
                    <solid android:color="#FF1A1A1A" />
                    <corners android:radius="20dp" />
                    <stroke android:width="2dp" android:color="#66FFFFFF" />
                </shape>
            </item>

            <!-- 正常状态 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="#FF000000" />
                    <corners android:radius="20dp" />
                    <stroke android:width="2dp" android:color="#FFFFFFFF" />
                </shape>
            </item>
        </selector>
    </item>
    
</ripple>
