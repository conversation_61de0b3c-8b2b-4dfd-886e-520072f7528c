import groovy.util.Node
import groovy.util.XmlParser
import groovy.xml.XmlUtil
import java.io.FileOutputStream
import java.io.FileNotFoundException

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.kotlin.ksp)
}

android {
    namespace = "com.touptek.xcamview"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.touptek.xcamview"
        minSdk = 31
        targetSdk = 34
        versionCode = 5002
        versionName = "14"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        externalNativeBuild {
            cmake {
                cppFlags("-std=c++11")
            }
        }
    }

    buildTypes {
        getByName("release") {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                file("proguard-rules.pro")
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }

    buildFeatures {
        viewBinding = true
    }

    signingConfigs {
        create("platform") {
            storeFile = file("signApk/platform.keystore")
            storePassword = "android"
            keyAlias = "platform"
            keyPassword = "android"
        }
    }

    buildTypes {
        getByName("debug") {
            signingConfig = signingConfigs.getByName("platform")
        }
    }

}

dependencies {
//    implementation(files("libs/rockchip.hardware.hdmi-V1.0-java.jar"))
//    compileOnly(files("libs/framework.jar"))
//    compileOnly(fileTree(mapOf("dir" to "libs/JAVA_LIBRARIES", "include" to listOf("**/*.jar"))))
    testImplementation(libs.junit)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)
    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.constraintlayout)
    implementation(libs.appcompat)
    implementation("androidx.fragment:fragment-ktx:1.5.6")

    implementation(libs.androidx.recyclerview)
    implementation(libs.glide)
    kapt(libs.glide.compiler)
    ksp("com.github.bumptech.glide:ksp:4.16.0")

    implementation("com.github.bumptech.glide:glide:4.15.1")
    implementation(files("libs/CodecUtils.aar"))

    // TIFF格式支持库
    implementation("io.github.beyka:Android-TiffBitmapFactory:*******")

//    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4")


}

tasks.preBuild {
    doLast {
        val imlFile = file("${project.name}.iml")
        println("Change ${project.name}.iml order")
        try {
            val parsedXml = XmlParser().parse(imlFile)
            // 使用 Groovy 的节点访问方式获取 component 节点
            val componentNodes = parsedXml.get("component") as List<Node>
            val componentNode = componentNodes[1]
            // 将 get("orderEntry") 的结果转换为 List<Node>
            val orderEntryNodes = componentNode.get("orderEntry") as? List<Node> ?: emptyList()
            val jdkNode = orderEntryNodes.find { it.attributes()["type"] == "jdk" }
            if (jdkNode != null) {
                componentNode.remove(jdkNode)
            }
            val sdkString = "Android API ${android.compileSdkVersion!!.substringAfter("android-")} Platform"
            Node(componentNode, "orderEntry", mapOf("type" to "jdk", "jdkName" to sdkString, "jdkType" to "Android SDK"))
            XmlUtil.serialize(parsedXml, FileOutputStream(imlFile))
        } catch (e: FileNotFoundException) {
            // nop, iml not found
        }
    }
}