{"logs": [{"outputFile": "com.android.rockchip.mediacodecnew.test.app-debugAndroidTest-5:/values-v21_values-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5dc3befe033c92eb27b3b393abf0af8d\\transformed\\core-1.5.0\\res\\values-v21\\values.xml", "from": {"startLines": "4,13", "startColumns": "0,0", "startOffsets": "180,655", "endLines": "12,21", "endColumns": "8,8", "endOffsets": "654,1127"}, "to": {"startLines": "2,11", "startColumns": "4,4", "startOffsets": "55,534", "endLines": "10,19", "endColumns": "8,8", "endOffsets": "529,1006"}}]}, {"outputFile": "com.android.rockchip.mediacodecnew.test.app-debugAndroidTest-5:/values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5dc3befe033c92eb27b3b393abf0af8d\\transformed\\core-1.5.0\\res\\values\\values.xml", "from": {"startLines": "4,11", "startColumns": "0,0", "startOffsets": "176,544", "endLines": "10,17", "endColumns": "8,8", "endOffsets": "543,909"}, "to": {"startLines": "2,9", "startColumns": "4,4", "startOffsets": "55,427", "endLines": "8,15", "endColumns": "8,8", "endOffsets": "422,792"}}]}, {"outputFile": "com.android.rockchip.mediacodecnew.test.app-debugAndroidTest-5:/values-v28_values-v28.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5dc3befe033c92eb27b3b393abf0af8d\\transformed\\core-1.5.0\\res\\values-v28\\values.xml", "from": {"startLines": "4,13", "startColumns": "0,0", "startOffsets": "180,659", "endLines": "12,21", "endColumns": "8,8", "endOffsets": "658,1135"}, "to": {"startLines": "2,11", "startColumns": "4,4", "startOffsets": "55,538", "endLines": "10,19", "endColumns": "8,8", "endOffsets": "533,1014"}}]}, {"outputFile": "com.android.rockchip.mediacodecnew.test.app-debugAndroidTest-5:/values-v18_values-v18.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5dc3befe033c92eb27b3b393abf0af8d\\transformed\\core-1.5.0\\res\\values-v18\\values.xml", "from": {"startLines": "4,12", "startColumns": "0,0", "startOffsets": "180,596", "endLines": "11,19", "endColumns": "8,8", "endOffsets": "595,1009"}, "to": {"startLines": "2,10", "startColumns": "4,4", "startOffsets": "55,475", "endLines": "9,17", "endColumns": "8,8", "endOffsets": "470,888"}}]}]}