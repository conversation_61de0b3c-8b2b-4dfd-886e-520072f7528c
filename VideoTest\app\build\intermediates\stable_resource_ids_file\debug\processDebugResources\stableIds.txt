com.android.rockchip.mediacodecnew:xml/data_extraction_rules = 0x7f140001
com.android.rockchip.mediacodecnew:xml/backup_rules = 0x7f140000
com.android.rockchip.mediacodecnew:styleable/ViewStubCompat = 0x7f13009a
com.android.rockchip.mediacodecnew:styleable/ViewBackgroundHelper = 0x7f130098
com.android.rockchip.mediacodecnew:styleable/Transform = 0x7f130094
com.android.rockchip.mediacodecnew:styleable/TpVideoPlayerView = 0x7f130093
com.android.rockchip.mediacodecnew:styleable/Tooltip = 0x7f130091
com.android.rockchip.mediacodecnew:styleable/ThemeEnforcement = 0x7f13008f
com.android.rockchip.mediacodecnew:styleable/TextInputLayout = 0x7f13008e
com.android.rockchip.mediacodecnew:styleable/TextInputEditText = 0x7f13008d
com.android.rockchip.mediacodecnew:styleable/TextEffects = 0x7f13008c
com.android.rockchip.mediacodecnew:styleable/StateSet = 0x7f130086
com.android.rockchip.mediacodecnew:styleable/SnackbarLayout = 0x7f130081
com.android.rockchip.mediacodecnew:styleable/Slider = 0x7f13007f
com.android.rockchip.mediacodecnew:styleable/ShapeableImageView = 0x7f13007d
com.android.rockchip.mediacodecnew:styleable/SearchBar = 0x7f13007a
com.android.rockchip.mediacodecnew:styleable/ScrollingViewBehavior_Layout = 0x7f130079
com.android.rockchip.mediacodecnew:styleable/RecycleListView = 0x7f130076
com.android.rockchip.mediacodecnew:styleable/RangeSlider = 0x7f130075
com.android.rockchip.mediacodecnew:styleable/PopupWindowBackgroundState = 0x7f130072
com.android.rockchip.mediacodecnew:styleable/OnSwipe = 0x7f13006f
com.android.rockchip.mediacodecnew:styleable/NavigationView = 0x7f13006d
com.android.rockchip.mediacodecnew:styleable/NavigationBarActiveIndicator = 0x7f13006a
com.android.rockchip.mediacodecnew:styleable/MotionScene = 0x7f130068
com.android.rockchip.mediacodecnew:styleable/MotionLayout = 0x7f130067
com.android.rockchip.mediacodecnew:styleable/MotionLabel = 0x7f130066
com.android.rockchip.mediacodecnew:styleable/MockView = 0x7f130062
com.android.rockchip.mediacodecnew:styleable/MaterialRadioButton = 0x7f130058
com.android.rockchip.mediacodecnew:styleable/MaterialDivider = 0x7f130057
com.android.rockchip.mediacodecnew:styleable/MaterialCheckBoxStates = 0x7f130056
com.android.rockchip.mediacodecnew:styleable/MaterialCheckBox = 0x7f130055
com.android.rockchip.mediacodecnew:styleable/MaterialCalendarItem = 0x7f130053
com.android.rockchip.mediacodecnew:styleable/MaterialButtonToggleGroup = 0x7f130051
com.android.rockchip.mediacodecnew:styleable/MaterialButton = 0x7f130050
com.android.rockchip.mediacodecnew:styleable/MaterialAutoCompleteTextView = 0x7f13004f
com.android.rockchip.mediacodecnew:styleable/MaterialAlertDialogTheme = 0x7f13004e
com.android.rockchip.mediacodecnew:styleable/include = 0x7f13009c
com.android.rockchip.mediacodecnew:styleable/LinearLayoutCompat = 0x7f130049
com.android.rockchip.mediacodecnew:styleable/Layout = 0x7f130047
com.android.rockchip.mediacodecnew:styleable/KeyTrigger = 0x7f130046
com.android.rockchip.mediacodecnew:styleable/KeyPosition = 0x7f130044
com.android.rockchip.mediacodecnew:styleable/KeyFramesVelocity = 0x7f130043
com.android.rockchip.mediacodecnew:styleable/KeyFrame = 0x7f130041
com.android.rockchip.mediacodecnew:styleable/KeyCycle = 0x7f130040
com.android.rockchip.mediacodecnew:styleable/ImageFilterView = 0x7f13003d
com.android.rockchip.mediacodecnew:styleable/GradientColorItem = 0x7f13003c
com.android.rockchip.mediacodecnew:styleable/Fragment = 0x7f130039
com.android.rockchip.mediacodecnew:styleable/FontFamilyFont = 0x7f130037
com.android.rockchip.mediacodecnew:styleable/FontFamily = 0x7f130036
com.android.rockchip.mediacodecnew:styleable/FloatingActionButton_Behavior_Layout = 0x7f130034
com.android.rockchip.mediacodecnew:styleable/FloatingActionButton = 0x7f130033
com.android.rockchip.mediacodecnew:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f130032
com.android.rockchip.mediacodecnew:styleable/DrawerLayout = 0x7f130030
com.android.rockchip.mediacodecnew:styleable/DrawerArrowToggle = 0x7f13002f
com.android.rockchip.mediacodecnew:styleable/ConstraintLayout_placeholder = 0x7f130029
com.android.rockchip.mediacodecnew:styleable/CollapsingToolbarLayout_Layout = 0x7f130023
com.android.rockchip.mediacodecnew:styleable/CollapsingToolbarLayout = 0x7f130022
com.android.rockchip.mediacodecnew:styleable/Chip = 0x7f13001d
com.android.rockchip.mediacodecnew:styleable/Carousel = 0x7f13001b
com.android.rockchip.mediacodecnew:styleable/BaseProgressIndicator = 0x7f130014
com.android.rockchip.mediacodecnew:styleable/Badge = 0x7f130013
com.android.rockchip.mediacodecnew:styleable/AppCompatTextView = 0x7f130011
com.android.rockchip.mediacodecnew:styleable/AppCompatEmojiHelper = 0x7f13000d
com.android.rockchip.mediacodecnew:styleable/AppCompatSeekBar = 0x7f13000f
com.android.rockchip.mediacodecnew:styleable/AnimatedStateListDrawableTransition = 0x7f130009
com.android.rockchip.mediacodecnew:styleable/AlertDialog = 0x7f130006
com.android.rockchip.mediacodecnew:styleable/ActionBarLayout = 0x7f130001
com.android.rockchip.mediacodecnew:styleable/ActionBar = 0x7f130000
com.android.rockchip.mediacodecnew:style/Widget.Support.CoordinatorLayout = 0x7f120469
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Tooltip = 0x7f120468
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Toolbar = 0x7f120464
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f120462
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f120460
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.Button = 0x7f12045b
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextView = 0x7f120459
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f120456
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f120451
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f120450
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f12044b
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TabLayout = 0x7f12044a
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f120448
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Slider = 0x7f120446
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f120442
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.PopupMenu = 0x7f120440
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f12043d
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.NavigationRailView = 0x7f12043a
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f120435
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f120433
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f120432
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f120430
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f12042f
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f12042e
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f12042d
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f12042c
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f12042a
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f120429
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f120428
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f120427
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f120424
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f120465
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f120423
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar = 0x7f120421
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f12041f
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f12041a
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f120417
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f120415
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ChipGroup = 0x7f120412
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Chip.Filter = 0x7f120411
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Chip.Choice = 0x7f12040f
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Chip.Action = 0x7f12040e
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CardView = 0x7f12040c
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f12040b
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f12040a
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f120409
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f120405
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.TextButton = 0x7f120404
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f120402
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.Icon = 0x7f120401
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button = 0x7f120400
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f1203fd
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f1203fa
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f1203f9
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1203f5
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f1203f2
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f1203f1
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ActionMode = 0x7f1203ef
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f12045e
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f12044e
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f1203ee
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f1203ec
com.android.rockchip.mediacodecnew:style/Widget.Material3.Tooltip = 0x7f1203ea
com.android.rockchip.mediacodecnew:style/Widget.Material3.Toolbar.Surface = 0x7f1203e9
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1203e6
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1203e3
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1203e1
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1203dd
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1203db
com.android.rockchip.mediacodecnew:style/Widget.Material3.Slider = 0x7f1203d3
com.android.rockchip.mediacodecnew:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1203d2
com.android.rockchip.mediacodecnew:style/Widget.Material3.SideSheet.Modal = 0x7f1203d1
com.android.rockchip.mediacodecnew:style/Widget.Material3.SideSheet = 0x7f1203cf
com.android.rockchip.mediacodecnew:style/Widget.Material3.SearchView.Prefix = 0x7f1203cd
com.android.rockchip.mediacodecnew:style/Widget.Material3.PopupMenu.Overflow = 0x7f1203c7
com.android.rockchip.mediacodecnew:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1203c6
com.android.rockchip.mediacodecnew:style/Widget.Material3.PopupMenu = 0x7f1203c4
com.android.rockchip.mediacodecnew:styleable/MaterialTimePicker = 0x7f13005d
com.android.rockchip.mediacodecnew:style/Widget.Material3.NavigationRailView.Badge = 0x7f1203c2
com.android.rockchip.mediacodecnew:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1203c1
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1203bf
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1203be
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1203bd
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1203b8
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker = 0x7f1203b7
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1203b4
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1203b0
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Item = 0x7f1203ae
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1203ad
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1203ab
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1203aa
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1203a7
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1203a6
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1203a2
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f12039d
com.android.rockchip.mediacodecnew:styleable/AnimatedStateListDrawableCompat = 0x7f130007
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f12043b
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Surface = 0x7f120399
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f120397
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f120392
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f120391
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f120390
com.android.rockchip.mediacodecnew:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f12038e
com.android.rockchip.mediacodecnew:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f12038c
com.android.rockchip.mediacodecnew:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f12038b
com.android.rockchip.mediacodecnew:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f12038a
com.android.rockchip.mediacodecnew:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f120388
com.android.rockchip.mediacodecnew:style/Widget.Material3.DrawerLayout = 0x7f120386
com.android.rockchip.mediacodecnew:style/Widget.Material3.CompoundButton.Switch = 0x7f120385
com.android.rockchip.mediacodecnew:style/Widget.Material3.CompoundButton.RadioButton = 0x7f120384
com.android.rockchip.mediacodecnew:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f120381
com.android.rockchip.mediacodecnew:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f12037d
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f120438
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Suggestion = 0x7f120378
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f120377
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Input.Elevated = 0x7f120375
com.android.rockchip.mediacodecnew:style/Widget.Material3.CheckedTextView = 0x7f12036f
com.android.rockchip.mediacodecnew:style/Widget.Material3.CardView.Outlined = 0x7f12036e
com.android.rockchip.mediacodecnew:style/Widget.Material3.CardView.Elevated = 0x7f12036c
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.TonalButton = 0x7f120369
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.TextButton.Dialog = 0x7f120364
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.TextButton = 0x7f120363
com.android.rockchip.mediacodecnew:styleable/ActivityChooserView = 0x7f130005
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.IconButton = 0x7f12035d
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f12035b
com.android.rockchip.mediacodecnew:style/Widget.Material3.BottomSheet.DragHandle = 0x7f120357
com.android.rockchip.mediacodecnew:style/Widget.Material3.BottomAppBar.Legacy = 0x7f120353
com.android.rockchip.mediacodecnew:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f12034b
com.android.rockchip.mediacodecnew:style/Widget.Material3.AppBarLayout = 0x7f12034a
com.android.rockchip.mediacodecnew:style/Widget.Material3.LinearProgressIndicator = 0x7f12039c
com.android.rockchip.mediacodecnew:style/Widget.Material3.ActionMode = 0x7f120349
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.BottomSheet = 0x7f1203fe
com.android.rockchip.mediacodecnew:style/Widget.Material3.ActionBar.Solid = 0x7f120348
com.android.rockchip.mediacodecnew:style/Widget.Design.TextInputLayout = 0x7f120347
com.android.rockchip.mediacodecnew:style/Widget.Design.TabLayout = 0x7f120345
com.android.rockchip.mediacodecnew:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f120343
com.android.rockchip.mediacodecnew:style/Widget.Design.FloatingActionButton = 0x7f120341
com.android.rockchip.mediacodecnew:style/Widget.Compat.NotificationActionText = 0x7f12033c
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f12033a
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Toolbar = 0x7f120339
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f120338
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.TextView = 0x7f120337
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f12032b
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.PopupWindow = 0x7f120329
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f120328
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.PopupMenu = 0x7f120327
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ListPopupWindow = 0x7f120323
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ListMenuView = 0x7f120322
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f120321
com.android.rockchip.mediacodecnew:styleable/LinearLayoutCompat_Layout = 0x7f13004a
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.SearchView = 0x7f120320
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.PopupMenu = 0x7f12031e
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f12031d
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f120316
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionButton = 0x7f120315
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f120312
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f120311
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f12030d
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.DrawerArrowToggle = 0x7f120308
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f120306
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f120305
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ButtonBar = 0x7f120303
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f1202ff
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Button.Borderless = 0x7f1202fe
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActivityChooserView = 0x7f1202fb
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionButton.Overflow = 0x7f1202f9
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f1202f8
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionBar.TabBar = 0x7f1202f4
com.android.rockchip.mediacodecnew:style/VideoControlButton = 0x7f1202f0
com.android.rockchip.mediacodecnew:style/TpVideoTimeDisplay = 0x7f1202ef
com.android.rockchip.mediacodecnew:style/TpVideoSpeedDropdownItem = 0x7f1202ed
com.android.rockchip.mediacodecnew:style/TpVideoSpeedDropdownAnimation = 0x7f1202ec
com.android.rockchip.mediacodecnew:style/TpVideoSpeedDialogAnimation = 0x7f1202eb
com.android.rockchip.mediacodecnew:style/TpVideoProgressBar = 0x7f1202e8
com.android.rockchip.mediacodecnew:style/TpVideoControlButton = 0x7f1202e6
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f1202e5
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1202de
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f1202db
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1202d7
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1202d4
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1202d1
com.android.rockchip.mediacodecnew:style/Widget.Material3.CollapsingToolbar.Large = 0x7f120380
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1202cd
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1202c9
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1202c7
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1202c6
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1202c5
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1202c3
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents = 0x7f1202be
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1202bc
com.android.rockchip.mediacodecnew:styleable/ExtendedFloatingActionButton = 0x7f130031
com.android.rockchip.mediacodecnew:styleable/ConstraintSet = 0x7f13002b
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1202ba
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1202b7
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.NavigationView = 0x7f1202b1
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1202b0
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1202ac
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1202aa
com.android.rockchip.mediacodecnew:styleable/Motion = 0x7f130063
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1202a9
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1202a6
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1202a2
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f12029e
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.Icon = 0x7f12035c
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f12029a
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Dialog = 0x7f120297
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Dark = 0x7f120293
com.android.rockchip.mediacodecnew:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1203c9
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Button.IconButton = 0x7f12028b
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.BottomAppBar = 0x7f120285
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120284
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f120283
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f120280
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3 = 0x7f12027e
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat.Light = 0x7f12027c
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f12027b
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat.Dialog = 0x7f12027a
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f120279
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat = 0x7f120274
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f120272
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f12026d
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f12026b
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f12026a
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120269
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120267
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Dialog = 0x7f120266
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120265
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f120264
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f120262
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light = 0x7f120261
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f12025f
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f12025e
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f12025c
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f12025b
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Dialog.Alert = 0x7f120259
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f120257
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f120254
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f120253
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f12024f
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f12024c
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f12024a
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f120249
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight = 0x7f120248
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.CompactMenu = 0x7f120247
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Bridge = 0x7f120246
com.android.rockchip.mediacodecnew:style/Theme.Material3.Light.SideSheetDialog = 0x7f120243
com.android.rockchip.mediacodecnew:style/Theme.Material3.Light.NoActionBar = 0x7f120242
com.android.rockchip.mediacodecnew:style/Theme.Material3.Light.Dialog.Alert = 0x7f12023f
com.android.rockchip.mediacodecnew:style/Theme.Material3.Light.Dialog = 0x7f12023e
com.android.rockchip.mediacodecnew:style/Theme.Material3.Light.BottomSheetDialog = 0x7f12023d
com.android.rockchip.mediacodecnew:style/Theme.Material3.DayNight.NoActionBar = 0x7f120237
com.android.rockchip.mediacodecnew:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f120236
com.android.rockchip.mediacodecnew:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f120235
com.android.rockchip.mediacodecnew:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f120234
com.android.rockchip.mediacodecnew:style/Theme.Material3.DayNight.Dialog = 0x7f120233
com.android.rockchip.mediacodecnew:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f120232
com.android.rockchip.mediacodecnew:style/Theme.Material3.Dark.SideSheetDialog = 0x7f120230
com.android.rockchip.mediacodecnew:style/Theme.Material3.Dark.NoActionBar = 0x7f12022f
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f120457
com.android.rockchip.mediacodecnew:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f12022e
com.android.rockchip.mediacodecnew:style/Theme.Material3.Dark.Dialog.Alert = 0x7f12022c
com.android.rockchip.mediacodecnew:style/Theme.Material3.Dark = 0x7f120229
com.android.rockchip.mediacodecnew:styleable/ScrimInsetsFrameLayout = 0x7f130078
com.android.rockchip.mediacodecnew:style/Theme.Design.Light.BottomSheetDialog = 0x7f120226
com.android.rockchip.mediacodecnew:style/Theme.Design.Light = 0x7f120225
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f120220
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f12021f
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f120395
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f12021e
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Light.DarkActionBar = 0x7f12021c
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Empty = 0x7f12021a
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Dialog.Alert = 0x7f120217
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f120214
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f12041c
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f120212
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.DayNight.Dialog = 0x7f120211
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.CompactMenu = 0x7f12020e
com.android.rockchip.mediacodecnew:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f12020a
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Tooltip = 0x7f120209
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f120208
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Overline = 0x7f120205
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Headline1 = 0x7f1201ff
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Caption = 0x7f1201fd
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Button = 0x7f1201fc
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Body1 = 0x7f1201fa
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Badge = 0x7f1201f9
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.SearchView.Prefix = 0x7f1201f5
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f1201f2
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f12031f
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.HeadlineLarge = 0x7f1201ec
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.DisplayMedium = 0x7f1201ea
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.BodyLarge = 0x7f1201e6
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.ActionBar.Title = 0x7f1201e5
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1201e3
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1201e2
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1201dd
com.android.rockchip.mediacodecnew:style/Theme.AppCompat = 0x7f12020d
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1201d8
com.android.rockchip.mediacodecnew:styleable/TextAppearance = 0x7f13008b
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1201d7
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Suffix = 0x7f1201d3
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Prefix = 0x7f1201d1
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Hint = 0x7f1201cf
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Counter = 0x7f1201cb
com.android.rockchip.mediacodecnew:style/TextAppearance.Compat.Notification.Title = 0x7f1201c9
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1201e4
com.android.rockchip.mediacodecnew:style/TextAppearance.Compat.Notification.Line2 = 0x7f1201c7
com.android.rockchip.mediacodecnew:style/TextAppearance.Compat.Notification = 0x7f1201c5
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1201c2
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1201c0
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1201be
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1201ba
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1201b9
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1201b8
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1201b6
com.android.rockchip.mediacodecnew:style/Widget.Material3.Toolbar.OnSurface = 0x7f1203e8
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1201b5
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1201b4
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1201b3
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Title = 0x7f1201af
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1201ae
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Subhead = 0x7f1201ad
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Small = 0x7f1201ab
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1201a9
com.android.rockchip.mediacodecnew:styleable/AppCompatTheme = 0x7f130012
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Menu = 0x7f1201a8
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Medium = 0x7f1201a6
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1201a5
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1201a1
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.NavigationView = 0x7f12043f
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Display4 = 0x7f12019d
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Display1 = 0x7f12019a
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Button = 0x7f120198
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Body2 = 0x7f120197
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f120194
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f12018e
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f12018d
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f120189
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f120187
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f120183
com.android.rockchip.mediacodecnew:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f120180
com.android.rockchip.mediacodecnew:styleable/ColorStateListItem = 0x7f130024
com.android.rockchip.mediacodecnew:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f12017f
com.android.rockchip.mediacodecnew:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f12017e
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.Tooltip = 0x7f12017b
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.SmallComponent = 0x7f12017a
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f120179
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.Corner.None = 0x7f120175
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.Corner.Medium = 0x7f120174
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.Corner.Large = 0x7f120173
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f120210
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f120171
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f120170
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f12016c
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f12016b
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f12016a
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f120169
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1201db
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f120165
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f120164
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f120163
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f120162
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f120161
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f12015f
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f12015e
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1201c1
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f12015b
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.LabelSmall = 0x7f1201f1
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f120157
com.android.rockchip.mediacodecnew:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f120155
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f120154
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f120153
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f120150
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f12014f
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f12014e
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f12014c
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f120166
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f12014b
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f12014a
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f120149
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f120148
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f120147
com.android.rockchip.mediacodecnew:style/Platform.V25.AppCompat.Light = 0x7f120144
com.android.rockchip.mediacodecnew:style/Platform.V25.AppCompat = 0x7f120143
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.TabLayout = 0x7f1202b6
com.android.rockchip.mediacodecnew:style/Platform.V21.AppCompat = 0x7f120141
com.android.rockchip.mediacodecnew:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f120140
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1202b4
com.android.rockchip.mediacodecnew:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f12013f
com.android.rockchip.mediacodecnew:style/Platform.ThemeOverlay.AppCompat = 0x7f12013e
com.android.rockchip.mediacodecnew:style/Platform.MaterialComponents = 0x7f12013a
com.android.rockchip.mediacodecnew:style/Platform.AppCompat.Light = 0x7f120139
com.android.rockchip.mediacodecnew:style/Platform.AppCompat = 0x7f120138
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f120135
com.android.rockchip.mediacodecnew:style/Widget.Material3.SearchView.Toolbar = 0x7f1203ce
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f120133
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f120132
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f120131
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f12012f
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f12012d
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f12012a
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f120129
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f120128
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Button.Colored = 0x7f120301
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Body.Text = 0x7f120126
com.android.rockchip.mediacodecnew:style/FullScreenDialog = 0x7f120123
com.android.rockchip.mediacodecnew:style/CardView.Light = 0x7f120122
com.android.rockchip.mediacodecnew:style/CardView.Dark = 0x7f120121
com.android.rockchip.mediacodecnew:style/CardView = 0x7f120120
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.TextView = 0x7f12011f
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.Snackbar = 0x7f12011c
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f120118
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f120116
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f120115
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f120192
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f120112
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f12010a
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.FloatingActionButton = 0x7f120109
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f120108
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Display3 = 0x7f12019c
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.CollapsingToolbar = 0x7f120103
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.ActionMode = 0x7f1200ff
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1200fe
com.android.rockchip.mediacodecnew:style/Base.Widget.Design.TabLayout = 0x7f1200fd
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1200fa
com.android.rockchip.mediacodecnew:styleable/Constraint = 0x7f130026
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1200f6
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.SearchView = 0x7f1200f3
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1200f2
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1200f1
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1200ef
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.PopupWindow = 0x7f1200ed
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1200ec
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ListMenuView = 0x7f1200e6
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1200e4
com.android.rockchip.mediacodecnew:style/Widget.Design.BottomSheet.Modal = 0x7f12033f
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1200e2
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1200e0
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1200de
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ImageButton = 0x7f1200dd
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.EditText = 0x7f1200dc
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1200db
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1200d8
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1200d7
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ButtonBar = 0x7f1200d4
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Button.Colored = 0x7f1200d2
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1200d1
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1200cf
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1200cd
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1200c9
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1200c7
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1200c4
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionBar = 0x7f1200c3
com.android.rockchip.mediacodecnew:style/Base.V7.Widget.AppCompat.EditText = 0x7f1200c1
com.android.rockchip.mediacodecnew:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1200be
com.android.rockchip.mediacodecnew:style/Base.V7.Theme.AppCompat.Light = 0x7f1200bd
com.android.rockchip.mediacodecnew:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1200bc
com.android.rockchip.mediacodecnew:style/Base.V7.Theme.AppCompat = 0x7f1200bb
com.android.rockchip.mediacodecnew:style/Base.V24.Theme.Material3.Dark = 0x7f1200b2
com.android.rockchip.mediacodecnew:style/Base.V23.Theme.AppCompat = 0x7f1200b0
com.android.rockchip.mediacodecnew:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1200ad
com.android.rockchip.mediacodecnew:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1200ac
com.android.rockchip.mediacodecnew:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1200a7
com.android.rockchip.mediacodecnew:style/Base.V21.Theme.AppCompat.Light = 0x7f1200a4
com.android.rockchip.mediacodecnew:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1200a3
com.android.rockchip.mediacodecnew:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1200a0
com.android.rockchip.mediacodecnew:style/Widget.Material3.CircularProgressIndicator = 0x7f12037b
com.android.rockchip.mediacodecnew:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f12009c
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f12009a
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f120099
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Body1 = 0x7f120196
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120098
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f120094
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Input.Icon = 0x7f120376
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f120090
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f12008f
com.android.rockchip.mediacodecnew:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f120352
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f120087
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f120086
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f120085
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f120083
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f12018f
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.Material3.Dialog = 0x7f120082
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionButton = 0x7f1202f7
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f120080
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f12007e
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f12007d
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f12007a
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f120193
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.AppCompat = 0x7f120079
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120074
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120071
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f120070
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f12006d
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f120461
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f12006c
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f12006a
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f120064
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f120063
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Light = 0x7f12005f
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f12005d
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f12005a
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f120245
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Dark = 0x7f120059
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f120058
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f120057
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f120055
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f120053
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Light = 0x7f120052
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f120051
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f12004e
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat = 0x7f12004b
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f12004a
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f120049
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f120047
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.MaterialComponents.Button = 0x7f120045
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f120044
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.Material3.Search = 0x7f120043
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f120042
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f12003f
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f12003d
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f120038
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f120037
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f120036
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f120035
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f120033
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f120032
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f12002e
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f120028
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Medium = 0x7f120025
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f120023
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f120022
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f1203ff
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1203e5
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Large = 0x7f120021
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Inverse = 0x7f120020
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Headline = 0x7f12001f
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Display1 = 0x7f12001b
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Caption = 0x7f12001a
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Button = 0x7f120019
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Body2 = 0x7f120018
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f12039a
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat = 0x7f120016
com.android.rockchip.mediacodecnew:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120015
com.android.rockchip.mediacodecnew:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f120013
com.android.rockchip.mediacodecnew:style/Base.CardView = 0x7f120010
com.android.rockchip.mediacodecnew:style/Base.AlertDialog.AppCompat.Light = 0x7f12000c
com.android.rockchip.mediacodecnew:style/Base.AlertDialog.AppCompat = 0x7f12000b
com.android.rockchip.mediacodecnew:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f12000a
com.android.rockchip.mediacodecnew:style/Animation.Material3.SideSheetDialog.Right = 0x7f120009
com.android.rockchip.mediacodecnew:style/Animation.Material3.SideSheetDialog.Left = 0x7f120008
com.android.rockchip.mediacodecnew:style/Animation.Material3.SideSheetDialog = 0x7f120007
com.android.rockchip.mediacodecnew:style/Animation.Design.BottomSheetDialog = 0x7f120005
com.android.rockchip.mediacodecnew:style/Animation.AppCompat.Tooltip = 0x7f120004
com.android.rockchip.mediacodecnew:style/Animation.AppCompat.Dialog = 0x7f120002
com.android.rockchip.mediacodecnew:style/AlertDialog.AppCompat = 0x7f120000
com.android.rockchip.mediacodecnew:string/side_sheet_behavior = 0x7f11009e
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f120414
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1202d5
com.android.rockchip.mediacodecnew:string/searchview_clear_text_content_description = 0x7f11009b
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1203b2
com.android.rockchip.mediacodecnew:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1200c0
com.android.rockchip.mediacodecnew:string/searchbar_scrolling_view_behavior = 0x7f11009a
com.android.rockchip.mediacodecnew:string/search_menu_title = 0x7f110099
com.android.rockchip.mediacodecnew:string/path_password_eye = 0x7f110095
com.android.rockchip.mediacodecnew:string/mtrl_timepicker_confirm = 0x7f110093
com.android.rockchip.mediacodecnew:string/mtrl_switch_track_decoration_path = 0x7f110090
com.android.rockchip.mediacodecnew:string/mtrl_switch_thumb_path_unchecked = 0x7f11008f
com.android.rockchip.mediacodecnew:string/mtrl_switch_thumb_group_name = 0x7f11008a
com.android.rockchip.mediacodecnew:style/Animation.Material3.BottomSheetDialog = 0x7f120006
com.android.rockchip.mediacodecnew:string/mtrl_picker_toggle_to_year_selection = 0x7f110089
com.android.rockchip.mediacodecnew:string/mtrl_picker_toggle_to_text_input_mode = 0x7f110088
com.android.rockchip.mediacodecnew:string/mtrl_picker_toggle_to_day_selection = 0x7f110087
com.android.rockchip.mediacodecnew:string/mtrl_picker_today_description = 0x7f110085
com.android.rockchip.mediacodecnew:styleable/ClockHandView = 0x7f130021
com.android.rockchip.mediacodecnew:string/mtrl_picker_text_input_day_abbr = 0x7f110082
com.android.rockchip.mediacodecnew:string/mtrl_picker_text_input_date_range_start_hint = 0x7f110081
com.android.rockchip.mediacodecnew:string/mtrl_picker_text_input_date_range_end_hint = 0x7f110080
com.android.rockchip.mediacodecnew:string/mtrl_picker_text_input_date_hint = 0x7f11007f
com.android.rockchip.mediacodecnew:string/mtrl_picker_range_header_unselected = 0x7f11007c
com.android.rockchip.mediacodecnew:string/mtrl_picker_range_header_title = 0x7f11007b
com.android.rockchip.mediacodecnew:string/mtrl_picker_range_header_only_start_selected = 0x7f110079
com.android.rockchip.mediacodecnew:string/mtrl_picker_out_of_range = 0x7f110077
com.android.rockchip.mediacodecnew:string/mtrl_picker_invalid_format = 0x7f110071
com.android.rockchip.mediacodecnew:string/mtrl_picker_day_of_week_column_header = 0x7f11006f
com.android.rockchip.mediacodecnew:string/mtrl_picker_date_header_unselected = 0x7f11006e
com.android.rockchip.mediacodecnew:string/mtrl_picker_date_header_selected = 0x7f11006c
com.android.rockchip.mediacodecnew:string/mtrl_picker_confirm = 0x7f11006b
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f120188
com.android.rockchip.mediacodecnew:string/mtrl_picker_cancel = 0x7f11006a
com.android.rockchip.mediacodecnew:string/mtrl_picker_announce_current_selection_none = 0x7f110069
com.android.rockchip.mediacodecnew:string/mtrl_picker_announce_current_range_selection = 0x7f110067
com.android.rockchip.mediacodecnew:string/mtrl_picker_a11y_prev_month = 0x7f110066
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f120111
com.android.rockchip.mediacodecnew:string/mtrl_picker_a11y_next_month = 0x7f110065
com.android.rockchip.mediacodecnew:string/mtrl_chip_close_icon_content_description = 0x7f110062
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_state_description_checked = 0x7f11005f
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_button_path_checked = 0x7f11005b
com.android.rockchip.mediacodecnew:string/material_timepicker_select_time = 0x7f110054
com.android.rockchip.mediacodecnew:string/material_timepicker_pm = 0x7f110053
com.android.rockchip.mediacodecnew:string/material_timepicker_hour = 0x7f110051
com.android.rockchip.mediacodecnew:string/material_timepicker_clock_mode_description = 0x7f110050
com.android.rockchip.mediacodecnew:string/material_timepicker_am = 0x7f11004f
com.android.rockchip.mediacodecnew:string/material_slider_range_start = 0x7f11004d
com.android.rockchip.mediacodecnew:string/material_slider_range_end = 0x7f11004c
com.android.rockchip.mediacodecnew:string/material_motion_easing_linear = 0x7f11004a
com.android.rockchip.mediacodecnew:string/material_motion_easing_accelerated = 0x7f110047
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.Material3.Light = 0x7f12008e
com.android.rockchip.mediacodecnew:string/material_minute_suffix = 0x7f110046
com.android.rockchip.mediacodecnew:string/material_minute_selection = 0x7f110045
com.android.rockchip.mediacodecnew:string/material_hour_selection = 0x7f110043
com.android.rockchip.mediacodecnew:string/material_hour_24h_suffix = 0x7f110042
com.android.rockchip.mediacodecnew:string/material_clock_toggle_content_description = 0x7f110041
com.android.rockchip.mediacodecnew:string/material_clock_display_divider = 0x7f110040
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_linear = 0x7f11003c
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_legacy_decelerate = 0x7f11003b
com.android.rockchip.mediacodecnew:style/ShapeAppearance.MaterialComponents = 0x7f12017c
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_legacy_accelerate = 0x7f11003a
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_emphasized_path_data = 0x7f110038
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f110036
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_emphasized = 0x7f110035
com.android.rockchip.mediacodecnew:string/m3_ref_typeface_plain_regular = 0x7f110034
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.Slider = 0x7f12011b
com.android.rockchip.mediacodecnew:string/m3_ref_typeface_plain_medium = 0x7f110033
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f1202e1
com.android.rockchip.mediacodecnew:string/m3_ref_typeface_brand_medium = 0x7f110031
com.android.rockchip.mediacodecnew:string/m3_exceed_max_badge_text_suffix = 0x7f110030
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f120286
com.android.rockchip.mediacodecnew:string/item_view_role_description = 0x7f11002f
com.android.rockchip.mediacodecnew:string/icon_content_description = 0x7f11002e
com.android.rockchip.mediacodecnew:string/fab_transformation_sheet_behavior = 0x7f11002c
com.android.rockchip.mediacodecnew:string/fab_transformation_scrim_behavior = 0x7f11002b
com.android.rockchip.mediacodecnew:string/error_a11y_label = 0x7f110028
com.android.rockchip.mediacodecnew:styleable/ConstraintLayout_ReactiveGuide = 0x7f130028
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1202c0
com.android.rockchip.mediacodecnew:string/clear_text_end_icon_content_description = 0x7f110027
com.android.rockchip.mediacodecnew:string/character_counter_overflowed_content_description = 0x7f110025
com.android.rockchip.mediacodecnew:string/bottomsheet_drag_handle_content_description = 0x7f110023
com.android.rockchip.mediacodecnew:string/bottomsheet_action_collapse = 0x7f11001f
com.android.rockchip.mediacodecnew:string/bottom_sheet_behavior = 0x7f11001e
com.android.rockchip.mediacodecnew:string/abc_shareactionprovider_share_with_application = 0x7f110019
com.android.rockchip.mediacodecnew:string/abc_shareactionprovider_share_with = 0x7f110018
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f120418
com.android.rockchip.mediacodecnew:string/abc_searchview_description_submit = 0x7f110016
com.android.rockchip.mediacodecnew:string/abc_searchview_description_query = 0x7f110014
com.android.rockchip.mediacodecnew:styleable/MaterialTextAppearance = 0x7f13005b
com.android.rockchip.mediacodecnew:string/abc_searchview_description_clear = 0x7f110013
com.android.rockchip.mediacodecnew:string/abc_search_hint = 0x7f110012
com.android.rockchip.mediacodecnew:string/abc_prepend_shortcut_label = 0x7f110011
com.android.rockchip.mediacodecnew:string/abc_menu_sym_shortcut_label = 0x7f110010
com.android.rockchip.mediacodecnew:string/abc_menu_shift_shortcut_label = 0x7f11000e
com.android.rockchip.mediacodecnew:string/abc_menu_function_shortcut_label = 0x7f11000c
com.android.rockchip.mediacodecnew:string/abc_menu_enter_shortcut_label = 0x7f11000b
com.android.rockchip.mediacodecnew:string/abc_menu_delete_shortcut_label = 0x7f11000a
com.android.rockchip.mediacodecnew:string/abc_menu_ctrl_shortcut_label = 0x7f110009
com.android.rockchip.mediacodecnew:string/abc_menu_alt_shortcut_label = 0x7f110008
com.android.rockchip.mediacodecnew:string/abc_capital_off = 0x7f110006
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f120252
com.android.rockchip.mediacodecnew:string/abc_action_mode_done = 0x7f110003
com.android.rockchip.mediacodecnew:raw/temperature_fragment = 0x7f10002b
com.android.rockchip.mediacodecnew:raw/swirl_fragment = 0x7f10002a
com.android.rockchip.mediacodecnew:raw/simple_vertex = 0x7f100027
com.android.rockchip.mediacodecnew:raw/simple_fragment = 0x7f100026
com.android.rockchip.mediacodecnew:raw/sharpness_fragment = 0x7f100025
com.android.rockchip.mediacodecnew:raw/sepia_fragment = 0x7f100024
com.android.rockchip.mediacodecnew:raw/saturation_fragment = 0x7f100023
com.android.rockchip.mediacodecnew:raw/ripple_fragment = 0x7f100022
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Light.Dialog = 0x7f12021d
com.android.rockchip.mediacodecnew:raw/polygonization_fragment = 0x7f10001f
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f12028f
com.android.rockchip.mediacodecnew:raw/object_vertex = 0x7f10001d
com.android.rockchip.mediacodecnew:raw/lamoish_fragment = 0x7f100019
com.android.rockchip.mediacodecnew:raw/image70s_fragment = 0x7f100018
com.android.rockchip.mediacodecnew:raw/gamma_fragment = 0x7f100014
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f120091
com.android.rockchip.mediacodecnew:raw/fire_fragment = 0x7f100011
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Dark.Dialog = 0x7f12005b
com.android.rockchip.mediacodecnew:raw/exposure_fragment = 0x7f100010
com.android.rockchip.mediacodecnew:raw/duotone_fragment = 0x7f10000d
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f12028c
com.android.rockchip.mediacodecnew:raw/contrast_fragment = 0x7f10000c
com.android.rockchip.mediacodecnew:raw/color_fragment = 0x7f10000b
com.android.rockchip.mediacodecnew:raw/chroma_fragment = 0x7f100009
com.android.rockchip.mediacodecnew:raw/camera_fragment = 0x7f100007
com.android.rockchip.mediacodecnew:raw/black_fragment = 0x7f100004
com.android.rockchip.mediacodecnew:raw/android_view_fragment = 0x7f100001
com.android.rockchip.mediacodecnew:raw/analog_tv_fragment = 0x7f100000
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f120466
com.android.rockchip.mediacodecnew:plurals/mtrl_badge_content_description = 0x7f0f0000
com.android.rockchip.mediacodecnew:mipmap/ic_launcher_round = 0x7f0e0001
com.android.rockchip.mediacodecnew:macro/m3_sys_color_light_surface_tint = 0x7f0d0176
com.android.rockchip.mediacodecnew:macro/m3_sys_color_dark_surface_tint = 0x7f0d0175
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f12011e
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0d0173
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0d0171
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0d016f
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0d016c
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0d016b
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0d016a
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0d0167
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0d0166
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0d0165
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0d0162
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0d0161
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0d015f
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f12010c
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0d015e
com.android.rockchip.mediacodecnew:style/Widget.Material3.Badge = 0x7f12034f
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0d015c
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0d015a
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f120300
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0d0159
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f120270
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0d0156
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light = 0x7f12006e
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0d0155
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0d0153
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_container_shape = 0x7f0d0150
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_container_color = 0x7f0d014f
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_clock_dial_color = 0x7f0d014d
com.android.rockchip.mediacodecnew:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0d014c
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1203b6
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f12010d
com.android.rockchip.mediacodecnew:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0d014a
com.android.rockchip.mediacodecnew:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0d0148
com.android.rockchip.mediacodecnew:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0d0147
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0d0142
com.android.rockchip.mediacodecnew:styleable/ViewPager2 = 0x7f130099
com.android.rockchip.mediacodecnew:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f12037e
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0d0140
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0d013f
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0d013e
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0d013c
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0d0138
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f12018b
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0d0137
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0d0136
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_handle_color = 0x7f0d0135
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0d0134
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0d0130
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_track_color = 0x7f0d012f
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0d012e
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0d012d
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_icon_color = 0x7f0d012a
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0d0128
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0d0127
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_focus_track_color = 0x7f0d0124
com.android.rockchip.mediacodecnew:string/bottomsheet_action_expand_halfway = 0x7f110021
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0d0121
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0d011f
com.android.rockchip.mediacodecnew:string/abc_toolbar_collapse_description = 0x7f11001a
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0d011c
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1200d5
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0d011b
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0d011a
com.android.rockchip.mediacodecnew:macro/m3_comp_suggestion_chip_container_shape = 0x7f0d0118
com.android.rockchip.mediacodecnew:macro/m3_comp_snackbar_supporting_text_type = 0x7f0d0117
com.android.rockchip.mediacodecnew:string/abc_action_bar_home_description = 0x7f110000
com.android.rockchip.mediacodecnew:macro/m3_comp_snackbar_container_color = 0x7f0d0114
com.android.rockchip.mediacodecnew:macro/m3_comp_slider_label_label_text_color = 0x7f0d0113
com.android.rockchip.mediacodecnew:macro/m3_comp_slider_label_container_color = 0x7f0d0112
com.android.rockchip.mediacodecnew:macro/m3_comp_slider_inactive_track_color = 0x7f0d0111
com.android.rockchip.mediacodecnew:macro/m3_comp_slider_disabled_handle_color = 0x7f0d010e
com.android.rockchip.mediacodecnew:macro/m3_comp_slider_disabled_active_track_color = 0x7f0d010d
com.android.rockchip.mediacodecnew:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0d010b
com.android.rockchip.mediacodecnew:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0d0109
com.android.rockchip.mediacodecnew:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0d0108
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.CompactMenu = 0x7f12004c
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d0105
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0d0104
com.android.rockchip.mediacodecnew:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0d0149
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0d00fb
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0d00fa
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0d00f9
com.android.rockchip.mediacodecnew:styleable/OpenGlView = 0x7f130070
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0d00f8
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_header_input_text_type = 0x7f0d00f7
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_container_surface_tint_layer_color = 0x7f0d00f3
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_supporting_text_color = 0x7f0d00ef
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0d00ee
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0d00ed
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f120185
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_leading_icon_color = 0x7f0d00ec
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f120119
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_input_text_type = 0x7f0d00eb
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0d00e9
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_container_surface_tint_layer_color = 0x7f0d00e7
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_container_color = 0x7f0d00e6
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f120365
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0d00e5
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0d00e4
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0d00e1
com.android.rockchip.mediacodecnew:style/Widget.Material3.CollapsingToolbar = 0x7f12037f
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0d00e0
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0d00df
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0d00de
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0d00dd
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0d00db
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1202b9
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0d00d9
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_header_input_text_color = 0x7f0d00f6
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0d00d8
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0d00d7
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0d00d6
com.android.rockchip.mediacodecnew:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0d014b
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0d00d5
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_container_color = 0x7f0d00f2
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0d00d4
com.android.rockchip.mediacodecnew:style/AlertDialog.AppCompat.Light = 0x7f120001
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0d00d2
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0d00d1
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0d00d0
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f120213
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0d00cb
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0d00c9
com.android.rockchip.mediacodecnew:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0d00c8
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0d00c6
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0d00c4
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0d00c2
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0d00c0
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0d00be
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0d00bd
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0d00bc
com.android.rockchip.mediacodecnew:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0d010f
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0d00ba
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0d00b9
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0d00b7
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_caret_color = 0x7f0d00b2
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f12043c
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0d00b1
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_card_outline_color = 0x7f0d00b0
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0d00ae
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f120310
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f120130
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_card_container_shape = 0x7f0d00ab
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_card_container_color = 0x7f0d00aa
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0d00a7
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0d00a4
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0d00a3
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_label_text_type = 0x7f0d00a1
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0d00a0
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0d009f
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0d009c
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0d0099
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0d0098
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f120206
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0d0097
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0d0093
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0d0092
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0d0091
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0d008d
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0d008c
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0d008b
com.android.rockchip.mediacodecnew:styleable/ChipGroup = 0x7f13001e
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0d008a
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Button = 0x7f120289
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0d0089
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.AutoCompleteTextView = 0x7f1202fc
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f12024b
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1201aa
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_headline_color = 0x7f0d0087
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_container_color = 0x7f0d0086
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f120256
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0d0083
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0d0082
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0d007f
com.android.rockchip.mediacodecnew:style/Base.Animation.AppCompat.DropDownUp = 0x7f12000e
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0d007b
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0d007a
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_label_text_type = 0x7f0d0079
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0d0077
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0d0076
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0d0075
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0d0074
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0d0071
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0d006e
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_container_color = 0x7f0d006d
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0d006b
com.android.rockchip.mediacodecnew:styleable/OnClick = 0x7f13006e
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0d006a
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents = 0x7f120244
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0d0068
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0d0065
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0d0062
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0d0061
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0d0163
com.android.rockchip.mediacodecnew:macro/m3_comp_menu_container_color = 0x7f0d0060
com.android.rockchip.mediacodecnew:macro/m3_comp_linear_progress_indicator_track_color = 0x7f0d005f
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1203e2
com.android.rockchip.mediacodecnew:macro/m3_comp_linear_progress_indicator_active_indicator_color = 0x7f0d005e
com.android.rockchip.mediacodecnew:macro/m3_comp_input_chip_label_text_type = 0x7f0d005d
com.android.rockchip.mediacodecnew:macro/m3_comp_icon_button_selected_icon_color = 0x7f0d005a
com.android.rockchip.mediacodecnew:macro/m3_comp_filter_chip_label_text_type = 0x7f0d0059
com.android.rockchip.mediacodecnew:styleable/PopupWindow = 0x7f130071
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0d00ad
com.android.rockchip.mediacodecnew:macro/m3_comp_filter_chip_container_shape = 0x7f0d0058
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f120416
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_tonal_button_container_color = 0x7f0d0053
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0d0052
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_text_field_input_text_type = 0x7f0d0051
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0d0050
com.android.rockchip.mediacodecnew:style/Platform.Widget.AppCompat.Spinner = 0x7f120145
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0d004f
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_text_field_container_color = 0x7f0d004c
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0d004b
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_icon_button_container_color = 0x7f0d0049
com.android.rockchip.mediacodecnew:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f12034e
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_card_container_color = 0x7f0d0047
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0d0095
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_button_container_color = 0x7f0d0044
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f12007c
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_autocomplete_menu_list_item_selected_container_color = 0x7f0d0042
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_tertiary_icon_color = 0x7f0d0041
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_tertiary_container_color = 0x7f0d0040
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_surface_icon_color = 0x7f0d003f
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1202ad
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_surface_container_color = 0x7f0d003e
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_primary_icon_color = 0x7f0d0039
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_primary_container_shape = 0x7f0d0038
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0d0036
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0d0035
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0d0032
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0d0031
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Display3 = 0x7f12001d
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0d002f
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0d002e
com.android.rockchip.mediacodecnew:macro/m3_comp_elevated_button_container_color = 0x7f0d002a
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f120105
com.android.rockchip.mediacodecnew:macro/m3_comp_divider_color = 0x7f0d0029
com.android.rockchip.mediacodecnew:macro/m3_comp_dialog_supporting_text_type = 0x7f0d0028
com.android.rockchip.mediacodecnew:macro/m3_comp_dialog_supporting_text_color = 0x7f0d0027
com.android.rockchip.mediacodecnew:macro/m3_comp_dialog_headline_color = 0x7f0d0025
com.android.rockchip.mediacodecnew:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f12038d
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0d0021
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0d001e
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0d001c
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0d001a
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0d00cf
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0d0016
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0d0015
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0d0014
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0d0011
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_container_shape = 0x7f0d000f
com.android.rockchip.mediacodecnew:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0d000a
com.android.rockchip.mediacodecnew:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0d0008
com.android.rockchip.mediacodecnew:macro/m3_comp_checkbox_selected_container_color = 0x7f0d0006
com.android.rockchip.mediacodecnew:macro/m3_comp_badge_large_label_text_color = 0x7f0d0003
com.android.rockchip.mediacodecnew:layout/tp_video_player_controls = 0x7f0c007e
com.android.rockchip.mediacodecnew:style/Widget.Material3.NavigationRailView = 0x7f1203c0
com.android.rockchip.mediacodecnew:layout/tp_speed_dropdown_menu = 0x7f0c007c
com.android.rockchip.mediacodecnew:layout/support_simple_spinner_dropdown_item = 0x7f0c007b
com.android.rockchip.mediacodecnew:layout/spinner_item = 0x7f0c007a
com.android.rockchip.mediacodecnew:layout/select_dialog_singlechoice_material = 0x7f0c0079
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1201ac
com.android.rockchip.mediacodecnew:layout/select_dialog_item_material = 0x7f0c0077
com.android.rockchip.mediacodecnew:styleable/FlowLayout = 0x7f130035
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1202a8
com.android.rockchip.mediacodecnew:layout/popup_menu = 0x7f0c0076
com.android.rockchip.mediacodecnew:styleable/StateListDrawable = 0x7f130084
com.android.rockchip.mediacodecnew:layout/notification_template_part_chronometer = 0x7f0c0074
com.android.rockchip.mediacodecnew:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f120156
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_legacy = 0x7f110039
com.android.rockchip.mediacodecnew:layout/notification_template_icon_group = 0x7f0c0073
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker = 0x7f12045a
com.android.rockchip.mediacodecnew:layout/notification_template_custom_big = 0x7f0c0072
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_handle_color = 0x7f0d0125
com.android.rockchip.mediacodecnew:layout/network_settings = 0x7f0c006f
com.android.rockchip.mediacodecnew:layout/mtrl_search_view = 0x7f0c006e
com.android.rockchip.mediacodecnew:layout/mtrl_search_bar = 0x7f0c006d
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0d00bb
com.android.rockchip.mediacodecnew:layout/mtrl_picker_text_input_date_range = 0x7f0c006c
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0d0043
com.android.rockchip.mediacodecnew:layout/mtrl_picker_text_input_date = 0x7f0c006b
com.android.rockchip.mediacodecnew:layout/mtrl_picker_header_selection_text = 0x7f0c0068
com.android.rockchip.mediacodecnew:layout/mtrl_picker_header_fullscreen = 0x7f0c0067
com.android.rockchip.mediacodecnew:layout/mtrl_picker_fullscreen = 0x7f0c0065
com.android.rockchip.mediacodecnew:layout/mtrl_picker_dialog = 0x7f0c0064
com.android.rockchip.mediacodecnew:layout/mtrl_navigation_rail_item = 0x7f0c0062
com.android.rockchip.mediacodecnew:layout/mtrl_layout_snackbar_include = 0x7f0c0061
com.android.rockchip.mediacodecnew:layout/mtrl_layout_snackbar = 0x7f0c0060
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f120095
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_vertical = 0x7f0c005e
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_months = 0x7f0c005d
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_month_navigation = 0x7f0c005c
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_month = 0x7f0c005a
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0d0084
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_horizontal = 0x7f0c0059
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_days_of_week = 0x7f0c0058
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_day_of_week = 0x7f0c0057
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_day = 0x7f0c0056
com.android.rockchip.mediacodecnew:layout/mtrl_auto_complete_simple_item = 0x7f0c0055
com.android.rockchip.mediacodecnew:layout/mtrl_alert_select_dialog_multichoice = 0x7f0c0053
com.android.rockchip.mediacodecnew:layout/tp_speed_selection_dialog = 0x7f0c007d
com.android.rockchip.mediacodecnew:layout/mtrl_alert_select_dialog_item = 0x7f0c0052
com.android.rockchip.mediacodecnew:layout/mtrl_alert_dialog = 0x7f0c004f
com.android.rockchip.mediacodecnew:layout/media_browser_integrated = 0x7f0c004d
com.android.rockchip.mediacodecnew:layout/media_browser = 0x7f0c004c
com.android.rockchip.mediacodecnew:layout/material_timepicker_textinput_display = 0x7f0c004b
com.android.rockchip.mediacodecnew:layout/material_time_input = 0x7f0c0048
com.android.rockchip.mediacodecnew:layout/material_clockface_textview = 0x7f0c0043
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0d0054
com.android.rockchip.mediacodecnew:layout/material_clock_period_toggle_land = 0x7f0c0042
com.android.rockchip.mediacodecnew:layout/material_chip_input_combo = 0x7f0c003e
com.android.rockchip.mediacodecnew:layout/m3_side_sheet_dialog = 0x7f0c003d
com.android.rockchip.mediacodecnew:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f120014
com.android.rockchip.mediacodecnew:layout/m3_auto_complete_simple_item = 0x7f0c003c
com.android.rockchip.mediacodecnew:layout/image_viewer = 0x7f0c0037
com.android.rockchip.mediacodecnew:layout/fragment_tp_tv_mode_settings = 0x7f0c0036
com.android.rockchip.mediacodecnew:layout/material_time_chip = 0x7f0c0047
com.android.rockchip.mediacodecnew:layout/fragment_tp_smb_settings = 0x7f0c0035
com.android.rockchip.mediacodecnew:layout/fragment_tp_network_settings = 0x7f0c0034
com.android.rockchip.mediacodecnew:layout/encoder = 0x7f0c0033
com.android.rockchip.mediacodecnew:layout/dialog_tp_settings = 0x7f0c0031
com.android.rockchip.mediacodecnew:layout/dialog_smb_settings = 0x7f0c0030
com.android.rockchip.mediacodecnew:layout/design_text_input_start_icon = 0x7f0c002e
com.android.rockchip.mediacodecnew:layout/design_text_input_end_icon = 0x7f0c002d
com.android.rockchip.mediacodecnew:layout/design_navigation_item = 0x7f0c0027
com.android.rockchip.mediacodecnew:layout/design_menu_item_action_area = 0x7f0c0026
com.android.rockchip.mediacodecnew:layout/design_layout_tab_text = 0x7f0c0025
com.android.rockchip.mediacodecnew:layout/design_layout_tab_icon = 0x7f0c0024
com.android.rockchip.mediacodecnew:layout/design_layout_snackbar = 0x7f0c0022
com.android.rockchip.mediacodecnew:layout/design_bottom_sheet_dialog = 0x7f0c0021
com.android.rockchip.mediacodecnew:layout/activity_tp_video_player_new = 0x7f0c001d
com.android.rockchip.mediacodecnew:raw/zebra_fragment = 0x7f10002c
com.android.rockchip.mediacodecnew:layout/abc_tooltip = 0x7f0c001b
com.android.rockchip.mediacodecnew:layout/abc_select_dialog_material = 0x7f0c001a
com.android.rockchip.mediacodecnew:layout/abc_search_view = 0x7f0c0019
com.android.rockchip.mediacodecnew:layout/abc_screen_simple_overlay_action_mode = 0x7f0c0016
com.android.rockchip.mediacodecnew:layout/abc_screen_content_include = 0x7f0c0014
com.android.rockchip.mediacodecnew:layout/abc_popup_menu_item_layout = 0x7f0c0013
com.android.rockchip.mediacodecnew:layout/abc_list_menu_item_radio = 0x7f0c0011
com.android.rockchip.mediacodecnew:styleable/MenuView = 0x7f130061
com.android.rockchip.mediacodecnew:layout/abc_list_menu_item_icon = 0x7f0c000f
com.android.rockchip.mediacodecnew:layout/abc_cascading_menu_item_layout = 0x7f0c000b
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.RatingBar.Small = 0x7f12032e
com.android.rockchip.mediacodecnew:layout/abc_alert_dialog_title_material = 0x7f0c000a
com.android.rockchip.mediacodecnew:layout/abc_alert_dialog_material = 0x7f0c0009
com.android.rockchip.mediacodecnew:layout/abc_activity_chooser_view = 0x7f0c0006
com.android.rockchip.mediacodecnew:style/Theme.Design = 0x7f120223
com.android.rockchip.mediacodecnew:layout/abc_action_mode_close_item_material = 0x7f0c0005
com.android.rockchip.mediacodecnew:interpolator/mtrl_linear_out_slow_in = 0x7f0b0011
com.android.rockchip.mediacodecnew:interpolator/mtrl_linear = 0x7f0b0010
com.android.rockchip.mediacodecnew:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0b000d
com.android.rockchip.mediacodecnew:interpolator/m3_sys_motion_easing_standard = 0x7f0b000b
com.android.rockchip.mediacodecnew:string/mtrl_picker_text_input_month_abbr = 0x7f110083
com.android.rockchip.mediacodecnew:interpolator/m3_sys_motion_easing_linear = 0x7f0b000a
com.android.rockchip.mediacodecnew:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0b0009
com.android.rockchip.mediacodecnew:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0b0008
com.android.rockchip.mediacodecnew:interpolator/m3_sys_motion_easing_emphasized = 0x7f0b0007
com.android.rockchip.mediacodecnew:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004
com.android.rockchip.mediacodecnew:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003
com.android.rockchip.mediacodecnew:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001
com.android.rockchip.mediacodecnew:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000
com.android.rockchip.mediacodecnew:integer/status_bar_notification_info_maxnum = 0x7f0a0043
com.android.rockchip.mediacodecnew:integer/mtrl_view_visible = 0x7f0a0041
com.android.rockchip.mediacodecnew:layout/abc_action_menu_item_layout = 0x7f0c0002
com.android.rockchip.mediacodecnew:integer/mtrl_view_invisible = 0x7f0a0040
com.android.rockchip.mediacodecnew:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0a003e
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f12045c
com.android.rockchip.mediacodecnew:integer/mtrl_switch_track_viewport_height = 0x7f0a003c
com.android.rockchip.mediacodecnew:string/mtrl_badge_numberless_content_description = 0x7f110056
com.android.rockchip.mediacodecnew:integer/mtrl_switch_thumb_viewport_size = 0x7f0a003b
com.android.rockchip.mediacodecnew:integer/mtrl_switch_thumb_pressed_duration = 0x7f0a0039
com.android.rockchip.mediacodecnew:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0a0038
com.android.rockchip.mediacodecnew:integer/mtrl_switch_thumb_motion_duration = 0x7f0a0036
com.android.rockchip.mediacodecnew:integer/mtrl_card_anim_duration_ms = 0x7f0a0034
com.android.rockchip.mediacodecnew:integer/mtrl_calendar_year_selector_span = 0x7f0a0032
com.android.rockchip.mediacodecnew:integer/mtrl_calendar_selection_text_lines = 0x7f0a0031
com.android.rockchip.mediacodecnew:integer/mtrl_btn_anim_delay_ms = 0x7f0a002e
com.android.rockchip.mediacodecnew:integer/mtrl_badge_max_character_count = 0x7f0a002d
com.android.rockchip.mediacodecnew:integer/material_motion_duration_medium_2 = 0x7f0a0029
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f12031b
com.android.rockchip.mediacodecnew:integer/material_motion_duration_medium_1 = 0x7f0a0028
com.android.rockchip.mediacodecnew:integer/material_motion_duration_long_2 = 0x7f0a0027
com.android.rockchip.mediacodecnew:integer/material_motion_duration_long_1 = 0x7f0a0026
com.android.rockchip.mediacodecnew:integer/m3_sys_shape_corner_large_corner_family = 0x7f0a0023
com.android.rockchip.mediacodecnew:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0a0021
com.android.rockchip.mediacodecnew:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0a0020
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_short2 = 0x7f0a001c
com.android.rockchip.mediacodecnew:styleable/TpImageView = 0x7f130092
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_long4 = 0x7f0a0016
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.HelperText = 0x7f1201ce
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_extra_long3 = 0x7f0a0011
com.android.rockchip.mediacodecnew:string/androidx_startup = 0x7f11001b
com.android.rockchip.mediacodecnew:integer/m3_card_anim_delay_ms = 0x7f0a000c
com.android.rockchip.mediacodecnew:integer/design_snackbar_text_max_lines = 0x7f0a0006
com.android.rockchip.mediacodecnew:integer/config_tooltipAnimTime = 0x7f0a0005
com.android.rockchip.mediacodecnew:string/m3_ref_typeface_brand_regular = 0x7f110032
com.android.rockchip.mediacodecnew:integer/app_bar_elevation_anim_duration = 0x7f0a0002
com.android.rockchip.mediacodecnew:id/x_right = 0x7f09029f
com.android.rockchip.mediacodecnew:id/wrap_content = 0x7f09029c
com.android.rockchip.mediacodecnew:id/wrap = 0x7f09029b
com.android.rockchip.mediacodecnew:styleable/MaterialToolbar = 0x7f13005e
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f120072
com.android.rockchip.mediacodecnew:id/withinBounds = 0x7f09029a
com.android.rockchip.mediacodecnew:id/with_icon = 0x7f090299
com.android.rockchip.mediacodecnew:id/withText = 0x7f090298
com.android.rockchip.mediacodecnew:id/wifi_button = 0x7f090296
com.android.rockchip.mediacodecnew:id/visible = 0x7f090293
com.android.rockchip.mediacodecnew:id/view_tree_saved_state_registry_owner = 0x7f090291
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0d0168
com.android.rockchip.mediacodecnew:id/view_tree_lifecycle_owner = 0x7f09028f
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f120287
com.android.rockchip.mediacodecnew:id/view_transition = 0x7f09028e
com.android.rockchip.mediacodecnew:id/view_offset_helper = 0x7f09028d
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f120403
com.android.rockchip.mediacodecnew:id/vertical_only = 0x7f09028c
com.android.rockchip.mediacodecnew:layout/m3_alert_dialog = 0x7f0c0039
com.android.rockchip.mediacodecnew:id/useLogo = 0x7f09028b
com.android.rockchip.mediacodecnew:raw/money_fragment = 0x7f10001a
com.android.rockchip.mediacodecnew:id/up = 0x7f09028a
com.android.rockchip.mediacodecnew:id/unlabeled = 0x7f090289
com.android.rockchip.mediacodecnew:id/uniform = 0x7f090288
com.android.rockchip.mediacodecnew:id/tv_wdr_value = 0x7f090285
com.android.rockchip.mediacodecnew:id/tv_wb_red_value = 0x7f090284
com.android.rockchip.mediacodecnew:id/tv_wb_green_value = 0x7f090283
com.android.rockchip.mediacodecnew:id/tv_wb_blue_value = 0x7f090282
com.android.rockchip.mediacodecnew:id/tv_total_time = 0x7f090281
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1203bb
com.android.rockchip.mediacodecnew:id/tv_sharpness_value = 0x7f090280
com.android.rockchip.mediacodecnew:id/tv_saturation_value = 0x7f09027f
com.android.rockchip.mediacodecnew:string/material_slider_value = 0x7f11004e
com.android.rockchip.mediacodecnew:id/tv_mode_description_text = 0x7f09027c
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0d013d
com.android.rockchip.mediacodecnew:id/tv_hz_value = 0x7f090279
com.android.rockchip.mediacodecnew:id/tv_gamma_value = 0x7f090277
com.android.rockchip.mediacodecnew:id/tv_flip_value = 0x7f090276
com.android.rockchip.mediacodecnew:layout/material_radial_view_group = 0x7f0c0045
com.android.rockchip.mediacodecnew:id/tv_exposure_time_value = 0x7f090275
com.android.rockchip.mediacodecnew:id/tv_exposure_gain_value = 0x7f090274
com.android.rockchip.mediacodecnew:id/tv_exposure_compensation_value = 0x7f090273
com.android.rockchip.mediacodecnew:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f120383
com.android.rockchip.mediacodecnew:id/tv_duration = 0x7f090272
com.android.rockchip.mediacodecnew:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f120146
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0d0131
com.android.rockchip.mediacodecnew:id/tv_denoise_value = 0x7f090271
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f12007b
com.android.rockchip.mediacodecnew:id/tv_current_position = 0x7f09026e
com.android.rockchip.mediacodecnew:id/tv_ct_red_value = 0x7f09026d
com.android.rockchip.mediacodecnew:id/tv_color_tone_value = 0x7f090268
com.android.rockchip.mediacodecnew:id/tv_color_mode_value = 0x7f090267
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0d0158
com.android.rockchip.mediacodecnew:id/tv_brightness_value = 0x7f090266
com.android.rockchip.mediacodecnew:id/triangle = 0x7f090264
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f12029b
com.android.rockchip.mediacodecnew:id/transition_transform = 0x7f090263
com.android.rockchip.mediacodecnew:id/transition_position = 0x7f090261
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.PopupMenu = 0x7f1200eb
com.android.rockchip.mediacodecnew:id/transition_layout_save = 0x7f090260
com.android.rockchip.mediacodecnew:id/transitionToStart = 0x7f09025e
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f12028d
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f120281
com.android.rockchip.mediacodecnew:id/top = 0x7f090259
com.android.rockchip.mediacodecnew:id/toggle = 0x7f090258
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f110059
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_small_container_color = 0x7f0d0170
com.android.rockchip.mediacodecnew:id/title_template = 0x7f090257
com.android.rockchip.mediacodecnew:id/title = 0x7f090255
com.android.rockchip.mediacodecnew:layout/design_bottom_navigation_item = 0x7f0c0020
com.android.rockchip.mediacodecnew:id/texture_view = 0x7f090253
com.android.rockchip.mediacodecnew:id/textureView = 0x7f090252
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_button_icon_path_group_name = 0x7f110058
com.android.rockchip.mediacodecnew:id/textinput_suffix_text = 0x7f090251
com.android.rockchip.mediacodecnew:id/textinput_placeholder = 0x7f09024f
com.android.rockchip.mediacodecnew:id/textinput_error = 0x7f09024d
com.android.rockchip.mediacodecnew:id/text_input_start_icon = 0x7f09024b
com.android.rockchip.mediacodecnew:id/text_input_error_icon = 0x7f09024a
com.android.rockchip.mediacodecnew:id/textSpacerNoTitle = 0x7f090246
com.android.rockchip.mediacodecnew:macro/m3_comp_dialog_container_shape = 0x7f0d0024
com.android.rockchip.mediacodecnew:id/textEnd = 0x7f090244
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat = 0x7f120195
com.android.rockchip.mediacodecnew:id/tag_unhandled_key_listeners = 0x7f09023f
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f120318
com.android.rockchip.mediacodecnew:mipmap/ic_launcher = 0x7f0e0000
com.android.rockchip.mediacodecnew:id/tag_unhandled_key_event_manager = 0x7f09023e
com.android.rockchip.mediacodecnew:id/tag_transition_group = 0x7f09023d
com.android.rockchip.mediacodecnew:id/tag_screen_reader_focusable = 0x7f09023b
com.android.rockchip.mediacodecnew:styleable/MenuGroup = 0x7f13005f
com.android.rockchip.mediacodecnew:id/tag_on_apply_window_listener = 0x7f090238
com.android.rockchip.mediacodecnew:id/tag_accessibility_clickable_spans = 0x7f090235
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0d0055
com.android.rockchip.mediacodecnew:id/tabMode = 0x7f090233
com.android.rockchip.mediacodecnew:id/switch_vertical_flip = 0x7f090232
com.android.rockchip.mediacodecnew:id/switch_roi_mode = 0x7f09022f
com.android.rockchip.mediacodecnew:id/switch_horizontal_flip = 0x7f09022e
com.android.rockchip.mediacodecnew:id/stretch = 0x7f09022a
com.android.rockchip.mediacodecnew:id/stream_type_radio_group = 0x7f090229
com.android.rockchip.mediacodecnew:id/stop_rtsp_button = 0x7f090228
com.android.rockchip.mediacodecnew:id/stop = 0x7f090227
com.android.rockchip.mediacodecnew:id/staticPostLayout = 0x7f090226
com.android.rockchip.mediacodecnew:id/staticLayout = 0x7f090225
com.android.rockchip.mediacodecnew:id/start_rtsp_button = 0x7f090224
com.android.rockchip.mediacodecnew:id/startVertical = 0x7f090223
com.android.rockchip.mediacodecnew:id/startHorizontal = 0x7f090221
com.android.rockchip.mediacodecnew:id/start = 0x7f090220
com.android.rockchip.mediacodecnew:id/standard = 0x7f09021f
com.android.rockchip.mediacodecnew:id/src_in = 0x7f09021d
com.android.rockchip.mediacodecnew:id/spring = 0x7f09021a
com.android.rockchip.mediacodecnew:id/spread_inside = 0x7f090219
com.android.rockchip.mediacodecnew:layout/material_clock_display = 0x7f0c003f
com.android.rockchip.mediacodecnew:id/split_action_bar = 0x7f090217
com.android.rockchip.mediacodecnew:id/spline = 0x7f090216
com.android.rockchip.mediacodecnew:id/spinner_image_format = 0x7f090214
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f120407
com.android.rockchip.mediacodecnew:id/speed_2_0 = 0x7f090213
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Headline = 0x7f12019e
com.android.rockchip.mediacodecnew:id/speed_1_5 = 0x7f090212
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f120277
com.android.rockchip.mediacodecnew:id/speed_1_25 = 0x7f090211
com.android.rockchip.mediacodecnew:id/speed_1_0 = 0x7f090210
com.android.rockchip.mediacodecnew:id/south = 0x7f09020b
com.android.rockchip.mediacodecnew:id/snapMargins = 0x7f09020a
com.android.rockchip.mediacodecnew:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1200a1
com.android.rockchip.mediacodecnew:id/snackbar_text = 0x7f090208
com.android.rockchip.mediacodecnew:id/snackbar_action = 0x7f090207
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f12029c
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f120041
com.android.rockchip.mediacodecnew:id/slide = 0x7f090206
com.android.rockchip.mediacodecnew:id/showHome = 0x7f090201
com.android.rockchip.mediacodecnew:id/shortcut = 0x7f0901ff
com.android.rockchip.mediacodecnew:id/sharedValueUnset = 0x7f0901fe
com.android.rockchip.mediacodecnew:id/sharedValueSet = 0x7f0901fd
com.android.rockchip.mediacodecnew:id/settings_panel = 0x7f0901fc
com.android.rockchip.mediacodecnew:id/settings_content_frame = 0x7f0901fa
com.android.rockchip.mediacodecnew:id/settingsButton = 0x7f0901f9
com.android.rockchip.mediacodecnew:id/selection_type = 0x7f0901f8
com.android.rockchip.mediacodecnew:id/select_dialog_listview = 0x7f0901f6
com.android.rockchip.mediacodecnew:id/seekbar_wdr = 0x7f0901f5
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Headline3 = 0x7f120201
com.android.rockchip.mediacodecnew:id/seekbar_wb_green = 0x7f0901f3
com.android.rockchip.mediacodecnew:id/seekbar_progress = 0x7f0901ef
com.android.rockchip.mediacodecnew:id/seekbar_mirror = 0x7f0901ee
com.android.rockchip.mediacodecnew:layout/abc_action_menu_layout = 0x7f0c0003
com.android.rockchip.mediacodecnew:id/seekbar_hue = 0x7f0901eb
com.android.rockchip.mediacodecnew:id/seekbar_gamma = 0x7f0901ea
com.android.rockchip.mediacodecnew:id/seekbar_exposure_time = 0x7f0901e8
com.android.rockchip.mediacodecnew:id/seekbar_exposure_gain = 0x7f0901e7
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.TextView = 0x7f1200f9
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0d001b
com.android.rockchip.mediacodecnew:id/seekbar_dark_enhance = 0x7f0901e4
com.android.rockchip.mediacodecnew:id/seekbar_ct_green = 0x7f0901e2
com.android.rockchip.mediacodecnew:id/seekbar_ct_blue = 0x7f0901e1
com.android.rockchip.mediacodecnew:id/seekbar_contrast = 0x7f0901e0
com.android.rockchip.mediacodecnew:id/seekbar_color_tone = 0x7f0901df
com.android.rockchip.mediacodecnew:id/seekbar_brightness = 0x7f0901dd
com.android.rockchip.mediacodecnew:id/seekbar_bandwidth = 0x7f0901dc
com.android.rockchip.mediacodecnew:id/seek_bar = 0x7f0901db
com.android.rockchip.mediacodecnew:id/search_voice_btn = 0x7f0901da
com.android.rockchip.mediacodecnew:id/search_src_text = 0x7f0901d9
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Search = 0x7f1202b3
com.android.rockchip.mediacodecnew:id/search_plate = 0x7f0901d8
com.android.rockchip.mediacodecnew:id/search_mag_icon = 0x7f0901d7
com.android.rockchip.mediacodecnew:id/search_edit_frame = 0x7f0901d5
com.android.rockchip.mediacodecnew:id/scrollable = 0x7f0901d0
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0d00cd
com.android.rockchip.mediacodecnew:id/scrollView = 0x7f0901cf
com.android.rockchip.mediacodecnew:styleable/View = 0x7f130097
com.android.rockchip.mediacodecnew:id/scale = 0x7f0901ca
com.android.rockchip.mediacodecnew:id/sawtooth = 0x7f0901c9
com.android.rockchip.mediacodecnew:id/save_overlay_view = 0x7f0901c8
com.android.rockchip.mediacodecnew:id/rtsp_status_text = 0x7f0901c5
com.android.rockchip.mediacodecnew:id/row_index_key = 0x7f0901c4
com.android.rockchip.mediacodecnew:id/rounded = 0x7f0901c3
com.android.rockchip.mediacodecnew:id/roi_view = 0x7f0901c2
com.android.rockchip.mediacodecnew:id/roiView = 0x7f0901c1
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1200ea
com.android.rockchip.mediacodecnew:id/right_icon = 0x7f0901bf
com.android.rockchip.mediacodecnew:id/rightToLeft = 0x7f0901be
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f120288
com.android.rockchip.mediacodecnew:id/rg_image_format = 0x7f0901bc
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Light.NoActionBar = 0x7f120221
com.android.rockchip.mediacodecnew:id/resolutionTestButton = 0x7f0901ba
com.android.rockchip.mediacodecnew:id/recycler_view = 0x7f0901b8
com.android.rockchip.mediacodecnew:id/recordButton = 0x7f0901b6
com.android.rockchip.mediacodecnew:id/rb_format_jpeg = 0x7f0901b4
com.android.rockchip.mediacodecnew:id/rb_format_bmp = 0x7f0901b3
com.android.rockchip.mediacodecnew:id/radio_wb_roi = 0x7f0901b1
com.android.rockchip.mediacodecnew:id/radio_wb_auto = 0x7f0901af
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0d00ca
com.android.rockchip.mediacodecnew:id/radio_group_wb = 0x7f0901ad
com.android.rockchip.mediacodecnew:id/radio_camera_stream = 0x7f0901ac
com.android.rockchip.mediacodecnew:id/progress_circular = 0x7f0901a8
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f120152
com.android.rockchip.mediacodecnew:id/preview_container = 0x7f0901a6
com.android.rockchip.mediacodecnew:id/pressed = 0x7f0901a5
com.android.rockchip.mediacodecnew:id/pooling_container_listener_holder_tag = 0x7f0901a2
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Caption = 0x7f120199
com.android.rockchip.mediacodecnew:id/percent = 0x7f0901a0
com.android.rockchip.mediacodecnew:id/peekHeight = 0x7f09019f
com.android.rockchip.mediacodecnew:id/pausePreviewButton = 0x7f09019e
com.android.rockchip.mediacodecnew:id/pathRelative = 0x7f09019d
com.android.rockchip.mediacodecnew:id/path = 0x7f09019c
com.android.rockchip.mediacodecnew:id/password_toggle = 0x7f09019b
com.android.rockchip.mediacodecnew:id/parent_matrix = 0x7f09019a
com.android.rockchip.mediacodecnew:id/parentPanel = 0x7f090198
com.android.rockchip.mediacodecnew:id/parent = 0x7f090197
com.android.rockchip.mediacodecnew:id/parallax = 0x7f090196
com.android.rockchip.mediacodecnew:id/outward = 0x7f090193
com.android.rockchip.mediacodecnew:style/Widget.Material3.CardView.Filled = 0x7f12036d
com.android.rockchip.mediacodecnew:id/outline = 0x7f090192
com.android.rockchip.mediacodecnew:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f120240
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_divider_color = 0x7f0d00f4
com.android.rockchip.mediacodecnew:id/open_search_view_toolbar_container = 0x7f090191
com.android.rockchip.mediacodecnew:id/open_search_view_toolbar = 0x7f090190
com.android.rockchip.mediacodecnew:id/open_search_view_status_bar_spacer = 0x7f09018f
com.android.rockchip.mediacodecnew:id/open_search_view_divider = 0x7f090188
com.android.rockchip.mediacodecnew:id/open_search_view_clear_button = 0x7f090186
com.android.rockchip.mediacodecnew:id/open_search_view_background = 0x7f090185
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0d0063
com.android.rockchip.mediacodecnew:id/open_search_bar_text_view = 0x7f090184
com.android.rockchip.mediacodecnew:id/notification_main_column = 0x7f09017f
com.android.rockchip.mediacodecnew:id/notification_background = 0x7f09017e
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_headline_color = 0x7f0d0151
com.android.rockchip.mediacodecnew:id/normal = 0x7f09017c
com.android.rockchip.mediacodecnew:id/neverCompleteToStart = 0x7f090178
com.android.rockchip.mediacodecnew:id/neverCompleteToEnd = 0x7f090177
com.android.rockchip.mediacodecnew:id/network_interface_spinner = 0x7f090175
com.android.rockchip.mediacodecnew:id/network_info_text = 0x7f090174
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1202bd
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f120134
com.android.rockchip.mediacodecnew:id/navigation_bar_item_small_label_view = 0x7f090172
com.android.rockchip.mediacodecnew:id/navigation_bar_item_large_label_view = 0x7f090171
com.android.rockchip.mediacodecnew:id/navigation_bar_item_icon_view = 0x7f09016f
com.android.rockchip.mediacodecnew:id/multiply = 0x7f09016c
com.android.rockchip.mediacodecnew:id/mtrl_view_tag_bottom_padding = 0x7f09016b
com.android.rockchip.mediacodecnew:style/Theme.Design.BottomSheetDialog = 0x7f120224
com.android.rockchip.mediacodecnew:id/mtrl_picker_text_input_range_start = 0x7f090169
com.android.rockchip.mediacodecnew:id/mtrl_picker_text_input_date = 0x7f090167
com.android.rockchip.mediacodecnew:id/mtrl_picker_header_toggle = 0x7f090166
com.android.rockchip.mediacodecnew:id/mtrl_picker_header_selection_text = 0x7f090164
com.android.rockchip.mediacodecnew:id/mtrl_picker_header = 0x7f090163
com.android.rockchip.mediacodecnew:id/mtrl_motion_snapshot_view = 0x7f090161
com.android.rockchip.mediacodecnew:id/mtrl_internal_children_alpha_tag = 0x7f090160
com.android.rockchip.mediacodecnew:id/mtrl_child_content_container = 0x7f09015f
com.android.rockchip.mediacodecnew:id/mtrl_card_checked_layer_id = 0x7f09015e
com.android.rockchip.mediacodecnew:id/mtrl_calendar_year_selector_frame = 0x7f09015d
com.android.rockchip.mediacodecnew:id/mtrl_calendar_text_input_frame = 0x7f09015c
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f12024e
com.android.rockchip.mediacodecnew:id/mtrl_calendar_selection_frame = 0x7f09015b
com.android.rockchip.mediacodecnew:id/mtrl_calendar_days_of_week = 0x7f090157
com.android.rockchip.mediacodecnew:id/mtrl_calendar_day_selector_frame = 0x7f090156
com.android.rockchip.mediacodecnew:id/mtrl_anchor_parent = 0x7f090155
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f110037
com.android.rockchip.mediacodecnew:id/motion_base = 0x7f090154
com.android.rockchip.mediacodecnew:id/month_title = 0x7f090153
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0d0066
com.android.rockchip.mediacodecnew:id/month_navigation_previous = 0x7f090152
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Display2 = 0x7f12019b
com.android.rockchip.mediacodecnew:id/time = 0x7f090254
com.android.rockchip.mediacodecnew:id/month_navigation_next = 0x7f090151
com.android.rockchip.mediacodecnew:id/month_navigation_bar = 0x7f09014f
com.android.rockchip.mediacodecnew:id/month_grid = 0x7f09014e
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0d0096
com.android.rockchip.mediacodecnew:id/mini = 0x7f09014d
com.android.rockchip.mediacodecnew:id/message = 0x7f09014b
com.android.rockchip.mediacodecnew:id/menu_vertical_flip = 0x7f09014a
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f120260
com.android.rockchip.mediacodecnew:id/menu_tv_mode = 0x7f090149
com.android.rockchip.mediacodecnew:id/menu_smb_settings = 0x7f090147
com.android.rockchip.mediacodecnew:id/menu_network_settings = 0x7f090144
com.android.rockchip.mediacodecnew:id/media_thumbnail = 0x7f09013f
com.android.rockchip.mediacodecnew:id/media_name = 0x7f09013e
com.android.rockchip.mediacodecnew:id/matrix = 0x7f09013d
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.AppCompat.Light = 0x7f12007f
com.android.rockchip.mediacodecnew:id/material_timepicker_ok_button = 0x7f09013a
com.android.rockchip.mediacodecnew:id/material_timepicker_mode_button = 0x7f090139
com.android.rockchip.mediacodecnew:id/material_timepicker_container = 0x7f090138
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1202c1
com.android.rockchip.mediacodecnew:id/material_timepicker_cancel_button = 0x7f090137
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1202c4
com.android.rockchip.mediacodecnew:id/material_textinput_timepicker = 0x7f090136
com.android.rockchip.mediacodecnew:id/material_minute_tv = 0x7f090135
com.android.rockchip.mediacodecnew:id/material_minute_text_input = 0x7f090134
com.android.rockchip.mediacodecnew:id/material_label = 0x7f090133
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0d0069
com.android.rockchip.mediacodecnew:id/material_hour_text_input = 0x7f090131
com.android.rockchip.mediacodecnew:id/material_clock_period_toggle = 0x7f090130
com.android.rockchip.mediacodecnew:id/material_clock_level = 0x7f09012d
com.android.rockchip.mediacodecnew:styleable/Variant = 0x7f130096
com.android.rockchip.mediacodecnew:id/material_clock_display_and_toggle = 0x7f09012a
com.android.rockchip.mediacodecnew:id/submit_area = 0x7f09022c
com.android.rockchip.mediacodecnew:id/masked = 0x7f090126
com.android.rockchip.mediacodecnew:string/app_name = 0x7f11001c
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0d0030
com.android.rockchip.mediacodecnew:id/marquee = 0x7f090125
com.android.rockchip.mediacodecnew:id/list_item = 0x7f090123
com.android.rockchip.mediacodecnew:id/listMode = 0x7f090122
com.android.rockchip.mediacodecnew:id/linear = 0x7f090121
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1203de
com.android.rockchip.mediacodecnew:id/line3 = 0x7f090120
com.android.rockchip.mediacodecnew:id/line1 = 0x7f09011f
com.android.rockchip.mediacodecnew:id/legacy = 0x7f09011e
com.android.rockchip.mediacodecnew:id/leftToRight = 0x7f09011d
com.android.rockchip.mediacodecnew:id/left = 0x7f09011c
com.android.rockchip.mediacodecnew:id/layout = 0x7f09011b
com.android.rockchip.mediacodecnew:id/labeled = 0x7f09011a
com.android.rockchip.mediacodecnew:id/jumpToStart = 0x7f090119
com.android.rockchip.mediacodecnew:layout/abc_dialog_title_material = 0x7f0c000c
com.android.rockchip.mediacodecnew:id/jumpToEnd = 0x7f090118
com.android.rockchip.mediacodecnew:id/middle = 0x7f09014c
com.android.rockchip.mediacodecnew:id/item_touch_helper_previous_elevation = 0x7f090117
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120136
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0d00da
com.android.rockchip.mediacodecnew:id/italic = 0x7f090116
com.android.rockchip.mediacodecnew:id/textinput_prefix_text = 0x7f090250
com.android.rockchip.mediacodecnew:id/is_pooling_container_tag = 0x7f090115
com.android.rockchip.mediacodecnew:id/invisible = 0x7f090113
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.DayNight = 0x7f12020f
com.android.rockchip.mediacodecnew:id/info = 0x7f090111
com.android.rockchip.mediacodecnew:id/included = 0x7f09010f
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_button_outline_color = 0x7f0d00a8
com.android.rockchip.mediacodecnew:id/immediateStop = 0x7f09010e
com.android.rockchip.mediacodecnew:id/none = 0x7f09017b
com.android.rockchip.mediacodecnew:id/ignoreRequest = 0x7f09010b
com.android.rockchip.mediacodecnew:id/ignore = 0x7f09010a
com.android.rockchip.mediacodecnew:id/icon = 0x7f090107
com.android.rockchip.mediacodecnew:id/hotspot_status_text = 0x7f090106
com.android.rockchip.mediacodecnew:id/hotspot_button = 0x7f090105
com.android.rockchip.mediacodecnew:id/horizontal_only = 0x7f090104
com.android.rockchip.mediacodecnew:id/pin = 0x7f0901a1
com.android.rockchip.mediacodecnew:id/honorRequest = 0x7f090103
com.android.rockchip.mediacodecnew:id/homeAsUp = 0x7f090102
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1202df
com.android.rockchip.mediacodecnew:id/home = 0x7f090101
com.android.rockchip.mediacodecnew:id/header_title = 0x7f0900ff
com.android.rockchip.mediacodecnew:id/grouping = 0x7f0900fd
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1200c6
com.android.rockchip.mediacodecnew:id/group_divider = 0x7f0900fc
com.android.rockchip.mediacodecnew:id/graph_wrap = 0x7f0900fb
com.android.rockchip.mediacodecnew:styleable/MaterialShape = 0x7f130059
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Spinner.Underlined = 0x7f120336
com.android.rockchip.mediacodecnew:id/gone = 0x7f0900f9
com.android.rockchip.mediacodecnew:id/glide_custom_view_target_tag = 0x7f0900f8
com.android.rockchip.mediacodecnew:id/ghost_view_holder = 0x7f0900f7
com.android.rockchip.mediacodecnew:id/ghost_view = 0x7f0900f6
com.android.rockchip.mediacodecnew:id/frost = 0x7f0900f4
com.android.rockchip.mediacodecnew:id/right_side = 0x7f0901c0
com.android.rockchip.mediacodecnew:id/forever = 0x7f0900f2
com.android.rockchip.mediacodecnew:id/floating = 0x7f0900f1
com.android.rockchip.mediacodecnew:id/flip = 0x7f0900f0
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1201e0
com.android.rockchip.mediacodecnew:id/fixed = 0x7f0900ef
com.android.rockchip.mediacodecnew:id/fitToContents = 0x7f0900ed
com.android.rockchip.mediacodecnew:id/fitEnd = 0x7f0900eb
com.android.rockchip.mediacodecnew:id/fill = 0x7f0900e6
com.android.rockchip.mediacodecnew:id/fade = 0x7f0900e5
com.android.rockchip.mediacodecnew:id/expanded_menu = 0x7f0900e4
com.android.rockchip.mediacodecnew:id/ethernet_status_text = 0x7f0900e1
com.android.rockchip.mediacodecnew:id/et_username = 0x7f0900e0
com.android.rockchip.mediacodecnew:id/et_password = 0x7f0900dd
com.android.rockchip.mediacodecnew:id/enterAlways = 0x7f0900db
com.android.rockchip.mediacodecnew:id/end = 0x7f0900d9
com.android.rockchip.mediacodecnew:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1200a5
com.android.rockchip.mediacodecnew:id/edit_query = 0x7f0900d6
com.android.rockchip.mediacodecnew:id/east = 0x7f0900d4
com.android.rockchip.mediacodecnew:id/easeOut = 0x7f0900d3
com.android.rockchip.mediacodecnew:id/easeInOut = 0x7f0900d2
com.android.rockchip.mediacodecnew:id/easeIn = 0x7f0900d1
com.android.rockchip.mediacodecnew:id/dragStart = 0x7f0900ce
com.android.rockchip.mediacodecnew:id/fitCenter = 0x7f0900ea
com.android.rockchip.mediacodecnew:id/dragLeft = 0x7f0900cc
com.android.rockchip.mediacodecnew:id/dragEnd = 0x7f0900cb
com.android.rockchip.mediacodecnew:id/dragDown = 0x7f0900ca
com.android.rockchip.mediacodecnew:id/text = 0x7f090242
com.android.rockchip.mediacodecnew:id/dragClockwise = 0x7f0900c9
com.android.rockchip.mediacodecnew:id/dragAnticlockwise = 0x7f0900c8
com.android.rockchip.mediacodecnew:id/open_search_view_content_container = 0x7f090187
com.android.rockchip.mediacodecnew:id/disablePostScroll = 0x7f0900c5
com.android.rockchip.mediacodecnew:id/disableHome = 0x7f0900c3
com.android.rockchip.mediacodecnew:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1200b5
com.android.rockchip.mediacodecnew:id/dimensions = 0x7f0900c1
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0d0126
com.android.rockchip.mediacodecnew:id/textSpacerNoButtons = 0x7f090245
com.android.rockchip.mediacodecnew:id/dialog_button = 0x7f0900c0
com.android.rockchip.mediacodecnew:id/design_navigation_view = 0x7f0900bf
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f120467
com.android.rockchip.mediacodecnew:id/design_menu_item_action_area = 0x7f0900bc
com.android.rockchip.mediacodecnew:id/design_bottom_sheet = 0x7f0900bb
com.android.rockchip.mediacodecnew:id/dependency_ordering = 0x7f0900ba
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0d0132
com.android.rockchip.mediacodecnew:integer/m3_sys_shape_corner_full_corner_family = 0x7f0a0022
com.android.rockchip.mediacodecnew:id/deltaRelative = 0x7f0900b9
com.android.rockchip.mediacodecnew:layout/design_navigation_item_header = 0x7f0c0028
com.android.rockchip.mediacodecnew:id/default_activity_button = 0x7f0900b7
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.RatingBar = 0x7f1200f0
com.android.rockchip.mediacodecnew:id/decor_content_parent = 0x7f0900b6
com.android.rockchip.mediacodecnew:id/date_picker_actions = 0x7f0900b3
com.android.rockchip.mediacodecnew:id/cut = 0x7f0900b2
com.android.rockchip.mediacodecnew:id/custom = 0x7f0900b0
com.android.rockchip.mediacodecnew:id/cradle = 0x7f0900ae
com.android.rockchip.mediacodecnew:id/cos = 0x7f0900ac
com.android.rockchip.mediacodecnew:id/controls_layout = 0x7f0900aa
com.android.rockchip.mediacodecnew:id/control_panel = 0x7f0900a9
com.android.rockchip.mediacodecnew:id/continuousVelocity = 0x7f0900a7
com.android.rockchip.mediacodecnew:id/contiguous = 0x7f0900a6
com.android.rockchip.mediacodecnew:id/contentPanel = 0x7f0900a5
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents = 0x7f120065
com.android.rockchip.mediacodecnew:id/confirm_button = 0x7f0900a1
com.android.rockchip.mediacodecnew:id/compress = 0x7f0900a0
com.android.rockchip.mediacodecnew:id/clip_vertical = 0x7f09009c
com.android.rockchip.mediacodecnew:id/clip_horizontal = 0x7f09009b
com.android.rockchip.mediacodecnew:id/clear_text = 0x7f09009a
com.android.rockchip.mediacodecnew:id/circle_center = 0x7f090099
com.android.rockchip.mediacodecnew:id/checked = 0x7f090097
com.android.rockchip.mediacodecnew:id/checkbox = 0x7f090095
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1202bf
com.android.rockchip.mediacodecnew:id/chains = 0x7f090094
com.android.rockchip.mediacodecnew:id/chain2 = 0x7f090093
com.android.rockchip.mediacodecnew:id/chain = 0x7f090092
com.android.rockchip.mediacodecnew:id/showCustom = 0x7f090200
com.android.rockchip.mediacodecnew:id/center_vertical = 0x7f090091
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f12004f
com.android.rockchip.mediacodecnew:id/cb_enable_smb_video = 0x7f09008c
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f12030e
com.android.rockchip.mediacodecnew:id/cb_enable_smb = 0x7f09008b
com.android.rockchip.mediacodecnew:id/carryVelocity = 0x7f09008a
com.android.rockchip.mediacodecnew:id/captureButton = 0x7f090089
com.android.rockchip.mediacodecnew:id/cancel_button = 0x7f090088
com.android.rockchip.mediacodecnew:id/callMeasure = 0x7f090087
com.android.rockchip.mediacodecnew:id/cache_measures = 0x7f090086
com.android.rockchip.mediacodecnew:id/buttonPanel = 0x7f090085
com.android.rockchip.mediacodecnew:id/btn_test_connection = 0x7f090084
com.android.rockchip.mediacodecnew:id/btn_stereoscopic_scene = 0x7f090082
com.android.rockchip.mediacodecnew:id/btn_step_frame = 0x7f090081
com.android.rockchip.mediacodecnew:id/btn_step_decode = 0x7f090080
com.android.rockchip.mediacodecnew:layout/dialog_image_format_settings = 0x7f0c002f
com.android.rockchip.mediacodecnew:id/btn_speed = 0x7f09007f
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Chip = 0x7f120291
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f120040
com.android.rockchip.mediacodecnew:id/btn_save = 0x7f09007b
com.android.rockchip.mediacodecnew:id/btn_rewind = 0x7f09007a
com.android.rockchip.mediacodecnew:id/btn_refresh_paths = 0x7f090079
com.android.rockchip.mediacodecnew:id/btn_record = 0x7f090078
com.android.rockchip.mediacodecnew:id/btn_play_pause = 0x7f090076
com.android.rockchip.mediacodecnew:id/rectangles = 0x7f0901b7
com.android.rockchip.mediacodecnew:id/btn_open_network = 0x7f090074
com.android.rockchip.mediacodecnew:id/btn_next_video = 0x7f090073
com.android.rockchip.mediacodecnew:id/btn_load_scene = 0x7f090072
com.android.rockchip.mediacodecnew:id/btn_forward = 0x7f090071
com.android.rockchip.mediacodecnew:id/material_timepicker_view = 0x7f09013b
com.android.rockchip.mediacodecnew:id/btn_delete_scene = 0x7f09006e
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0d00b5
com.android.rockchip.mediacodecnew:id/btn_close_settings = 0x7f09006d
com.android.rockchip.mediacodecnew:id/btn_close_popup = 0x7f09006c
com.android.rockchip.mediacodecnew:id/btn_close_network = 0x7f09006b
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Snackbar = 0x7f120447
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0d012b
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0d00a9
com.android.rockchip.mediacodecnew:id/btn_clear_cache = 0x7f09006a
com.android.rockchip.mediacodecnew:id/btn_capture = 0x7f090069
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Year = 0x7f1203b1
com.android.rockchip.mediacodecnew:id/btn_apply_scene = 0x7f090064
com.android.rockchip.mediacodecnew:id/bounceStart = 0x7f090062
com.android.rockchip.mediacodecnew:id/bounce = 0x7f09005f
com.android.rockchip.mediacodecnew:id/bottom = 0x7f09005e
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialDivider = 0x7f1203b5
com.android.rockchip.mediacodecnew:id/bestChoice = 0x7f09005c
com.android.rockchip.mediacodecnew:id/beginning = 0x7f09005b
com.android.rockchip.mediacodecnew:string/mtrl_picker_invalid_range = 0x7f110074
com.android.rockchip.mediacodecnew:id/barrier = 0x7f090058
com.android.rockchip.mediacodecnew:id/autoCompleteToStart = 0x7f090057
com.android.rockchip.mediacodecnew:id/autoCompleteToEnd = 0x7f090056
com.android.rockchip.mediacodecnew:id/autoComplete = 0x7f090055
com.android.rockchip.mediacodecnew:id/auto = 0x7f090054
com.android.rockchip.mediacodecnew:id/async = 0x7f090053
com.android.rockchip.mediacodecnew:id/anticipate = 0x7f090050
com.android.rockchip.mediacodecnew:id/antiClockwise = 0x7f09004f
com.android.rockchip.mediacodecnew:interpolator/mtrl_fast_out_linear_in = 0x7f0b000e
com.android.rockchip.mediacodecnew:id/animateToEnd = 0x7f09004d
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f120160
com.android.rockchip.mediacodecnew:id/allStates = 0x7f09004b
com.android.rockchip.mediacodecnew:integer/m3_card_anim_duration_ms = 0x7f0a000d
com.android.rockchip.mediacodecnew:id/all = 0x7f09004a
com.android.rockchip.mediacodecnew:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0d0119
com.android.rockchip.mediacodecnew:id/alertTitle = 0x7f090048
com.android.rockchip.mediacodecnew:id/adjust = 0x7f090047
com.android.rockchip.mediacodecnew:id/actions = 0x7f090044
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f120437
com.android.rockchip.mediacodecnew:id/action_mode_bar_stub = 0x7f090041
com.android.rockchip.mediacodecnew:id/action_image = 0x7f09003d
com.android.rockchip.mediacodecnew:id/btn_open_smb = 0x7f090075
com.android.rockchip.mediacodecnew:id/action_divider = 0x7f09003c
com.android.rockchip.mediacodecnew:id/action_bar_subtitle = 0x7f090038
com.android.rockchip.mediacodecnew:id/action_bar_container = 0x7f090035
com.android.rockchip.mediacodecnew:attr/indeterminateAnimationType = 0x7f040238
com.android.rockchip.mediacodecnew:id/action_bar = 0x7f090033
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_9 = 0x7f09002f
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_7 = 0x7f09002d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant70 = 0x7f060115
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_6 = 0x7f09002c
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_5 = 0x7f09002b
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ListView.DropDown = 0x7f120325
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_31 = 0x7f090029
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_29 = 0x7f090026
com.android.rockchip.mediacodecnew:attr/dayStyle = 0x7f040170
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_28 = 0x7f090025
com.android.rockchip.mediacodecnew:attr/tabSelectedTextAppearance = 0x7f040428
com.android.rockchip.mediacodecnew:id/CTRL = 0x7f090003
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_27 = 0x7f090024
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_26 = 0x7f090023
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1202af
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_25 = 0x7f090022
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_24 = 0x7f090021
com.android.rockchip.mediacodecnew:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0700ac
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_23 = 0x7f090020
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_22 = 0x7f09001f
com.android.rockchip.mediacodecnew:attr/textBackgroundPanY = 0x7f04045a
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_18 = 0x7f09001a
com.android.rockchip.mediacodecnew:styleable/NavigationRailView = 0x7f13006c
com.android.rockchip.mediacodecnew:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_16 = 0x7f090018
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_14 = 0x7f090016
com.android.rockchip.mediacodecnew:attr/listLayout = 0x7f0402c4
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_secondary = 0x7f0601bf
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_13 = 0x7f090015
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f1202e3
com.android.rockchip.mediacodecnew:attr/materialCalendarHeaderToggleButton = 0x7f0402e8
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_12 = 0x7f090014
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Toolbar = 0x7f1200fb
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_10 = 0x7f090012
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_1 = 0x7f090011
com.android.rockchip.mediacodecnew:id/material_clock_period_am_button = 0x7f09012e
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_0 = 0x7f090010
com.android.rockchip.mediacodecnew:id/accessibility_action_clickable_span = 0x7f09000f
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1202e2
com.android.rockchip.mediacodecnew:id/accelerate = 0x7f09000e
com.android.rockchip.mediacodecnew:id/SYM = 0x7f09000b
com.android.rockchip.mediacodecnew:id/progress_loading_paths = 0x7f0901aa
com.android.rockchip.mediacodecnew:id/SHOW_PROGRESS = 0x7f09000a
com.android.rockchip.mediacodecnew:attr/contentInsetStart = 0x7f040140
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral0 = 0x7f06009c
com.android.rockchip.mediacodecnew:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080075
com.android.rockchip.mediacodecnew:id/SHOW_PATH = 0x7f090009
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f12043e
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Spinner = 0x7f120333
com.android.rockchip.mediacodecnew:id/SHOW_ALL = 0x7f090008
com.android.rockchip.mediacodecnew:id/btn_save_settings = 0x7f09007d
com.android.rockchip.mediacodecnew:id/NO_DEBUG = 0x7f090006
com.android.rockchip.mediacodecnew:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.android.rockchip.mediacodecnew:id/META = 0x7f090005
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_text_field_container_shape = 0x7f0d004d
com.android.rockchip.mediacodecnew:id/FUNCTION = 0x7f090004
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat.ActionBar = 0x7f120275
com.android.rockchip.mediacodecnew:id/BOTTOM_START = 0x7f090002
com.android.rockchip.mediacodecnew:color/design_icon_tint = 0x7f060052
com.android.rockchip.mediacodecnew:id/ALT = 0x7f090000
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0d00c7
com.android.rockchip.mediacodecnew:drawable/tp_video_progress_drawable = 0x7f0800f0
com.android.rockchip.mediacodecnew:attr/transitionDisable = 0x7f0404b6
com.android.rockchip.mediacodecnew:drawable/tp_video_controls_background = 0x7f0800ee
com.android.rockchip.mediacodecnew:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.android.rockchip.mediacodecnew:drawable/tp_speed_item_selected_background = 0x7f0800eb
com.android.rockchip.mediacodecnew:drawable/tp_speed_dropdown_background = 0x7f0800e9
com.android.rockchip.mediacodecnew:id/notification_main_column_container = 0x7f090180
com.android.rockchip.mediacodecnew:drawable/notification_tile_bg = 0x7f0800e4
com.android.rockchip.mediacodecnew:dimen/fastscroll_minimum_range = 0x7f070092
com.android.rockchip.mediacodecnew:color/dim_foreground_material_dark = 0x7f060056
com.android.rockchip.mediacodecnew:drawable/notification_template_icon_bg = 0x7f0800e2
com.android.rockchip.mediacodecnew:drawable/notification_bg_low_pressed = 0x7f0800de
com.android.rockchip.mediacodecnew:color/m3_sys_color_on_tertiary_fixed = 0x7f0601da
com.android.rockchip.mediacodecnew:drawable/notification_action_background = 0x7f0800da
com.android.rockchip.mediacodecnew:drawable/navigation_empty_icon = 0x7f0800d9
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.EditText = 0x7f12030a
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary30 = 0x7f060138
com.android.rockchip.mediacodecnew:drawable/mtrl_tabs_default_indicator = 0x7f0800d8
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f070194
com.android.rockchip.mediacodecnew:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f080023
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_track_decoration = 0x7f0800d7
com.android.rockchip.mediacodecnew:id/material_clock_hand = 0x7f09012c
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_unchecked = 0x7f0800d3
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0800d2
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0800d1
com.android.rockchip.mediacodecnew:drawable/mtrl_navigation_bar_item_background = 0x7f0800c9
com.android.rockchip.mediacodecnew:drawable/mtrl_ic_indeterminate = 0x7f0800c8
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f120319
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f12002a
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0800ba
com.android.rockchip.mediacodecnew:attr/layout_constraintTag = 0x7f04029a
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_default_width = 0x7f0701c2
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button = 0x7f0800b5
com.android.rockchip.mediacodecnew:drawable/material_ic_edit_black_24dp = 0x7f0800ad
com.android.rockchip.mediacodecnew:drawable/material_ic_clear_black_24dp = 0x7f0800ac
com.android.rockchip.mediacodecnew:id/dropdown_menu = 0x7f0900d0
com.android.rockchip.mediacodecnew:drawable/m3_tabs_transparent_background = 0x7f0800a9
com.android.rockchip.mediacodecnew:attr/colorSwitchThumbNormal = 0x7f04012c
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant30 = 0x7f060111
com.android.rockchip.mediacodecnew:drawable/m3_tabs_rounded_line_indicator = 0x7f0800a8
com.android.rockchip.mediacodecnew:drawable/m3_radiobutton_ripple = 0x7f0800a4
com.android.rockchip.mediacodecnew:id/beginOnFirstDraw = 0x7f09005a
com.android.rockchip.mediacodecnew:drawable/m3_password_eye = 0x7f0800a2
com.android.rockchip.mediacodecnew:styleable/ForegroundLinearLayout = 0x7f130038
com.android.rockchip.mediacodecnew:drawable/m3_avd_show_password = 0x7f0800a0
com.android.rockchip.mediacodecnew:drawable/m3_avd_hide_password = 0x7f08009f
com.android.rockchip.mediacodecnew:attr/floatingActionButtonStyle = 0x7f0401e8
com.android.rockchip.mediacodecnew:attr/layout_constraintBaseline_creator = 0x7f04027a
com.android.rockchip.mediacodecnew:drawable/ic_skip_next_white_24 = 0x7f08009b
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0701a0
com.android.rockchip.mediacodecnew:drawable/ic_search_black_24 = 0x7f080099
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_11 = 0x7f090013
com.android.rockchip.mediacodecnew:id/enterAlwaysCollapsed = 0x7f0900dc
com.android.rockchip.mediacodecnew:drawable/ic_fast_forward_white_24 = 0x7f08008b
com.android.rockchip.mediacodecnew:color/m3_dark_primary_text_disable_only = 0x7f060077
com.android.rockchip.mediacodecnew:drawable/ic_arrow_back_black_24 = 0x7f080088
com.android.rockchip.mediacodecnew:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080041
com.android.rockchip.mediacodecnew:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f08007f
com.android.rockchip.mediacodecnew:drawable/btn_checkbox_checked_mtrl = 0x7f08007a
com.android.rockchip.mediacodecnew:attr/ratingBarStyle = 0x7f040397
com.android.rockchip.mediacodecnew:drawable/avd_show_password = 0x7f080079
com.android.rockchip.mediacodecnew:attr/dialogPreferredPadding = 0x7f04017b
com.android.rockchip.mediacodecnew:drawable/avd_hide_password = 0x7f080078
com.android.rockchip.mediacodecnew:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f07016b
com.android.rockchip.mediacodecnew:drawable/abc_textfield_search_material = 0x7f080076
com.android.rockchip.mediacodecnew:attr/alertDialogStyle = 0x7f04002c
com.android.rockchip.mediacodecnew:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080074
com.android.rockchip.mediacodecnew:drawable/abc_text_select_handle_middle_mtrl = 0x7f080070
com.android.rockchip.mediacodecnew:drawable/abc_text_select_handle_left_mtrl = 0x7f08006f
com.android.rockchip.mediacodecnew:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08006d
com.android.rockchip.mediacodecnew:id/transitionToEnd = 0x7f09025d
com.android.rockchip.mediacodecnew:attr/navigationIcon = 0x7f040350
com.android.rockchip.mediacodecnew:drawable/abc_tab_indicator_material = 0x7f08006c
com.android.rockchip.mediacodecnew:styleable/FragmentContainerView = 0x7f13003a
com.android.rockchip.mediacodecnew:drawable/abc_star_half_black_48dp = 0x7f080069
com.android.rockchip.mediacodecnew:id/seekbar_sharpness = 0x7f0901f1
com.android.rockchip.mediacodecnew:dimen/m3_snackbar_action_text_color_alpha = 0x7f0701e7
com.android.rockchip.mediacodecnew:drawable/abc_seekbar_track_material = 0x7f080065
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_primary = 0x7f0601bd
com.android.rockchip.mediacodecnew:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f080061
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0d00cc
com.android.rockchip.mediacodecnew:drawable/abc_ratingbar_material = 0x7f08005c
com.android.rockchip.mediacodecnew:color/cardview_light_background = 0x7f06002b
com.android.rockchip.mediacodecnew:drawable/abc_list_selector_holo_light = 0x7f080058
com.android.rockchip.mediacodecnew:raw/cartoon_fragment = 0x7f100008
com.android.rockchip.mediacodecnew:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f080060
com.android.rockchip.mediacodecnew:id/chronometer = 0x7f090098
com.android.rockchip.mediacodecnew:attr/materialTimePickerTitleStyle = 0x7f040304
com.android.rockchip.mediacodecnew:drawable/abc_list_selector_holo_dark = 0x7f080057
com.android.rockchip.mediacodecnew:dimen/abc_dialog_padding_top_material = 0x7f070025
com.android.rockchip.mediacodecnew:drawable/abc_list_selector_disabled_holo_dark = 0x7f080055
com.android.rockchip.mediacodecnew:id/touch_outside = 0x7f09025b
com.android.rockchip.mediacodecnew:drawable/abc_list_pressed_holo_light = 0x7f080052
com.android.rockchip.mediacodecnew:id/action_mode_close_button = 0x7f090042
com.android.rockchip.mediacodecnew:attr/autoCompleteTextViewStyle = 0x7f040040
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0800d5
com.android.rockchip.mediacodecnew:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1200b3
com.android.rockchip.mediacodecnew:drawable/abc_list_pressed_holo_dark = 0x7f080051
com.android.rockchip.mediacodecnew:drawable/abc_list_longpressed_holo = 0x7f080050
com.android.rockchip.mediacodecnew:drawable/abc_list_focused_holo = 0x7f08004f
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_primary_container = 0x7f06017c
com.android.rockchip.mediacodecnew:drawable/abc_list_divider_mtrl_alpha = 0x7f08004e
com.android.rockchip.mediacodecnew:attr/drawableSize = 0x7f04018f
com.android.rockchip.mediacodecnew:drawable/abc_list_divider_material = 0x7f08004d
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f120406
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error100 = 0x7f0600ea
com.android.rockchip.mediacodecnew:drawable/abc_ic_search_api_material = 0x7f080049
com.android.rockchip.mediacodecnew:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080048
com.android.rockchip.mediacodecnew:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080047
com.android.rockchip.mediacodecnew:styleable/KeyTimeCycle = 0x7f130045
com.android.rockchip.mediacodecnew:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080046
com.android.rockchip.mediacodecnew:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080044
com.android.rockchip.mediacodecnew:drawable/abc_control_background_material = 0x7f08003b
com.android.rockchip.mediacodecnew:attr/colorSecondary = 0x7f04011d
com.android.rockchip.mediacodecnew:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080037
com.android.rockchip.mediacodecnew:attr/colorErrorContainer = 0x7f0400fd
com.android.rockchip.mediacodecnew:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080034
com.android.rockchip.mediacodecnew:attr/round = 0x7f0403a7
com.android.rockchip.mediacodecnew:drawable/abc_btn_radio_material_anim = 0x7f080033
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0702bd
com.android.rockchip.mediacodecnew:drawable/test_level_drawable = 0x7f0800e6
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0d0174
com.android.rockchip.mediacodecnew:integer/mtrl_switch_track_viewport_width = 0x7f0a003d
com.android.rockchip.mediacodecnew:drawable/abc_btn_colored_material = 0x7f080030
com.android.rockchip.mediacodecnew:attr/placeholderTextColor = 0x7f040381
com.android.rockchip.mediacodecnew:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08002f
com.android.rockchip.mediacodecnew:macro/m3_comp_input_chip_container_shape = 0x7f0d005c
com.android.rockchip.mediacodecnew:drawable/ic_pause_white_24 = 0x7f080097
com.android.rockchip.mediacodecnew:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f070178
com.android.rockchip.mediacodecnew:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f080028
com.android.rockchip.mediacodecnew:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f080025
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f08001f
com.android.rockchip.mediacodecnew:string/mtrl_switch_thumb_path_pressed = 0x7f11008e
com.android.rockchip.mediacodecnew:id/seekbar_saturation = 0x7f0901f0
com.android.rockchip.mediacodecnew:dimen/compat_control_corner_material = 0x7f07005a
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f08001e
com.android.rockchip.mediacodecnew:id/search_go_btn = 0x7f0901d6
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f080019
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f080018
com.android.rockchip.mediacodecnew:dimen/m3_ripple_hovered_alpha = 0x7f0701cf
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f080016
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f080015
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.BodyMedium = 0x7f1201e7
com.android.rockchip.mediacodecnew:id/snap = 0x7f090209
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f080014
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button = 0x7f120359
com.android.rockchip.mediacodecnew:id/switch_to_camera_button = 0x7f090230
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f080013
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f080012
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1201bd
com.android.rockchip.mediacodecnew:id/report_drawn = 0x7f0901b9
com.android.rockchip.mediacodecnew:id/mtrl_picker_header_title_and_selection = 0x7f090165
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f080011
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f08000f
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1201d9
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant70 = 0x7f060217
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f08000e
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1203a0
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0d0139
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f08000d
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_button_path_group_name = 0x7f11005c
com.android.rockchip.mediacodecnew:dimen/m3_simple_item_color_hovered_alpha = 0x7f0701e1
com.android.rockchip.mediacodecnew:drawable/$m3_avd_hide_password__0 = 0x7f080007
com.android.rockchip.mediacodecnew:drawable/$ic_launcher_foreground__0 = 0x7f080006
com.android.rockchip.mediacodecnew:drawable/$avd_show_password__1 = 0x7f080004
com.android.rockchip.mediacodecnew:drawable/$avd_show_password__0 = 0x7f080003
com.android.rockchip.mediacodecnew:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0700de
com.android.rockchip.mediacodecnew:drawable/$avd_hide_password__0 = 0x7f080000
com.android.rockchip.mediacodecnew:id/seekbar_ldc = 0x7f0901ed
com.android.rockchip.mediacodecnew:attr/colorPrimary = 0x7f040115
com.android.rockchip.mediacodecnew:attr/actionModePasteDrawable = 0x7f04001a
com.android.rockchip.mediacodecnew:dimen/tooltip_y_offset_non_touch = 0x7f070319
com.android.rockchip.mediacodecnew:dimen/tooltip_precise_anchor_extra_offset = 0x7f070316
com.android.rockchip.mediacodecnew:dimen/tooltip_margin = 0x7f070315
com.android.rockchip.mediacodecnew:dimen/notification_top_pad_large_text = 0x7f070312
com.android.rockchip.mediacodecnew:dimen/notification_top_pad = 0x7f070311
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1203a8
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f0701fe
com.android.rockchip.mediacodecnew:dimen/notification_small_icon_background_padding = 0x7f07030e
com.android.rockchip.mediacodecnew:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0a0024
com.android.rockchip.mediacodecnew:dimen/notification_right_icon_size = 0x7f07030c
com.android.rockchip.mediacodecnew:dimen/notification_large_icon_height = 0x7f070308
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.Chip = 0x7f120114
com.android.rockchip.mediacodecnew:id/view_tree_view_model_store_owner = 0x7f090292
com.android.rockchip.mediacodecnew:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f070303
com.android.rockchip.mediacodecnew:id/noScroll = 0x7f090179
com.android.rockchip.mediacodecnew:dimen/mtrl_tooltip_padding = 0x7f070302
com.android.rockchip.mediacodecnew:dimen/mtrl_tooltip_minWidth = 0x7f070301
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_track = 0x7f0800d6
com.android.rockchip.mediacodecnew:dimen/mtrl_toolbar_default_height = 0x7f0702fd
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_start_icon_margin_end = 0x7f0702fc
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_end_icon_margin_start = 0x7f0702fa
com.android.rockchip.mediacodecnew:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0d005b
com.android.rockchip.mediacodecnew:dimen/m3_menu_elevation = 0x7f0701b6
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_box_corner_radius_small = 0x7f0702f5
com.android.rockchip.mediacodecnew:dimen/mtrl_switch_track_height = 0x7f0702f2
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f120422
com.android.rockchip.mediacodecnew:integer/mtrl_card_anim_delay_ms = 0x7f0a0033
com.android.rockchip.mediacodecnew:drawable/$m3_avd_show_password__2 = 0x7f08000c
com.android.rockchip.mediacodecnew:drawable/tp_speed_menu_background = 0x7f0800ec
com.android.rockchip.mediacodecnew:drawable/design_snackbar_background = 0x7f080086
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f120039
com.android.rockchip.mediacodecnew:dimen/mtrl_switch_thumb_icon_size = 0x7f0702f0
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_button_label_text_type = 0x7f0d0046
com.android.rockchip.mediacodecnew:dimen/mtrl_snackbar_padding_horizontal = 0x7f0702ed
com.android.rockchip.mediacodecnew:raw/brightness_fragment = 0x7f100006
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_track_height = 0x7f0702e5
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_thumb_radius = 0x7f0702e3
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_label_radius = 0x7f0702e0
com.android.rockchip.mediacodecnew:attr/passwordToggleTint = 0x7f040375
com.android.rockchip.mediacodecnew:attr/liftOnScrollTargetViewId = 0x7f0402ba
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_label_padding = 0x7f0702df
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_halo_radius = 0x7f0702de
com.android.rockchip.mediacodecnew:color/switch_thumb_disabled_material_dark = 0x7f0602de
com.android.rockchip.mediacodecnew:dimen/mtrl_shape_corner_size_large_component = 0x7f0702db
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0d00e8
com.android.rockchip.mediacodecnew:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0d000c
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_track_thickness = 0x7f0702da
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0702d8
com.android.rockchip.mediacodecnew:styleable/State = 0x7f130083
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_size_small = 0x7f0702d5
com.android.rockchip.mediacodecnew:dimen/notification_action_text_size = 0x7f070305
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_size_medium = 0x7f0702d4
com.android.rockchip.mediacodecnew:styleable/MaterialTextView = 0x7f13005c
com.android.rockchip.mediacodecnew:raw/beauty_fragment = 0x7f100003
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0600e5
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_size_extra_small = 0x7f0702d3
com.android.rockchip.mediacodecnew:id/startToEnd = 0x7f090222
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_inset_small = 0x7f0702d0
com.android.rockchip.mediacodecnew:attr/colorSurfaceDim = 0x7f040129
com.android.rockchip.mediacodecnew:attr/actionModeCloseButtonStyle = 0x7f040014
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_inset_medium = 0x7f0702cf
com.android.rockchip.mediacodecnew:color/mtrl_navigation_bar_item_tint = 0x7f0602b4
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0702ce
com.android.rockchip.mediacodecnew:integer/hide_password_duration = 0x7f0a0008
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_inset = 0x7f0702cd
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_compact_width = 0x7f0702c5
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0702bf
com.android.rockchip.mediacodecnew:drawable/abc_ic_go_search_api_material = 0x7f080042
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_elevation = 0x7f0702be
com.android.rockchip.mediacodecnew:dimen/mtrl_min_touch_target_size = 0x7f0702bb
com.android.rockchip.mediacodecnew:dimen/mtrl_low_ripple_default_alpha = 0x7f0702b7
com.android.rockchip.mediacodecnew:styleable/ActionMenuView = 0x7f130003
com.android.rockchip.mediacodecnew:layout/mtrl_picker_header_toggle = 0x7f0c006a
com.android.rockchip.mediacodecnew:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0702b5
com.android.rockchip.mediacodecnew:id/action_bar_root = 0x7f090036
com.android.rockchip.mediacodecnew:dimen/mtrl_fab_translation_z_pressed = 0x7f0702b2
com.android.rockchip.mediacodecnew:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0702b1
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3 = 0x7f120124
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f120076
com.android.rockchip.mediacodecnew:attr/hintAnimationEnabled = 0x7f04021f
com.android.rockchip.mediacodecnew:drawable/tp_speed_item_background = 0x7f0800ea
com.android.rockchip.mediacodecnew:color/m3_textfield_filled_background_color = 0x7f0601eb
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_item_icon_padding = 0x7f0702c0
com.android.rockchip.mediacodecnew:dimen/mtrl_fab_elevation = 0x7f0702af
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0702ad
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0d007d
com.android.rockchip.mediacodecnew:layout/material_clockface_view = 0x7f0c0044
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0800ce
com.android.rockchip.mediacodecnew:id/seekbar_wb_blue = 0x7f0901f2
com.android.rockchip.mediacodecnew:drawable/abc_ic_clear_material = 0x7f080040
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_top_padding = 0x7f0702ab
com.android.rockchip.mediacodecnew:drawable/abc_textfield_default_mtrl_alpha = 0x7f080073
com.android.rockchip.mediacodecnew:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f07017e
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0702aa
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0702a6
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f120167
com.android.rockchip.mediacodecnew:layout/abc_popup_menu_header_item_layout = 0x7f0c0012
com.android.rockchip.mediacodecnew:dimen/abc_star_medium = 0x7f07003c
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_icon_size = 0x7f0702a5
com.android.rockchip.mediacodecnew:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08005e
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0702a4
com.android.rockchip.mediacodecnew:attr/counterTextColor = 0x7f04015e
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f080020
com.android.rockchip.mediacodecnew:id/open_search_view_edit_text = 0x7f09018a
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_elevation = 0x7f0702a2
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f120463
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_margin = 0x7f070068
com.android.rockchip.mediacodecnew:attr/textAppearanceLabelSmall = 0x7f040448
com.android.rockchip.mediacodecnew:color/material_grey_100 = 0x7f060243
com.android.rockchip.mediacodecnew:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f07029d
com.android.rockchip.mediacodecnew:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f06008d
com.android.rockchip.mediacodecnew:dimen/mtrl_chip_text_size = 0x7f07029b
com.android.rockchip.mediacodecnew:dimen/mtrl_chip_pressed_translation_z = 0x7f07029a
com.android.rockchip.mediacodecnew:dimen/mtrl_card_spacing = 0x7f070299
com.android.rockchip.mediacodecnew:string/mtrl_picker_text_input_year_abbr = 0x7f110084
com.android.rockchip.mediacodecnew:dimen/mtrl_card_dragged_z = 0x7f070297
com.android.rockchip.mediacodecnew:dimen/mtrl_card_corner_radius = 0x7f070296
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f120396
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1202c8
com.android.rockchip.mediacodecnew:color/design_dark_default_color_surface = 0x7f06003c
com.android.rockchip.mediacodecnew:dimen/mtrl_card_checked_icon_margin = 0x7f070294
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_year_vertical_padding = 0x7f070292
com.android.rockchip.mediacodecnew:styleable/ListPopupWindow = 0x7f13004c
com.android.rockchip.mediacodecnew:styleable/BottomNavigationView = 0x7f130016
com.android.rockchip.mediacodecnew:style/Widget.Material3.BottomNavigationView = 0x7f120354
com.android.rockchip.mediacodecnew:raw/fxaa_pc = 0x7f100013
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f07028e
com.android.rockchip.mediacodecnew:color/mtrl_choice_chip_background_color = 0x7f0602a7
com.android.rockchip.mediacodecnew:attr/touchAnchorId = 0x7f0404a6
com.android.rockchip.mediacodecnew:dimen/m3_sys_elevation_level1 = 0x7f0701ea
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_title_baseline_to_top = 0x7f07028d
com.android.rockchip.mediacodecnew:color/material_grey_600 = 0x7f060246
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f07028a
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_navigation_height = 0x7f070285
com.android.rockchip.mediacodecnew:animator/design_fab_hide_motion_spec = 0x7f020001
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f070284
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1203bc
com.android.rockchip.mediacodecnew:attr/reactiveGuide_animateChange = 0x7f04039a
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary95 = 0x7f060234
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_month_vertical_padding = 0x7f070283
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0800be
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f070281
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_text_padding = 0x7f07027d
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_selection_line_height = 0x7f07027c
com.android.rockchip.mediacodecnew:id/fullscreen_header = 0x7f0900f5
com.android.rockchip.mediacodecnew:attr/errorIconTint = 0x7f0401b5
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f070278
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary60 = 0x7f060230
com.android.rockchip.mediacodecnew:attr/indicatorDirectionLinear = 0x7f04023c
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_day_width = 0x7f070274
com.android.rockchip.mediacodecnew:attr/boxCollapsedPaddingTop = 0x7f040083
com.android.rockchip.mediacodecnew:color/m3_chip_background_color = 0x7f06006f
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_day_vertical_padding = 0x7f070273
com.android.rockchip.mediacodecnew:dimen/m3_comp_badge_size = 0x7f070100
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_day_corner = 0x7f07026f
com.android.rockchip.mediacodecnew:attr/buttonBarNeutralButtonStyle = 0x7f04008f
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_content_padding = 0x7f07026e
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_action_padding = 0x7f07026c
com.android.rockchip.mediacodecnew:attr/drawableTint = 0x7f040191
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_action_height = 0x7f07026b
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_text_btn_padding_right = 0x7f070267
com.android.rockchip.mediacodecnew:id/tag_window_insets_animation_callback = 0x7f090240
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f070263
com.android.rockchip.mediacodecnew:drawable/abc_btn_borderless_material = 0x7f08002b
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_medium1 = 0x7f0a0017
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_pressed_z = 0x7f070262
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_padding_left = 0x7f07025f
com.android.rockchip.mediacodecnew:id/collapseActionView = 0x7f09009f
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_letter_spacing = 0x7f07025c
com.android.rockchip.mediacodecnew:id/TOP_START = 0x7f09000d
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_inset = 0x7f07025b
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ProgressBar = 0x7f1200ee
com.android.rockchip.mediacodecnew:dimen/m3_small_fab_size = 0x7f0701e6
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f060172
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_icon_btn_padding_left = 0x7f070259
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0d016e
com.android.rockchip.mediacodecnew:attr/chipIconTint = 0x7f0400c7
com.android.rockchip.mediacodecnew:attr/constraintSet = 0x7f040134
com.android.rockchip.mediacodecnew:dimen/notification_content_margin_start = 0x7f070307
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_disabled_elevation = 0x7f070254
com.android.rockchip.mediacodecnew:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07024f
com.android.rockchip.mediacodecnew:attr/topInsetScrimEnabled = 0x7f0404a5
com.android.rockchip.mediacodecnew:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f07024e
com.android.rockchip.mediacodecnew:attr/coordinatorLayoutStyle = 0x7f04014c
com.android.rockchip.mediacodecnew:attr/tabTextAppearance = 0x7f04042b
com.android.rockchip.mediacodecnew:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f07024d
com.android.rockchip.mediacodecnew:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f07024c
com.android.rockchip.mediacodecnew:layout/abc_screen_simple = 0x7f0c0015
com.android.rockchip.mediacodecnew:id/match_constraint = 0x7f090127
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_4 = 0x7f09002a
com.android.rockchip.mediacodecnew:attr/actionBarItemBackground = 0x7f040004
com.android.rockchip.mediacodecnew:dimen/mtrl_badge_with_text_size = 0x7f07024b
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_z = 0x7f070269
com.android.rockchip.mediacodecnew:dimen/mtrl_badge_text_size = 0x7f070248
com.android.rockchip.mediacodecnew:styleable/RecyclerView = 0x7f130077
com.android.rockchip.mediacodecnew:dimen/mtrl_badge_size = 0x7f070246
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.android.rockchip.mediacodecnew:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070245
com.android.rockchip.mediacodecnew:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f070243
com.android.rockchip.mediacodecnew:style/Widget.Material3.TabLayout.Secondary = 0x7f1203da
com.android.rockchip.mediacodecnew:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f07023f
com.android.rockchip.mediacodecnew:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f12020c
com.android.rockchip.mediacodecnew:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f07023e
com.android.rockchip.mediacodecnew:dimen/material_textinput_max_width = 0x7f07023a
com.android.rockchip.mediacodecnew:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f070234
com.android.rockchip.mediacodecnew:attr/closeIconSize = 0x7f0400e4
com.android.rockchip.mediacodecnew:dimen/notification_small_icon_size_as_large = 0x7f07030f
com.android.rockchip.mediacodecnew:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f070233
com.android.rockchip.mediacodecnew:layout/notification_action_tombstone = 0x7f0c0071
com.android.rockchip.mediacodecnew:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f070232
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.SearchView = 0x7f12032f
com.android.rockchip.mediacodecnew:id/embed = 0x7f0900d8
com.android.rockchip.mediacodecnew:color/background_floating_material_dark = 0x7f06001d
com.android.rockchip.mediacodecnew:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f070231
com.android.rockchip.mediacodecnew:dimen/material_emphasis_disabled = 0x7f07022b
com.android.rockchip.mediacodecnew:dimen/material_divider_thickness = 0x7f07022a
com.android.rockchip.mediacodecnew:id/et_server_ip = 0x7f0900de
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_elevation = 0x7f070256
com.android.rockchip.mediacodecnew:dimen/material_cursor_width = 0x7f070229
com.android.rockchip.mediacodecnew:dimen/material_cursor_inset = 0x7f070228
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f07014f
com.android.rockchip.mediacodecnew:dimen/material_clock_size = 0x7f070227
com.android.rockchip.mediacodecnew:dimen/material_clock_period_toggle_width = 0x7f070226
com.android.rockchip.mediacodecnew:attr/textOutlineColor = 0x7f04046a
com.android.rockchip.mediacodecnew:dimen/material_clock_period_toggle_vertical_gap = 0x7f070225
com.android.rockchip.mediacodecnew:dimen/m3_alert_dialog_icon_size = 0x7f0700a2
com.android.rockchip.mediacodecnew:dimen/material_clock_number_text_size = 0x7f070222
com.android.rockchip.mediacodecnew:attr/carousel_touchUp_velocityThreshold = 0x7f0400ae
com.android.rockchip.mediacodecnew:dimen/material_clock_hand_padding = 0x7f070220
com.android.rockchip.mediacodecnew:color/m3_dark_default_color_secondary_text = 0x7f060074
com.android.rockchip.mediacodecnew:dimen/material_bottom_sheet_max_width = 0x7f07021a
com.android.rockchip.mediacodecnew:dimen/m3_timepicker_window_elevation = 0x7f070218
com.android.rockchip.mediacodecnew:dimen/m3_timepicker_display_stroke_width = 0x7f070217
com.android.rockchip.mediacodecnew:color/m3_efab_ripple_color_selector = 0x7f060084
com.android.rockchip.mediacodecnew:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070215
com.android.rockchip.mediacodecnew:style/Base.Theme.MediacodecNew = 0x7f120078
com.android.rockchip.mediacodecnew:id/tag_accessibility_actions = 0x7f090234
com.android.rockchip.mediacodecnew:attr/passwordToggleTintMode = 0x7f040376
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary60 = 0x7f06023d
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f07020d
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0d0133
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600ba
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f07020a
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f070209
com.android.rockchip.mediacodecnew:id/open_search_view_search_prefix = 0x7f09018e
com.android.rockchip.mediacodecnew:attr/badgeGravity = 0x7f040055
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary50 = 0x7f06012d
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f070207
com.android.rockchip.mediacodecnew:attr/closeIconEnabled = 0x7f0400e2
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f070200
com.android.rockchip.mediacodecnew:id/month_navigation_fragment_toggle = 0x7f090150
com.android.rockchip.mediacodecnew:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f07009a
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f0701fb
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f0701fa
com.android.rockchip.mediacodecnew:attr/materialCalendarHeaderConfirmButton = 0x7f0402e3
com.android.rockchip.mediacodecnew:color/primary_material_light = 0x7f0602d3
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f0701f9
com.android.rockchip.mediacodecnew:raw/glitch_fragment = 0x7f100015
com.android.rockchip.mediacodecnew:attr/panelMenuListWidth = 0x7f040371
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0701f2
com.android.rockchip.mediacodecnew:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080043
com.android.rockchip.mediacodecnew:attr/simpleItemSelectedColor = 0x7f0403d1
com.android.rockchip.mediacodecnew:attr/placeholder_emptyVisibility = 0x7f040382
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0701f0
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_button_icon_path_checked = 0x7f110057
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0701ef
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0d014e
com.android.rockchip.mediacodecnew:id/fill_vertical = 0x7f0900e8
com.android.rockchip.mediacodecnew:dimen/m3_sys_elevation_level5 = 0x7f0701ee
com.android.rockchip.mediacodecnew:dimen/m3_sys_elevation_level4 = 0x7f0701ed
com.android.rockchip.mediacodecnew:attr/animateNavigationIcon = 0x7f040034
com.android.rockchip.mediacodecnew:dimen/m3_sys_elevation_level2 = 0x7f0701eb
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface_bright = 0x7f060162
com.android.rockchip.mediacodecnew:dimen/m3_snackbar_margin = 0x7f0701e8
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0601b3
com.android.rockchip.mediacodecnew:dimen/m3_simple_item_color_selected_alpha = 0x7f0701e2
com.android.rockchip.mediacodecnew:dimen/m3_searchview_elevation = 0x7f0701db
com.android.rockchip.mediacodecnew:attr/textAppearanceDisplaySmall = 0x7f04043c
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral22 = 0x7f0600fb
com.android.rockchip.mediacodecnew:id/action_bar_activity_content = 0x7f090034
com.android.rockchip.mediacodecnew:string/mtrl_picker_announce_current_selection = 0x7f110068
com.android.rockchip.mediacodecnew:anim/abc_slide_in_bottom = 0x7f010006
com.android.rockchip.mediacodecnew:dimen/m3_searchview_divider_size = 0x7f0701da
com.android.rockchip.mediacodecnew:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0701d8
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_year = 0x7f0c005f
com.android.rockchip.mediacodecnew:dimen/m3_searchbar_margin_horizontal = 0x7f0701d4
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f08001c
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0d0169
com.android.rockchip.mediacodecnew:dimen/m3_ripple_focused_alpha = 0x7f0701ce
com.android.rockchip.mediacodecnew:attr/materialCardViewFilledStyle = 0x7f0402ef
com.android.rockchip.mediacodecnew:dimen/m3_ripple_default_alpha = 0x7f0701cd
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f120056
com.android.rockchip.mediacodecnew:attr/subtitleCentered = 0x7f040405
com.android.rockchip.mediacodecnew:dimen/item_touch_helper_swipe_escape_velocity = 0x7f07009c
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_item_padding_top = 0x7f0701cb
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0701ca
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0701c9
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_item_min_height = 0x7f0701c8
com.android.rockchip.mediacodecnew:id/actionDownUp = 0x7f090031
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0701c7
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0701c5
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_icon_size = 0x7f0701c4
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f070204
com.android.rockchip.mediacodecnew:attr/suffixText = 0x7f040409
com.android.rockchip.mediacodecnew:dimen/m3_navigation_item_vertical_padding = 0x7f0701bf
com.android.rockchip.mediacodecnew:dimen/m3_navigation_item_shape_inset_top = 0x7f0701be
com.android.rockchip.mediacodecnew:dimen/m3_navigation_item_shape_inset_start = 0x7f0701bd
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_tick_radius = 0x7f0702e4
com.android.rockchip.mediacodecnew:dimen/design_tab_text_size_2line = 0x7f07008c
com.android.rockchip.mediacodecnew:dimen/m3_navigation_item_icon_padding = 0x7f0701ba
com.android.rockchip.mediacodecnew:dimen/m3_navigation_item_horizontal_padding = 0x7f0701b9
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f120075
com.android.rockchip.mediacodecnew:attr/labelStyle = 0x7f04026a
com.android.rockchip.mediacodecnew:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0701b5
com.android.rockchip.mediacodecnew:id/menu_image_format = 0x7f090143
com.android.rockchip.mediacodecnew:dimen/m3_large_fab_size = 0x7f0701b4
com.android.rockchip.mediacodecnew:dimen/m3_large_fab_max_image_size = 0x7f0701b3
com.android.rockchip.mediacodecnew:styleable/AppBarLayoutStates = 0x7f13000b
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.MediumComponent = 0x7f120178
com.android.rockchip.mediacodecnew:dimen/m3_fab_corner_size = 0x7f0701b0
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f12008d
com.android.rockchip.mediacodecnew:id/default_content = 0x7f0900b8
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary70 = 0x7f06013c
com.android.rockchip.mediacodecnew:dimen/m3_fab_border_width = 0x7f0701af
com.android.rockchip.mediacodecnew:dimen/m3_extended_fab_bottom_padding = 0x7f0701a9
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f12012b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600b7
com.android.rockchip.mediacodecnew:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0701a6
com.android.rockchip.mediacodecnew:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0701a5
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0701a1
com.android.rockchip.mediacodecnew:id/special_effects_controller_view_tag = 0x7f09020d
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f07019e
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f060174
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f07019d
com.android.rockchip.mediacodecnew:attr/textureBlurFactor = 0x7f04046f
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f07019c
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f070199
com.android.rockchip.mediacodecnew:style/Theme.Material3.Light.DialogWhenLarge = 0x7f120241
com.android.rockchip.mediacodecnew:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f070198
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0d0010
com.android.rockchip.mediacodecnew:drawable/abc_seekbar_thumb_material = 0x7f080063
com.android.rockchip.mediacodecnew:attr/searchPrefixText = 0x7f0403b0
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f080010
com.android.rockchip.mediacodecnew:attr/cornerFamilyTopRight = 0x7f040152
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_track_height = 0x7f070191
com.android.rockchip.mediacodecnew:id/edge = 0x7f0900d5
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f07018e
com.android.rockchip.mediacodecnew:style/ShapeAppearance.MaterialComponents.Badge = 0x7f12017d
com.android.rockchip.mediacodecnew:color/material_grey_50 = 0x7f060245
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f07018d
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f07018a
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f070189
com.android.rockchip.mediacodecnew:dimen/m3_comp_snackbar_container_elevation = 0x7f070183
com.android.rockchip.mediacodecnew:id/navigation_bar_item_labels_group = 0x7f090170
com.android.rockchip.mediacodecnew:attr/shapeCornerFamily = 0x7f0403c2
com.android.rockchip.mediacodecnew:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f070181
com.android.rockchip.mediacodecnew:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f070180
com.android.rockchip.mediacodecnew:dimen/m3_comp_sheet_side_docked_container_width = 0x7f07017c
com.android.rockchip.mediacodecnew:attr/drawableLeftCompat = 0x7f04018d
com.android.rockchip.mediacodecnew:color/tp_video_play_button_bg_pressed = 0x7f0602ee
com.android.rockchip.mediacodecnew:dimen/m3_searchbar_height = 0x7f0701d3
com.android.rockchip.mediacodecnew:id/open_search_view_dummy_toolbar = 0x7f090189
com.android.rockchip.mediacodecnew:animator/m3_card_state_list_anim = 0x7f02000d
com.android.rockchip.mediacodecnew:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f07017b
com.android.rockchip.mediacodecnew:integer/m3_chip_anim_duration = 0x7f0a000e
com.android.rockchip.mediacodecnew:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f070176
com.android.rockchip.mediacodecnew:dimen/mtrl_tooltip_cornerSize = 0x7f0702ff
com.android.rockchip.mediacodecnew:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f070174
com.android.rockchip.mediacodecnew:style/Widget.Design.BottomNavigationView = 0x7f12033e
com.android.rockchip.mediacodecnew:dimen/m3_comp_search_view_container_elevation = 0x7f070171
com.android.rockchip.mediacodecnew:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f07016a
com.android.rockchip.mediacodecnew:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f070168
com.android.rockchip.mediacodecnew:color/mtrl_filled_stroke_color = 0x7f0602b0
com.android.rockchip.mediacodecnew:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f070167
com.android.rockchip.mediacodecnew:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f070166
com.android.rockchip.mediacodecnew:attr/contentPaddingRight = 0x7f040146
com.android.rockchip.mediacodecnew:color/material_personalized_color_primary_inverse = 0x7f060270
com.android.rockchip.mediacodecnew:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f070165
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_error = 0x7f060151
com.android.rockchip.mediacodecnew:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f070164
com.android.rockchip.mediacodecnew:dimen/mtrl_high_ripple_default_alpha = 0x7f0702b3
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070206
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f120030
com.android.rockchip.mediacodecnew:attr/itemShapeInsetTop = 0x7f04025b
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f0702f8
com.android.rockchip.mediacodecnew:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f070163
com.android.rockchip.mediacodecnew:attr/showPaths = 0x7f0403ca
com.android.rockchip.mediacodecnew:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f070161
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary30 = 0x7f06022d
com.android.rockchip.mediacodecnew:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f070160
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral6 = 0x7f0600a8
com.android.rockchip.mediacodecnew:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f07015e
com.android.rockchip.mediacodecnew:id/never = 0x7f090176
com.android.rockchip.mediacodecnew:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0800b2
com.android.rockchip.mediacodecnew:anim/m3_side_sheet_exit_to_left = 0x7f010027
com.android.rockchip.mediacodecnew:attr/applyMotionScene = 0x7f040038
com.android.rockchip.mediacodecnew:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f07015d
com.android.rockchip.mediacodecnew:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f07015c
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f120452
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_text_field_outline_width = 0x7f07015b
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f070157
com.android.rockchip.mediacodecnew:drawable/abc_list_selector_background_transition_holo_light = 0x7f080054
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_card_container_elevation = 0x7f070152
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f120458
com.android.rockchip.mediacodecnew:id/disjoint = 0x7f0900c7
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f07014c
com.android.rockchip.mediacodecnew:styleable/AppBarLayout = 0x7f13000a
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_drawer_icon_size = 0x7f070143
com.android.rockchip.mediacodecnew:styleable/MotionHelper = 0x7f130065
com.android.rockchip.mediacodecnew:id/search_close_btn = 0x7f0901d4
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f070142
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface_variant = 0x7f060169
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f07013f
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f07013d
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f07013c
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0800bc
com.android.rockchip.mediacodecnew:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002
com.android.rockchip.mediacodecnew:color/m3_sys_color_secondary_fixed_dim = 0x7f0601df
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_bar_container_height = 0x7f07013b
com.android.rockchip.mediacodecnew:drawable/btn_radio_off_mtrl = 0x7f08007e
com.android.rockchip.mediacodecnew:raw/snow_fragment = 0x7f100028
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f07014b
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_bar_container_elevation = 0x7f07013a
com.android.rockchip.mediacodecnew:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401cb
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f070139
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary70 = 0x7f0600d6
com.android.rockchip.mediacodecnew:drawable/abc_star_black_48dp = 0x7f080068
com.android.rockchip.mediacodecnew:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080029
com.android.rockchip.mediacodecnew:dimen/m3_comp_menu_container_elevation = 0x7f070137
com.android.rockchip.mediacodecnew:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f070135
com.android.rockchip.mediacodecnew:dimen/m3_toolbar_text_size_title = 0x7f070219
com.android.rockchip.mediacodecnew:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f070133
com.android.rockchip.mediacodecnew:attr/insetForeground = 0x7f040240
com.android.rockchip.mediacodecnew:dimen/m3_comp_input_chip_container_height = 0x7f070132
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1201b0
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0d001f
com.android.rockchip.mediacodecnew:dimen/m3_comp_input_chip_container_elevation = 0x7f070131
com.android.rockchip.mediacodecnew:string/hide_bottom_view_on_scroll_behavior = 0x7f11002d
com.android.rockchip.mediacodecnew:integer/abc_config_activityDefaultDur = 0x7f0a0000
com.android.rockchip.mediacodecnew:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f070130
com.android.rockchip.mediacodecnew:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f07012d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary80 = 0x7f06013d
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f07012a
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_large_icon_size = 0x7f07011d
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_card_icon_size = 0x7f070129
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_card_container_elevation = 0x7f070125
com.android.rockchip.mediacodecnew:string/mtrl_picker_start_date_description = 0x7f11007e
com.android.rockchip.mediacodecnew:drawable/abc_spinner_textfield_background_material = 0x7f080067
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_small_icon_size = 0x7f070121
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f07011f
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f07011e
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0702bc
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f07011a
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f120296
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f070119
com.android.rockchip.mediacodecnew:layout/abc_list_menu_item_checkbox = 0x7f0c000e
com.android.rockchip.mediacodecnew:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f07016f
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionMode = 0x7f1200cb
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f070118
com.android.rockchip.mediacodecnew:style/Theme.MediacodecNew = 0x7f120273
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_container_height = 0x7f070117
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_container_elevation = 0x7f070116
com.android.rockchip.mediacodecnew:dimen/mtrl_switch_thumb_elevation = 0x7f0702ef
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f070110
com.android.rockchip.mediacodecnew:dimen/m3_comp_elevated_card_icon_size = 0x7f07010c
com.android.rockchip.mediacodecnew:dimen/m3_comp_elevated_card_container_elevation = 0x7f07010b
com.android.rockchip.mediacodecnew:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070244
com.android.rockchip.mediacodecnew:attr/maxActionInlineWidth = 0x7f040306
com.android.rockchip.mediacodecnew:dimen/m3_comp_elevated_button_container_elevation = 0x7f070109
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_navigation_top_padding = 0x7f070286
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1203a9
com.android.rockchip.mediacodecnew:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f070107
com.android.rockchip.mediacodecnew:drawable/abc_ic_voice_search_api_material = 0x7f08004a
com.android.rockchip.mediacodecnew:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f070103
com.android.rockchip.mediacodecnew:dimen/m3_comp_bottom_app_bar_container_height = 0x7f070102
com.android.rockchip.mediacodecnew:style/Widget.Material3.BottomSheet = 0x7f120356
com.android.rockchip.mediacodecnew:dimen/m3_comp_badge_large_size = 0x7f0700ff
com.android.rockchip.mediacodecnew:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f0700fe
com.android.rockchip.mediacodecnew:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f0700fc
com.android.rockchip.mediacodecnew:attr/tabStyle = 0x7f04042a
com.android.rockchip.mediacodecnew:dimen/m3_comp_assist_chip_container_height = 0x7f0700fa
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral50 = 0x7f060100
com.android.rockchip.mediacodecnew:dimen/m3_chip_icon_size = 0x7f0700f9
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f120215
com.android.rockchip.mediacodecnew:dimen/m3_chip_corner_size = 0x7f0700f4
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.SeekBar = 0x7f120331
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f08001b
com.android.rockchip.mediacodecnew:dimen/m3_carousel_small_item_size_min = 0x7f0700f2
com.android.rockchip.mediacodecnew:dimen/m3_btn_icon_btn_padding_left = 0x7f0700d1
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f070287
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary40 = 0x7f060139
com.android.rockchip.mediacodecnew:dimen/m3_carousel_small_item_size_max = 0x7f0700f1
com.android.rockchip.mediacodecnew:dimen/m3_carousel_small_item_default_corner_size = 0x7f0700f0
com.android.rockchip.mediacodecnew:attr/backgroundTint = 0x7f040053
com.android.rockchip.mediacodecnew:dimen/m3_carousel_gone_size = 0x7f0700ef
com.android.rockchip.mediacodecnew:dimen/m3_carousel_extra_small_item_size = 0x7f0700ee
com.android.rockchip.mediacodecnew:id/search_badge = 0x7f0901d1
com.android.rockchip.mediacodecnew:attr/quantizeMotionPhase = 0x7f040390
com.android.rockchip.mediacodecnew:dimen/m3_carousel_debug_keyline_width = 0x7f0700ed
com.android.rockchip.mediacodecnew:dimen/m3_card_stroke_width = 0x7f0700ec
com.android.rockchip.mediacodecnew:dimen/m3_card_hovered_z = 0x7f0700eb
com.android.rockchip.mediacodecnew:dimen/m3_card_elevated_hovered_z = 0x7f0700e9
com.android.rockchip.mediacodecnew:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f070134
com.android.rockchip.mediacodecnew:dimen/m3_card_elevated_dragged_z = 0x7f0700e7
com.android.rockchip.mediacodecnew:styleable/AppCompatTextHelper = 0x7f130010
com.android.rockchip.mediacodecnew:dimen/m3_card_disabled_z = 0x7f0700e4
com.android.rockchip.mediacodecnew:dimen/m3_btn_translation_z_base = 0x7f0700e2
com.android.rockchip.mediacodecnew:style/TpVideoSpeedDialog = 0x7f1202ea
com.android.rockchip.mediacodecnew:drawable/tooltip_frame_dark = 0x7f0800e7
com.android.rockchip.mediacodecnew:dimen/m3_btn_text_btn_padding_right = 0x7f0700e1
com.android.rockchip.mediacodecnew:dimen/mtrl_tooltip_minHeight = 0x7f070300
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary30 = 0x7f0600d2
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f0701fd
com.android.rockchip.mediacodecnew:drawable/$m3_avd_hide_password__2 = 0x7f080009
com.android.rockchip.mediacodecnew:attr/marginRightSystemWindowInsets = 0x7f0402d4
com.android.rockchip.mediacodecnew:dimen/m3_btn_text_btn_padding_left = 0x7f0700e0
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f120088
com.android.rockchip.mediacodecnew:attr/selectionRequired = 0x7f0403b5
com.android.rockchip.mediacodecnew:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0700df
com.android.rockchip.mediacodecnew:attr/errorShown = 0x7f0401b7
com.android.rockchip.mediacodecnew:dimen/m3_btn_padding_right = 0x7f0700db
com.android.rockchip.mediacodecnew:attr/showDividers = 0x7f0403c8
com.android.rockchip.mediacodecnew:dimen/design_tab_max_width = 0x7f070089
com.android.rockchip.mediacodecnew:dimen/m3_btn_padding_left = 0x7f0700da
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f120366
com.android.rockchip.mediacodecnew:dimen/m3_card_elevated_elevation = 0x7f0700e8
com.android.rockchip.mediacodecnew:dimen/m3_btn_padding_bottom = 0x7f0700d9
com.android.rockchip.mediacodecnew:dimen/m3_btn_max_width = 0x7f0700d8
com.android.rockchip.mediacodecnew:dimen/m3_btn_elevation = 0x7f0700d0
com.android.rockchip.mediacodecnew:dimen/m3_btn_elevated_btn_elevation = 0x7f0700cf
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_text_btn_padding_left = 0x7f070266
com.android.rockchip.mediacodecnew:dimen/m3_btn_dialog_btn_spacing = 0x7f0700cc
com.android.rockchip.mediacodecnew:style/Widget.Material3.Toolbar = 0x7f1203e7
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Headline6 = 0x7f120204
com.android.rockchip.mediacodecnew:dimen/m3_bottomappbar_height = 0x7f0700c9
com.android.rockchip.mediacodecnew:layout/m3_alert_dialog_title = 0x7f0c003b
com.android.rockchip.mediacodecnew:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700c7
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f120426
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f120379
com.android.rockchip.mediacodecnew:dimen/m3_searchbar_padding_start = 0x7f0701d7
com.android.rockchip.mediacodecnew:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0700c2
com.android.rockchip.mediacodecnew:id/seekbar_ct_red = 0x7f0901e3
com.android.rockchip.mediacodecnew:id/fitStart = 0x7f0900ec
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0702d6
com.android.rockchip.mediacodecnew:dimen/m3_bottom_nav_item_padding_top = 0x7f0700c0
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_icon_padding = 0x7f07025a
com.android.rockchip.mediacodecnew:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700be
com.android.rockchip.mediacodecnew:color/design_default_color_on_secondary = 0x7f060042
com.android.rockchip.mediacodecnew:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700bd
com.android.rockchip.mediacodecnew:dimen/m3_badge_with_text_vertical_offset = 0x7f0700ba
com.android.rockchip.mediacodecnew:dimen/m3_badge_with_text_size = 0x7f0700b9
com.android.rockchip.mediacodecnew:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0700b2
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0d0102
com.android.rockchip.mediacodecnew:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f0700fd
com.android.rockchip.mediacodecnew:styleable/GradientColor = 0x7f13003b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600a6
com.android.rockchip.mediacodecnew:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0700b1
com.android.rockchip.mediacodecnew:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0700af
com.android.rockchip.mediacodecnew:color/background_material_light = 0x7f060020
com.android.rockchip.mediacodecnew:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0700ae
com.android.rockchip.mediacodecnew:dimen/material_clock_display_width = 0x7f07021d
com.android.rockchip.mediacodecnew:dimen/m3_appbar_size_compact = 0x7f0700a9
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_landscape_header_width = 0x7f070280
com.android.rockchip.mediacodecnew:style/Widget.Material3.TabLayout.OnSurface = 0x7f1203d9
com.android.rockchip.mediacodecnew:dimen/m3_appbar_scrim_height_trigger = 0x7f0700a6
com.android.rockchip.mediacodecnew:attr/windowFixedHeightMajor = 0x7f0404d7
com.android.rockchip.mediacodecnew:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700a5
com.android.rockchip.mediacodecnew:dimen/m3_sys_elevation_level3 = 0x7f0701ec
com.android.rockchip.mediacodecnew:anim/m3_side_sheet_exit_to_right = 0x7f010028
com.android.rockchip.mediacodecnew:dimen/m3_alert_dialog_action_top_padding = 0x7f07009e
com.android.rockchip.mediacodecnew:dimen/m3_alert_dialog_action_bottom_padding = 0x7f07009d
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f070127
com.android.rockchip.mediacodecnew:attr/tabPadding = 0x7f040421
com.android.rockchip.mediacodecnew:dimen/hint_pressed_alpha_material_light = 0x7f070099
com.android.rockchip.mediacodecnew:dimen/hint_pressed_alpha_material_dark = 0x7f070098
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1201bf
com.android.rockchip.mediacodecnew:dimen/hint_alpha_material_light = 0x7f070097
com.android.rockchip.mediacodecnew:dimen/hint_alpha_material_dark = 0x7f070096
com.android.rockchip.mediacodecnew:dimen/highlight_alpha_material_light = 0x7f070095
com.android.rockchip.mediacodecnew:dimen/highlight_alpha_material_colored = 0x7f070093
com.android.rockchip.mediacodecnew:dimen/fastscroll_margin = 0x7f070091
com.android.rockchip.mediacodecnew:dimen/fastscroll_default_thickness = 0x7f070090
com.android.rockchip.mediacodecnew:attr/dividerInsetStart = 0x7f040182
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_text_input_padding_top = 0x7f07028c
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f120089
com.android.rockchip.mediacodecnew:dimen/disabled_alpha_material_light = 0x7f07008f
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f120034
com.android.rockchip.mediacodecnew:dimen/disabled_alpha_material_dark = 0x7f07008e
com.android.rockchip.mediacodecnew:dimen/design_textinput_caption_translate_y = 0x7f07008d
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f07020b
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Chip.Assist = 0x7f120292
com.android.rockchip.mediacodecnew:dimen/design_tab_text_size = 0x7f07008b
com.android.rockchip.mediacodecnew:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f080026
com.android.rockchip.mediacodecnew:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08002e
com.android.rockchip.mediacodecnew:dimen/design_snackbar_max_width = 0x7f070083
com.android.rockchip.mediacodecnew:dimen/m3_comp_suggestion_chip_container_height = 0x7f070184
com.android.rockchip.mediacodecnew:attr/itemSpacing = 0x7f04025c
com.android.rockchip.mediacodecnew:dimen/design_snackbar_elevation = 0x7f070081
com.android.rockchip.mediacodecnew:dimen/design_navigation_max_width = 0x7f07007b
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0d011e
com.android.rockchip.mediacodecnew:dimen/design_navigation_item_horizontal_padding = 0x7f070078
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_dialog_btn_min_width = 0x7f070253
com.android.rockchip.mediacodecnew:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700bc
com.android.rockchip.mediacodecnew:dimen/design_navigation_elevation = 0x7f070075
com.android.rockchip.mediacodecnew:dimen/design_fab_size_normal = 0x7f070072
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0702d9
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Button = 0x7f1200ce
com.android.rockchip.mediacodecnew:id/textTop = 0x7f090248
com.android.rockchip.mediacodecnew:attr/navigationRailStyle = 0x7f040353
com.android.rockchip.mediacodecnew:dimen/design_fab_image_size = 0x7f070070
com.android.rockchip.mediacodecnew:dimen/design_bottom_sheet_peek_height_min = 0x7f07006d
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_text_size = 0x7f07006a
com.android.rockchip.mediacodecnew:string/abc_capital_on = 0x7f110007
com.android.rockchip.mediacodecnew:macro/m3_comp_circular_progress_indicator_active_indicator_color = 0x7f0d000d
com.android.rockchip.mediacodecnew:color/m3_tabs_icon_color = 0x7f0601e2
com.android.rockchip.mediacodecnew:attr/motionDurationExtraLong4 = 0x7f040324
com.android.rockchip.mediacodecnew:dimen/mtrl_shape_corner_size_small_component = 0x7f0702dd
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_shadow_height = 0x7f070069
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_label_padding = 0x7f070067
com.android.rockchip.mediacodecnew:color/m3_sys_color_secondary_fixed = 0x7f0601de
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_item_max_width = 0x7f070065
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_active_item_max_width = 0x7f07005f
com.android.rockchip.mediacodecnew:dimen/design_appbar_elevation = 0x7f07005e
com.android.rockchip.mediacodecnew:dimen/def_drawer_elevation = 0x7f07005d
com.android.rockchip.mediacodecnew:dimen/compat_button_padding_vertical_material = 0x7f070059
com.android.rockchip.mediacodecnew:dimen/compat_button_inset_horizontal_material = 0x7f070056
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1203a1
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f120106
com.android.rockchip.mediacodecnew:attr/flow_lastHorizontalBias = 0x7f0401f3
com.android.rockchip.mediacodecnew:dimen/clock_face_margin_start = 0x7f070055
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1203f3
com.android.rockchip.mediacodecnew:dimen/cardview_compat_inset_shadow = 0x7f070052
com.android.rockchip.mediacodecnew:dimen/abc_text_size_subhead_material = 0x7f07004d
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f120093
com.android.rockchip.mediacodecnew:attr/shortcutMatchRequired = 0x7f0403c3
com.android.rockchip.mediacodecnew:color/m3_sys_color_primary_fixed = 0x7f0601dc
com.android.rockchip.mediacodecnew:dimen/abc_text_size_small_material = 0x7f07004c
com.android.rockchip.mediacodecnew:attr/selectableItemBackground = 0x7f0403b3
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f07018c
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f120298
com.android.rockchip.mediacodecnew:id/always = 0x7f09004c
com.android.rockchip.mediacodecnew:dimen/abc_text_size_medium_material = 0x7f070049
com.android.rockchip.mediacodecnew:dimen/m3_searchbar_margin_vertical = 0x7f0701d5
com.android.rockchip.mediacodecnew:dimen/abc_text_size_large_material = 0x7f070048
com.android.rockchip.mediacodecnew:id/radio = 0x7f0901ab
com.android.rockchip.mediacodecnew:dimen/abc_text_size_headline_material = 0x7f070047
com.android.rockchip.mediacodecnew:dimen/abc_text_size_display_2_material = 0x7f070044
com.android.rockchip.mediacodecnew:dimen/abc_text_size_button_material = 0x7f070041
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_button_outline_width = 0x7f070151
com.android.rockchip.mediacodecnew:dimen/abc_text_size_body_2_material = 0x7f070040
com.android.rockchip.mediacodecnew:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1200c2
com.android.rockchip.mediacodecnew:dimen/abc_text_size_body_1_material = 0x7f07003f
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f12025a
com.android.rockchip.mediacodecnew:dimen/abc_switch_padding = 0x7f07003e
com.android.rockchip.mediacodecnew:dimen/abc_star_small = 0x7f07003d
com.android.rockchip.mediacodecnew:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.OutlinedButton = 0x7f120361
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0d0012
com.android.rockchip.mediacodecnew:id/indeterminate = 0x7f090110
com.android.rockchip.mediacodecnew:id/hideable = 0x7f090100
com.android.rockchip.mediacodecnew:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.android.rockchip.mediacodecnew:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.android.rockchip.mediacodecnew:attr/customIntegerValue = 0x7f040169
com.android.rockchip.mediacodecnew:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_button_path_name = 0x7f11005d
com.android.rockchip.mediacodecnew:id/selected = 0x7f0901f7
com.android.rockchip.mediacodecnew:dimen/abc_search_view_preferred_height = 0x7f070036
com.android.rockchip.mediacodecnew:dimen/abc_panel_menu_list_width = 0x7f070034
com.android.rockchip.mediacodecnew:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_extra_long1 = 0x7f0a000f
com.android.rockchip.mediacodecnew:id/west = 0x7f090295
com.android.rockchip.mediacodecnew:drawable/design_ic_visibility_off = 0x7f080084
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral24 = 0x7f0600fc
com.android.rockchip.mediacodecnew:dimen/abc_list_item_height_material = 0x7f070031
com.android.rockchip.mediacodecnew:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.android.rockchip.mediacodecnew:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.android.rockchip.mediacodecnew:layout/design_navigation_item_subheader = 0x7f0c002a
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_elevation = 0x7f070062
com.android.rockchip.mediacodecnew:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.android.rockchip.mediacodecnew:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.android.rockchip.mediacodecnew:dimen/abc_dialog_padding_material = 0x7f070024
com.android.rockchip.mediacodecnew:attr/flow_padding = 0x7f0401f8
com.android.rockchip.mediacodecnew:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_error = 0x7f0601bb
com.android.rockchip.mediacodecnew:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.android.rockchip.mediacodecnew:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.android.rockchip.mediacodecnew:drawable/m3_popupmenu_background_overlay = 0x7f0800a3
com.android.rockchip.mediacodecnew:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.android.rockchip.mediacodecnew:id/sin = 0x7f090203
com.android.rockchip.mediacodecnew:dimen/abc_control_padding_material = 0x7f07001a
com.android.rockchip.mediacodecnew:dimen/abc_control_inset_material = 0x7f070019
com.android.rockchip.mediacodecnew:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.android.rockchip.mediacodecnew:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.android.rockchip.mediacodecnew:id/image = 0x7f09010c
com.android.rockchip.mediacodecnew:dimen/abc_button_inset_vertical_material = 0x7f070013
com.android.rockchip.mediacodecnew:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.android.rockchip.mediacodecnew:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.android.rockchip.mediacodecnew:dimen/abc_action_button_min_width_material = 0x7f07000e
com.android.rockchip.mediacodecnew:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0401e3
com.android.rockchip.mediacodecnew:attr/checkedIconMargin = 0x7f0400ba
com.android.rockchip.mediacodecnew:attr/materialButtonOutlinedStyle = 0x7f0402dc
com.android.rockchip.mediacodecnew:dimen/abc_action_button_min_height_material = 0x7f07000d
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.android.rockchip.mediacodecnew:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f070249
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1200e9
com.android.rockchip.mediacodecnew:dimen/abc_text_size_display_1_material = 0x7f070043
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.android.rockchip.mediacodecnew:style/Widget.Material3.CompoundButton.CheckBox = 0x7f120382
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0d0078
com.android.rockchip.mediacodecnew:attr/alertDialogButtonGroupStyle = 0x7f04002a
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1201bc
com.android.rockchip.mediacodecnew:attr/snackbarTextViewStyle = 0x7f0403db
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_default_height_material = 0x7f070002
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.android.rockchip.mediacodecnew:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f07024a
com.android.rockchip.mediacodecnew:attr/minSeparation = 0x7f040317
com.android.rockchip.mediacodecnew:color/tp_video_text_normal = 0x7f0602f5
com.android.rockchip.mediacodecnew:drawable/m3_selection_control_ripple = 0x7f0800a5
com.android.rockchip.mediacodecnew:color/tp_video_progress_thumb_stroke = 0x7f0602f3
com.android.rockchip.mediacodecnew:color/m3_dynamic_primary_text_disable_only = 0x7f060083
com.android.rockchip.mediacodecnew:color/tp_video_progress_thumb = 0x7f0602f2
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Snackbar.Message = 0x7f1201d2
com.android.rockchip.mediacodecnew:dimen/material_emphasis_medium = 0x7f07022e
com.android.rockchip.mediacodecnew:color/tp_video_progress_secondary = 0x7f0602f1
com.android.rockchip.mediacodecnew:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0702ea
com.android.rockchip.mediacodecnew:drawable/ic_m3_chip_close = 0x7f080092
com.android.rockchip.mediacodecnew:dimen/m3_chip_hovered_translation_z = 0x7f0700f8
com.android.rockchip.mediacodecnew:color/tp_video_progress_bg = 0x7f0602ef
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_margin = 0x7f0702ca
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0702f4
com.android.rockchip.mediacodecnew:color/tp_video_button_stroke_normal = 0x7f0602ea
com.android.rockchip.mediacodecnew:color/design_bottom_navigation_shadow_color = 0x7f06002e
com.android.rockchip.mediacodecnew:attr/alertDialogTheme = 0x7f04002d
com.android.rockchip.mediacodecnew:color/tp_video_button_bg_pressed = 0x7f0602e8
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant90 = 0x7f060117
com.android.rockchip.mediacodecnew:color/tooltip_background_light = 0x7f0602e5
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f1203eb
com.android.rockchip.mediacodecnew:attr/drawerArrowStyle = 0x7f040194
com.android.rockchip.mediacodecnew:color/material_dynamic_primary30 = 0x7f060220
com.android.rockchip.mediacodecnew:color/tooltip_background_dark = 0x7f0602e4
com.android.rockchip.mediacodecnew:attr/layout_keyline = 0x7f0402b1
com.android.rockchip.mediacodecnew:color/switch_thumb_normal_material_light = 0x7f0602e3
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600b5
com.android.rockchip.mediacodecnew:attr/layout_scrollFlags = 0x7f0402b5
com.android.rockchip.mediacodecnew:dimen/abc_floating_window_z = 0x7f07002f
com.android.rockchip.mediacodecnew:color/switch_thumb_material_dark = 0x7f0602e0
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f12003a
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_background = 0x7f06018e
com.android.rockchip.mediacodecnew:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0800af
com.android.rockchip.mediacodecnew:id/unchecked = 0x7f090287
com.android.rockchip.mediacodecnew:color/secondary_text_disabled_material_dark = 0x7f0602dc
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.TextButton.Icon = 0x7f120367
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_icon_size = 0x7f07011b
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1201d5
com.android.rockchip.mediacodecnew:style/Animation.AppCompat.DropDownUp = 0x7f120003
com.android.rockchip.mediacodecnew:color/secondary_text_default_material_light = 0x7f0602db
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1203af
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_secondary_icon_color = 0x7f0d003d
com.android.rockchip.mediacodecnew:color/ripple_material_dark = 0x7f0602d8
com.android.rockchip.mediacodecnew:layout/abc_list_menu_item_layout = 0x7f0c0010
com.android.rockchip.mediacodecnew:attr/endIconContentDescription = 0x7f0401a6
com.android.rockchip.mediacodecnew:color/primary_text_disabled_material_light = 0x7f0602d7
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_inverse_primary = 0x7f06014e
com.android.rockchip.mediacodecnew:dimen/notification_media_narrow_margin = 0x7f07030b
com.android.rockchip.mediacodecnew:color/primary_text_default_material_light = 0x7f0602d5
com.android.rockchip.mediacodecnew:id/btn_fast_forward = 0x7f090070
com.android.rockchip.mediacodecnew:color/primary_text_default_material_dark = 0x7f0602d4
com.android.rockchip.mediacodecnew:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f07022f
com.android.rockchip.mediacodecnew:animator/mtrl_card_state_list_anim = 0x7f020017
com.android.rockchip.mediacodecnew:color/primary_dark_material_light = 0x7f0602d1
com.android.rockchip.mediacodecnew:color/primary_dark_material_dark = 0x7f0602d0
com.android.rockchip.mediacodecnew:string/abc_menu_meta_shortcut_label = 0x7f11000d
com.android.rockchip.mediacodecnew:attr/motionEasingAccelerated = 0x7f040331
com.android.rockchip.mediacodecnew:color/design_default_color_error = 0x7f06003e
com.android.rockchip.mediacodecnew:color/notification_action_color_filter = 0x7f0602ce
com.android.rockchip.mediacodecnew:string/mtrl_timepicker_cancel = 0x7f110092
com.android.rockchip.mediacodecnew:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0602cd
com.android.rockchip.mediacodecnew:attr/checkMarkTint = 0x7f0400b2
com.android.rockchip.mediacodecnew:color/mtrl_textinput_focused_box_stroke_color = 0x7f0602cc
com.android.rockchip.mediacodecnew:attr/autoAdjustToWithinGrandparentBounds = 0x7f04003e
com.android.rockchip.mediacodecnew:attr/drawerLayoutStyle = 0x7f040196
com.android.rockchip.mediacodecnew:color/mtrl_textinput_filled_box_default_background_color = 0x7f0602cb
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant20 = 0x7f060212
com.android.rockchip.mediacodecnew:attr/tabPaddingEnd = 0x7f040423
com.android.rockchip.mediacodecnew:color/mtrl_textinput_disabled_color = 0x7f0602ca
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600a4
com.android.rockchip.mediacodecnew:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0700b0
com.android.rockchip.mediacodecnew:id/seekbar_flip = 0x7f0901e9
com.android.rockchip.mediacodecnew:attr/imagePanY = 0x7f040235
com.android.rockchip.mediacodecnew:attr/expandedTitleMargin = 0x7f0401be
com.android.rockchip.mediacodecnew:color/mtrl_textinput_default_box_stroke_color = 0x7f0602c9
com.android.rockchip.mediacodecnew:color/mtrl_text_btn_text_color_selector = 0x7f0602c8
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f0701fc
com.android.rockchip.mediacodecnew:id/navigation_bar_item_active_indicator_view = 0x7f09016d
com.android.rockchip.mediacodecnew:color/mtrl_tabs_legacy_text_color_selector = 0x7f0602c6
com.android.rockchip.mediacodecnew:styleable/TabItem = 0x7f130089
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1202b8
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.NoActionBar = 0x7f120271
com.android.rockchip.mediacodecnew:id/position = 0x7f0901a3
com.android.rockchip.mediacodecnew:attr/singleLine = 0x7f0403d5
com.android.rockchip.mediacodecnew:color/mtrl_tabs_icon_color_selector = 0x7f0602c4
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar = 0x7f12030c
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_headline_type = 0x7f0d0152
com.android.rockchip.mediacodecnew:attr/tabSelectedTextColor = 0x7f040429
com.android.rockchip.mediacodecnew:color/mtrl_tabs_colored_ripple_color = 0x7f0602c3
com.android.rockchip.mediacodecnew:color/mtrl_switch_track_tint = 0x7f0602c2
com.android.rockchip.mediacodecnew:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.android.rockchip.mediacodecnew:color/mtrl_switch_thumb_icon_tint = 0x7f0602bf
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Button = 0x7f1202fd
com.android.rockchip.mediacodecnew:color/mtrl_scrim_color = 0x7f0602be
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_active_text_size = 0x7f070061
com.android.rockchip.mediacodecnew:color/mtrl_popupmenu_overlay_color = 0x7f0602bd
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant40 = 0x7f060214
com.android.rockchip.mediacodecnew:color/mtrl_outlined_icon_tint = 0x7f0602bb
com.android.rockchip.mediacodecnew:integer/abc_config_activityShortDur = 0x7f0a0001
com.android.rockchip.mediacodecnew:color/mtrl_navigation_item_text_color = 0x7f0602b8
com.android.rockchip.mediacodecnew:color/mtrl_navigation_bar_ripple_color = 0x7f0602b5
com.android.rockchip.mediacodecnew:color/mtrl_choice_chip_ripple_color = 0x7f0602a8
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0d00b8
com.android.rockchip.mediacodecnew:id/action_bar_spinner = 0x7f090037
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Title = 0x7f12002f
com.android.rockchip.mediacodecnew:color/mtrl_chip_text_color = 0x7f0602a6
com.android.rockchip.mediacodecnew:attr/cardMaxElevation = 0x7f0400a1
com.android.rockchip.mediacodecnew:color/mtrl_calendar_selected_range = 0x7f0602a0
com.android.rockchip.mediacodecnew:attr/backgroundInsetStart = 0x7f04004e
com.android.rockchip.mediacodecnew:color/mtrl_calendar_item_stroke_color = 0x7f06029f
com.android.rockchip.mediacodecnew:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f07017f
com.android.rockchip.mediacodecnew:color/mtrl_btn_transparent_bg_color = 0x7f06029e
com.android.rockchip.mediacodecnew:styleable/RadialViewGroup = 0x7f130074
com.android.rockchip.mediacodecnew:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.android.rockchip.mediacodecnew:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f08007b
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat.Dark = 0x7f120276
com.android.rockchip.mediacodecnew:color/mtrl_btn_stroke_color_selector = 0x7f060299
com.android.rockchip.mediacodecnew:style/Platform.MaterialComponents.Light.Dialog = 0x7f12013d
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0d00a6
com.android.rockchip.mediacodecnew:color/mtrl_btn_ripple_color = 0x7f060298
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f12002c
com.android.rockchip.mediacodecnew:dimen/m3_side_sheet_margin_detached = 0x7f0701dd
com.android.rockchip.mediacodecnew:color/mtrl_btn_bg_color_selector = 0x7f060297
com.android.rockchip.mediacodecnew:dimen/m3_btn_translation_z_hovered = 0x7f0700e3
com.android.rockchip.mediacodecnew:color/material_timepicker_clockface = 0x7f060295
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0d00c1
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0701f3
com.android.rockchip.mediacodecnew:color/material_timepicker_button_stroke = 0x7f060293
com.android.rockchip.mediacodecnew:color/material_slider_inactive_track_color = 0x7f060290
com.android.rockchip.mediacodecnew:style/Widget.Design.Snackbar = 0x7f120344
com.android.rockchip.mediacodecnew:color/material_slider_inactive_tick_marks_color = 0x7f06028f
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1202cc
com.android.rockchip.mediacodecnew:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0702ba
com.android.rockchip.mediacodecnew:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f12020b
com.android.rockchip.mediacodecnew:color/material_slider_halo_color = 0x7f06028e
com.android.rockchip.mediacodecnew:color/material_slider_active_track_color = 0x7f06028d
com.android.rockchip.mediacodecnew:raw/rgb_saturation_fragment = 0x7f100021
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_min_width = 0x7f0702a8
com.android.rockchip.mediacodecnew:color/material_slider_active_tick_marks_color = 0x7f06028c
com.android.rockchip.mediacodecnew:id/action_menu_divider = 0x7f09003e
com.android.rockchip.mediacodecnew:dimen/m3_btn_inset = 0x7f0700d7
com.android.rockchip.mediacodecnew:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f070177
com.android.rockchip.mediacodecnew:dimen/appcompat_dialog_background_inset = 0x7f070051
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f120031
com.android.rockchip.mediacodecnew:color/material_personalized_color_outline = 0x7f06026c
com.android.rockchip.mediacodecnew:color/material_personalized_primary_inverse_text_disable_only = 0x7f06028a
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0800b6
com.android.rockchip.mediacodecnew:attr/statusBarScrim = 0x7f0403fb
com.android.rockchip.mediacodecnew:dimen/m3_comp_search_bar_container_elevation = 0x7f07016d
com.android.rockchip.mediacodecnew:attr/indicatorDirectionCircular = 0x7f04023b
com.android.rockchip.mediacodecnew:color/material_personalized_hint_foreground = 0x7f060288
com.android.rockchip.mediacodecnew:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f060287
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0701f5
com.android.rockchip.mediacodecnew:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary40 = 0x7f06011f
com.android.rockchip.mediacodecnew:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f060286
com.android.rockchip.mediacodecnew:integer/show_password_duration = 0x7f0a0042
com.android.rockchip.mediacodecnew:color/material_personalized_color_text_hint_foreground_inverse = 0x7f060283
com.android.rockchip.mediacodecnew:dimen/m3_comp_search_bar_avatar_size = 0x7f07016c
com.android.rockchip.mediacodecnew:color/material_personalized_color_tertiary_container = 0x7f060282
com.android.rockchip.mediacodecnew:color/material_personalized_color_tertiary = 0x7f060281
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1202a5
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_button_path_unchecked = 0x7f11005e
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_inverse = 0x7f06027f
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f07028b
com.android.rockchip.mediacodecnew:styleable/Snackbar = 0x7f130080
com.android.rockchip.mediacodecnew:attr/contrast = 0x7f04014a
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_container_lowest = 0x7f06027d
com.android.rockchip.mediacodecnew:color/material_personalized_color_secondary_text_inverse = 0x7f060276
com.android.rockchip.mediacodecnew:color/material_personalized_color_secondary_text = 0x7f060275
com.android.rockchip.mediacodecnew:color/material_personalized_color_secondary_container = 0x7f060274
com.android.rockchip.mediacodecnew:color/material_personalized_color_secondary = 0x7f060273
com.android.rockchip.mediacodecnew:style/Widget.Material3.Badge.AdjustToBounds = 0x7f120350
com.android.rockchip.mediacodecnew:id/content = 0x7f0900a4
com.android.rockchip.mediacodecnew:color/material_personalized_color_primary_text = 0x7f060271
com.android.rockchip.mediacodecnew:dimen/tooltip_precise_anchor_threshold = 0x7f070317
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0d0019
com.android.rockchip.mediacodecnew:attr/constraint_referenced_tags = 0x7f040138
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_day_today_stroke = 0x7f070272
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_variant = 0x7f060280
com.android.rockchip.mediacodecnew:macro/m3_comp_search_view_docked_container_shape = 0x7f0d00f5
com.android.rockchip.mediacodecnew:dimen/material_clock_hand_center_dot_radius = 0x7f07021f
com.android.rockchip.mediacodecnew:color/material_personalized_color_outline_variant = 0x7f06026d
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_tertiary_container = 0x7f06026b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary60 = 0x7f06012e
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_tertiary = 0x7f06026a
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_surface_variant = 0x7f060269
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_surface = 0x7f060267
com.android.rockchip.mediacodecnew:drawable/abc_ratingbar_small_material = 0x7f08005d
com.android.rockchip.mediacodecnew:id/navigation_bar_item_icon_container = 0x7f09016e
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_secondary_container = 0x7f060266
com.android.rockchip.mediacodecnew:layout/media_item = 0x7f0c004e
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_secondary = 0x7f060265
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_primary_container = 0x7f060264
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120073
com.android.rockchip.mediacodecnew:macro/m3_comp_assist_chip_container_shape = 0x7f0d0000
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary90 = 0x7f060233
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0601af
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f06018b
com.android.rockchip.mediacodecnew:dimen/design_fab_border_width = 0x7f07006e
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_primary = 0x7f060263
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_error_container = 0x7f060262
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_error = 0x7f060261
com.android.rockchip.mediacodecnew:id/tag_accessibility_pane_title = 0x7f090237
com.android.rockchip.mediacodecnew:color/material_on_surface_emphasis_medium = 0x7f060256
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f070145
com.android.rockchip.mediacodecnew:color/material_on_surface_emphasis_high_type = 0x7f060255
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f12015a
com.android.rockchip.mediacodecnew:attr/backgroundInsetEnd = 0x7f04004d
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f070193
com.android.rockchip.mediacodecnew:color/material_on_background_emphasis_high_type = 0x7f06024f
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070205
com.android.rockchip.mediacodecnew:id/onInterceptTouchReturnSwipe = 0x7f090183
com.android.rockchip.mediacodecnew:color/material_harmonized_color_on_error_container = 0x7f06024d
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0d0164
com.android.rockchip.mediacodecnew:color/material_harmonized_color_error_container = 0x7f06024b
com.android.rockchip.mediacodecnew:id/fragment_container_view_tag = 0x7f0900f3
com.android.rockchip.mediacodecnew:id/centerInside = 0x7f09008f
com.android.rockchip.mediacodecnew:attr/SharedValueId = 0x7f040002
com.android.rockchip.mediacodecnew:color/material_grey_850 = 0x7f060248
com.android.rockchip.mediacodecnew:dimen/design_navigation_icon_size = 0x7f070077
com.android.rockchip.mediacodecnew:color/material_grey_800 = 0x7f060247
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary99 = 0x7f060242
com.android.rockchip.mediacodecnew:id/fitXY = 0x7f0900ee
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary90 = 0x7f060240
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary80 = 0x7f06023f
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary50 = 0x7f06023c
com.android.rockchip.mediacodecnew:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f07010a
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f06016f
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary40 = 0x7f06023b
com.android.rockchip.mediacodecnew:attr/motionEffect_alpha = 0x7f04033d
com.android.rockchip.mediacodecnew:color/material_on_primary_emphasis_high_type = 0x7f060252
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary30 = 0x7f06023a
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_15 = 0x7f090017
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary80 = 0x7f060232
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary40 = 0x7f06022e
com.android.rockchip.mediacodecnew:attr/bottomNavigationStyle = 0x7f04007d
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary20 = 0x7f06022c
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary100 = 0x7f06022b
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1203ba
com.android.rockchip.mediacodecnew:raw/earlybird_fragment = 0x7f10000e
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary0 = 0x7f060229
com.android.rockchip.mediacodecnew:raw/grey_scale_fragment = 0x7f100016
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_short4 = 0x7f0a001e
com.android.rockchip.mediacodecnew:color/design_fab_shadow_end_color = 0x7f06004b
com.android.rockchip.mediacodecnew:color/tp_video_button_bg_disabled = 0x7f0602e6
com.android.rockchip.mediacodecnew:color/material_dynamic_primary95 = 0x7f060227
com.android.rockchip.mediacodecnew:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f070188
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0600e0
com.android.rockchip.mediacodecnew:color/material_dynamic_primary90 = 0x7f060226
com.android.rockchip.mediacodecnew:color/material_dynamic_primary60 = 0x7f060223
com.android.rockchip.mediacodecnew:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f08007d
com.android.rockchip.mediacodecnew:color/material_dynamic_primary50 = 0x7f060222
com.android.rockchip.mediacodecnew:color/material_dynamic_primary100 = 0x7f06021e
com.android.rockchip.mediacodecnew:color/material_dynamic_primary10 = 0x7f06021d
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.LargeComponent = 0x7f120177
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant90 = 0x7f060219
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f12035f
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f120268
com.android.rockchip.mediacodecnew:string/abc_activitychooserview_choose_application = 0x7f110005
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant60 = 0x7f060216
com.android.rockchip.mediacodecnew:string/mtrl_switch_track_path = 0x7f110091
com.android.rockchip.mediacodecnew:attr/lineHeight = 0x7f0402bc
com.android.rockchip.mediacodecnew:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0701d1
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant100 = 0x7f060211
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.BottomNavigationView = 0x7f1203fb
com.android.rockchip.mediacodecnew:color/highlighted_text_material_dark = 0x7f06005c
com.android.rockchip.mediacodecnew:color/mtrl_tabs_icon_color_selector_colored = 0x7f0602c5
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600a7
com.android.rockchip.mediacodecnew:drawable/ic_step_frame_white_24 = 0x7f08009d
com.android.rockchip.mediacodecnew:color/m3_slider_thumb_color = 0x7f060147
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant10 = 0x7f060210
com.android.rockchip.mediacodecnew:drawable/abc_btn_check_material_anim = 0x7f08002d
com.android.rockchip.mediacodecnew:attr/checkedButton = 0x7f0400b5
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant0 = 0x7f06020f
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral99 = 0x7f06020e
com.android.rockchip.mediacodecnew:attr/perpendicularPath_percent = 0x7f04037d
com.android.rockchip.mediacodecnew:dimen/m3_searchview_height = 0x7f0701dc
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_short1 = 0x7f0a001b
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_extra_long4 = 0x7f0a0012
com.android.rockchip.mediacodecnew:id/graph = 0x7f0900fa
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral95 = 0x7f06020d
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral90 = 0x7f06020c
com.android.rockchip.mediacodecnew:id/noState = 0x7f09017a
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral70 = 0x7f06020a
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Dialog = 0x7f120068
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f070146
com.android.rockchip.mediacodecnew:color/m3_timepicker_button_ripple_color = 0x7f0601f1
com.android.rockchip.mediacodecnew:color/m3_slider_halo_color = 0x7f060145
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral60 = 0x7f060209
com.android.rockchip.mediacodecnew:id/seekbar_wb_red = 0x7f0901f4
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral40 = 0x7f060207
com.android.rockchip.mediacodecnew:dimen/m3_searchbar_outlined_stroke_width = 0x7f0701d6
com.android.rockchip.mediacodecnew:color/m3_elevated_chip_background_color = 0x7f060085
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral30 = 0x7f060206
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0d007c
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral100 = 0x7f060204
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral0 = 0x7f060202
com.android.rockchip.mediacodecnew:color/material_deep_teal_200 = 0x7f0601ff
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.Display = 0x7f12045d
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Headline4 = 0x7f120202
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1200d6
com.android.rockchip.mediacodecnew:color/material_cursor_color = 0x7f0601fe
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_outline = 0x7f060179
com.android.rockchip.mediacodecnew:drawable/ic_skip_previous_white_24 = 0x7f08009c
com.android.rockchip.mediacodecnew:id/closest = 0x7f09009e
com.android.rockchip.mediacodecnew:attr/actionBarPopupTheme = 0x7f040005
com.android.rockchip.mediacodecnew:color/material_blue_grey_900 = 0x7f0601fc
com.android.rockchip.mediacodecnew:id/exitUntilCollapsed = 0x7f0900e2
com.android.rockchip.mediacodecnew:color/m3_timepicker_secondary_text_button_text_color = 0x7f0601f8
com.android.rockchip.mediacodecnew:style/Theme.Material3.DayNight = 0x7f120231
com.android.rockchip.mediacodecnew:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f0601f7
com.android.rockchip.mediacodecnew:color/m3_timepicker_display_ripple_color = 0x7f0601f5
com.android.rockchip.mediacodecnew:id/btn_browse_media = 0x7f090066
com.android.rockchip.mediacodecnew:color/m3_timepicker_clock_text_color = 0x7f0601f3
com.android.rockchip.mediacodecnew:color/m3_textfield_stroke_color = 0x7f0601ef
com.android.rockchip.mediacodecnew:string/path_password_eye_mask_strike_through = 0x7f110096
com.android.rockchip.mediacodecnew:dimen/m3_alert_dialog_icon_margin = 0x7f0700a1
com.android.rockchip.mediacodecnew:string/password_toggle_content_description = 0x7f110094
com.android.rockchip.mediacodecnew:dimen/design_snackbar_padding_horizontal = 0x7f070085
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_primary = 0x7f060171
com.android.rockchip.mediacodecnew:color/m3_textfield_label_color = 0x7f0601ee
com.android.rockchip.mediacodecnew:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f12009e
com.android.rockchip.mediacodecnew:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f070179
com.android.rockchip.mediacodecnew:color/m3_textfield_input_text_color = 0x7f0601ed
com.android.rockchip.mediacodecnew:attr/materialAlertDialogBodyTextStyle = 0x7f0402d6
com.android.rockchip.mediacodecnew:drawable/notify_panel_notification_icon_bg = 0x7f0800e5
com.android.rockchip.mediacodecnew:color/m3_text_button_ripple_color_selector = 0x7f0601ea
com.android.rockchip.mediacodecnew:string/bottomsheet_action_expand = 0x7f110020
com.android.rockchip.mediacodecnew:id/tag_accessibility_heading = 0x7f090236
com.android.rockchip.mediacodecnew:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700c4
com.android.rockchip.mediacodecnew:color/m3_text_button_foreground_color_selector = 0x7f0601e9
com.android.rockchip.mediacodecnew:color/m3_tabs_text_color_secondary = 0x7f0601e7
com.android.rockchip.mediacodecnew:string/bottomsheet_drag_handle_clicked = 0x7f110022
com.android.rockchip.mediacodecnew:drawable/abc_dialog_material_background = 0x7f08003c
com.android.rockchip.mediacodecnew:color/m3_tabs_text_color = 0x7f0601e6
com.android.rockchip.mediacodecnew:color/m3_sys_color_tertiary_fixed_dim = 0x7f0601e1
com.android.rockchip.mediacodecnew:color/m3_sys_color_tertiary_fixed = 0x7f0601e0
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f120313
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents = 0x7f12012e
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_container_color = 0x7f0d009b
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0701f1
com.android.rockchip.mediacodecnew:style/Theme.Design.Light.NoActionBar = 0x7f120227
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_size = 0x7f0702d2
com.android.rockchip.mediacodecnew:style/Widget.Material3.SearchBar = 0x7f1203ca
com.android.rockchip.mediacodecnew:attr/barrierAllowsGoneWidgets = 0x7f040067
com.android.rockchip.mediacodecnew:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0601d9
com.android.rockchip.mediacodecnew:dimen/material_helper_text_font_1_3_padding_top = 0x7f070237
com.android.rockchip.mediacodecnew:dimen/m3_btn_stroke_size = 0x7f0700dd
com.android.rockchip.mediacodecnew:color/m3_sys_color_on_secondary_fixed = 0x7f0601d8
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary90 = 0x7f060131
com.android.rockchip.mediacodecnew:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.android.rockchip.mediacodecnew:color/m3_sys_color_on_primary_fixed = 0x7f0601d6
com.android.rockchip.mediacodecnew:raw/circle_fragment = 0x7f10000a
com.android.rockchip.mediacodecnew:attr/seekBarStyle = 0x7f0403b2
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_tertiary_container = 0x7f0601d5
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_17 = 0x7f090019
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_tertiary = 0x7f0601d4
com.android.rockchip.mediacodecnew:drawable/mtrl_dialog_background = 0x7f0800bf
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface_variant = 0x7f0601d3
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface_container_low = 0x7f0601d0
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f12044f
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface_container_highest = 0x7f0601cf
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f070289
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface_container_high = 0x7f0601ce
com.android.rockchip.mediacodecnew:id/mtrl_calendar_months = 0x7f09015a
com.android.rockchip.mediacodecnew:color/material_personalized_color_error = 0x7f06025e
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface_bright = 0x7f0601cc
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface = 0x7f0601cb
com.android.rockchip.mediacodecnew:attr/theme = 0x7f040473
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_secondary_container = 0x7f0601ca
com.android.rockchip.mediacodecnew:attr/expandedTitleMarginTop = 0x7f0401c2
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_primary_container = 0x7f0601c8
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f07020f
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_outline = 0x7f0601c5
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f120159
com.android.rockchip.mediacodecnew:attr/dynamicColorThemeOverlay = 0x7f04019b
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_tertiary = 0x7f0601c3
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_surface_variant = 0x7f0601c2
com.android.rockchip.mediacodecnew:dimen/m3_searchbar_elevation = 0x7f0701d2
com.android.rockchip.mediacodecnew:styleable/MenuItem = 0x7f130060
com.android.rockchip.mediacodecnew:color/material_dynamic_primary80 = 0x7f060225
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_secondary_container = 0x7f0601c0
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_error_container = 0x7f0601bc
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0d00d3
com.android.rockchip.mediacodecnew:attr/spanCount = 0x7f0403dc
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_background = 0x7f0601ba
com.android.rockchip.mediacodecnew:styleable/AppBarLayout_Layout = 0x7f13000c
com.android.rockchip.mediacodecnew:dimen/abc_dialog_min_width_major = 0x7f070022
com.android.rockchip.mediacodecnew:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f12022d
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary95 = 0x7f060241
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_label_square_side = 0x7f0702e1
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_inverse_surface = 0x7f0601b9
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_inverse_primary = 0x7f0601b8
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_inverse_on_surface = 0x7f0601b7
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_error = 0x7f0601b5
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_background = 0x7f0601b4
com.android.rockchip.mediacodecnew:dimen/abc_control_corner_material = 0x7f070018
com.android.rockchip.mediacodecnew:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f060285
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Dialog = 0x7f120258
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0601ad
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0601ac
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0601aa
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0601a9
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0601a7
com.android.rockchip.mediacodecnew:color/material_timepicker_button_background = 0x7f060292
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0601a5
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Tooltip = 0x7f1201b1
com.android.rockchip.mediacodecnew:id/tp_video_player_view = 0x7f09025c
com.android.rockchip.mediacodecnew:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0601a3
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1203df
com.android.rockchip.mediacodecnew:drawable/mtrl_ic_check_mark = 0x7f0800c4
com.android.rockchip.mediacodecnew:style/Widget.Material3.TabLayout = 0x7f1203d8
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0601a2
com.android.rockchip.mediacodecnew:attr/fabSize = 0x7f0401d5
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0601a1
com.android.rockchip.mediacodecnew:attr/progressBarPadding = 0x7f04038d
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0601a0
com.android.rockchip.mediacodecnew:styleable/ActionMode = 0x7f130004
com.android.rockchip.mediacodecnew:color/bright_foreground_disabled_material_light = 0x7f060023
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface_container = 0x7f06019f
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface_bright = 0x7f06019e
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface = 0x7f06019d
com.android.rockchip.mediacodecnew:dimen/m3_bottom_sheet_elevation = 0x7f0700c3
com.android.rockchip.mediacodecnew:dimen/m3_badge_vertical_offset = 0x7f0700b6
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_secondary_container = 0x7f06019c
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_primary = 0x7f060199
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_outline = 0x7f060197
com.android.rockchip.mediacodecnew:color/mtrl_indicator_text_color = 0x7f0602b1
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f060195
com.android.rockchip.mediacodecnew:color/material_personalized_color_primary_container = 0x7f06026f
com.android.rockchip.mediacodecnew:dimen/design_fab_size_mini = 0x7f070071
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f060194
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_surface = 0x7f060193
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f060192
com.android.rockchip.mediacodecnew:id/blocking = 0x7f09005d
com.android.rockchip.mediacodecnew:attr/menuGravity = 0x7f040313
com.android.rockchip.mediacodecnew:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0602b9
com.android.rockchip.mediacodecnew:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_secondary = 0x7f060191
com.android.rockchip.mediacodecnew:color/abc_secondary_text_material_light = 0x7f060012
com.android.rockchip.mediacodecnew:dimen/mtrl_bottomappbar_height = 0x7f070251
com.android.rockchip.mediacodecnew:dimen/abc_dialog_title_divider_material = 0x7f070026
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f06018d
com.android.rockchip.mediacodecnew:layout/design_navigation_menu_item = 0x7f0c002c
com.android.rockchip.mediacodecnew:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f070214
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f06018c
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f120295
com.android.rockchip.mediacodecnew:dimen/mtrl_alert_dialog_background_inset_end = 0x7f070240
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_secondary_container = 0x7f060156
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f060189
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f060185
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f060183
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1200e5
com.android.rockchip.mediacodecnew:dimen/design_snackbar_action_text_color_alpha = 0x7f07007f
com.android.rockchip.mediacodecnew:id/tv_ct_green_value = 0x7f09026c
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface_container = 0x7f060181
com.android.rockchip.mediacodecnew:drawable/abc_scrubber_track_mtrl_alpha = 0x7f080062
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f060180
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_secondary = 0x7f06017d
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f120060
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_primary = 0x7f06017b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary100 = 0x7f060136
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f06017a
com.android.rockchip.mediacodecnew:attr/shrinkMotionSpec = 0x7f0403cd
com.android.rockchip.mediacodecnew:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f080022
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f060177
com.android.rockchip.mediacodecnew:id/overshoot = 0x7f090194
com.android.rockchip.mediacodecnew:attr/colorOnSecondaryFixed = 0x7f04010a
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f060176
com.android.rockchip.mediacodecnew:layout/abc_action_bar_title_item = 0x7f0c0000
com.android.rockchip.mediacodecnew:dimen/m3_datepicker_elevation = 0x7f0701a7
com.android.rockchip.mediacodecnew:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f07017a
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_surface = 0x7f060175
com.android.rockchip.mediacodecnew:dimen/material_clock_display_padding = 0x7f07021c
com.android.rockchip.mediacodecnew:styleable/ActionMenuItemView = 0x7f130002
com.android.rockchip.mediacodecnew:attr/layout_goneMarginBaseline = 0x7f0402a9
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f060173
com.android.rockchip.mediacodecnew:layout/item_settings_menu = 0x7f0c0038
com.android.rockchip.mediacodecnew:attr/waveVariesBy = 0x7f0404d3
com.android.rockchip.mediacodecnew:dimen/abc_text_size_display_4_material = 0x7f070046
com.android.rockchip.mediacodecnew:attr/carousel_infinite = 0x7f0400a9
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f06016e
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f120441
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_tertiary_container = 0x7f06016b
com.android.rockchip.mediacodecnew:id/material_clock_period_pm_button = 0x7f09012f
com.android.rockchip.mediacodecnew:dimen/m3_btn_icon_only_min_width = 0x7f0700d6
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f120077
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_tertiary = 0x7f06016a
com.android.rockchip.mediacodecnew:anim/abc_popup_exit = 0x7f010004
com.android.rockchip.mediacodecnew:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f07029e
com.android.rockchip.mediacodecnew:color/material_timepicker_clock_text_color = 0x7f060294
com.android.rockchip.mediacodecnew:attr/behavior_expandedOffset = 0x7f04006d
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_19 = 0x7f09001b
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface_dim = 0x7f060168
com.android.rockchip.mediacodecnew:style/TextAppearance.Compat.Notification.Time = 0x7f1201c8
com.android.rockchip.mediacodecnew:integer/design_tab_indicator_anim_duration_ms = 0x7f0a0007
com.android.rockchip.mediacodecnew:id/action_context_bar = 0x7f09003b
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface_container_lowest = 0x7f060167
com.android.rockchip.mediacodecnew:id/tv_mode_switch = 0x7f09027e
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface_container_low = 0x7f060166
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface_container_high = 0x7f060164
com.android.rockchip.mediacodecnew:attr/defaultQueryHint = 0x7f040174
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface_container = 0x7f060163
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Inverse = 0x7f12019f
com.android.rockchip.mediacodecnew:string/path_password_eye_mask_visible = 0x7f110097
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_disabled_z = 0x7f070255
com.android.rockchip.mediacodecnew:styleable/BottomAppBar = 0x7f130015
com.android.rockchip.mediacodecnew:color/mtrl_filled_icon_tint = 0x7f0602af
com.android.rockchip.mediacodecnew:attr/textStartPadding = 0x7f04046e
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface = 0x7f060161
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant40 = 0x7f060112
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant99 = 0x7f06021b
com.android.rockchip.mediacodecnew:style/TextAppearance.Compat.Notification.Info = 0x7f1201c6
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_secondary = 0x7f06015f
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_hovered_z = 0x7f070258
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_primary_container = 0x7f06015e
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f0701ff
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_surface_variant = 0x7f060158
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_primary = 0x7f060153
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f06017e
com.android.rockchip.mediacodecnew:color/foreground_material_light = 0x7f06005b
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_error_container = 0x7f060152
com.android.rockchip.mediacodecnew:drawable/notification_bg_low_normal = 0x7f0800dd
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_background = 0x7f060150
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_inverse_surface = 0x7f06014f
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_inverse_on_surface = 0x7f06014d
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_year_corner = 0x7f07028f
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_error_container = 0x7f06014c
com.android.rockchip.mediacodecnew:attr/borderRound = 0x7f040077
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_tertiary = 0x7f060188
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0d0056
com.android.rockchip.mediacodecnew:color/material_personalized_color_background = 0x7f06025a
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_background = 0x7f06014a
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_container_color = 0x7f0d000e
com.android.rockchip.mediacodecnew:attr/limitBoundsTo = 0x7f0402bb
com.android.rockchip.mediacodecnew:color/m3_switch_thumb_tint = 0x7f060148
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1202ce
com.android.rockchip.mediacodecnew:color/m3_slider_inactive_track_color = 0x7f060146
com.android.rockchip.mediacodecnew:id/input_scene_value = 0x7f090112
com.android.rockchip.mediacodecnew:id/clockwise = 0x7f09009d
com.android.rockchip.mediacodecnew:color/m3_slider_active_track_color = 0x7f060144
com.android.rockchip.mediacodecnew:attr/boxBackgroundMode = 0x7f040082
com.android.rockchip.mediacodecnew:color/m3_simple_item_ripple_color = 0x7f060143
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f070113
com.android.rockchip.mediacodecnew:color/m3_ref_palette_white = 0x7f060141
com.android.rockchip.mediacodecnew:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
com.android.rockchip.mediacodecnew:attr/titleMarginBottom = 0x7f040492
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary99 = 0x7f060140
com.android.rockchip.mediacodecnew:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080081
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_outline_color = 0x7f0d00c5
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary95 = 0x7f06013f
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary90 = 0x7f06013e
com.android.rockchip.mediacodecnew:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0700fb
com.android.rockchip.mediacodecnew:attr/submitBackground = 0x7f040403
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary60 = 0x7f06013b
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_secondary_container_color = 0x7f0d003c
com.android.rockchip.mediacodecnew:id/tv_connection_status = 0x7f090269
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary50 = 0x7f06013a
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary20 = 0x7f060137
com.android.rockchip.mediacodecnew:drawable/abc_spinner_mtrl_am_alpha = 0x7f080066
com.android.rockchip.mediacodecnew:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1200aa
com.android.rockchip.mediacodecnew:dimen/m3_slider_inactive_track_height = 0x7f0701e3
com.android.rockchip.mediacodecnew:dimen/design_bottom_sheet_modal_elevation = 0x7f07006c
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ListView.Menu = 0x7f120326
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary10 = 0x7f060135
com.android.rockchip.mediacodecnew:attr/windowMinWidthMinor = 0x7f0404dc
com.android.rockchip.mediacodecnew:color/m3_ref_palette_tertiary0 = 0x7f060134
com.android.rockchip.mediacodecnew:string/appbar_scrolling_view_behavior = 0x7f11001d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary70 = 0x7f06012f
com.android.rockchip.mediacodecnew:color/mtrl_card_view_foreground = 0x7f0602a1
com.android.rockchip.mediacodecnew:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f090290
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary30 = 0x7f06012b
com.android.rockchip.mediacodecnew:drawable/design_ic_visibility = 0x7f080083
com.android.rockchip.mediacodecnew:attr/flow_horizontalAlign = 0x7f0401ef
com.android.rockchip.mediacodecnew:color/m3_tabs_ripple_color_secondary = 0x7f0601e5
com.android.rockchip.mediacodecnew:id/btn_cancel = 0x7f090068
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary20 = 0x7f06012a
com.android.rockchip.mediacodecnew:drawable/abc_edit_text_material = 0x7f08003d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary100 = 0x7f060129
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary10 = 0x7f060128
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary0 = 0x7f060127
com.android.rockchip.mediacodecnew:style/Platform.MaterialComponents.Dialog = 0x7f12013b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary70 = 0x7f060122
com.android.rockchip.mediacodecnew:attr/dividerColor = 0x7f04017f
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary20 = 0x7f06011d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary0 = 0x7f06011a
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant99 = 0x7f060119
com.android.rockchip.mediacodecnew:attr/springMass = 0x7f0403e3
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant95 = 0x7f060118
com.android.rockchip.mediacodecnew:color/bright_foreground_inverse_material_light = 0x7f060025
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0601b2
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant80 = 0x7f060116
com.android.rockchip.mediacodecnew:attr/maxHeight = 0x7f040309
com.android.rockchip.mediacodecnew:dimen/m3_comp_search_view_docked_header_container_height = 0x7f070172
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant50 = 0x7f060113
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant20 = 0x7f060110
com.android.rockchip.mediacodecnew:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f070175
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_item_min_width = 0x7f070066
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant10 = 0x7f06010e
com.android.rockchip.mediacodecnew:attr/moveWhenScrollAtTop = 0x7f04034d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant0 = 0x7f06010d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral98 = 0x7f06010b
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_3 = 0x7f090027
com.android.rockchip.mediacodecnew:attr/backgroundInsetBottom = 0x7f04004c
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral90 = 0x7f060106
com.android.rockchip.mediacodecnew:color/m3_tabs_ripple_color = 0x7f0601e4
com.android.rockchip.mediacodecnew:anim/m3_motion_fade_enter = 0x7f010023
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral87 = 0x7f060105
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1200f8
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral80 = 0x7f060104
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_elevation_material = 0x7f070005
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral4 = 0x7f0600fe
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1203ac
com.android.rockchip.mediacodecnew:id/reverseSawtooth = 0x7f0901bb
com.android.rockchip.mediacodecnew:drawable/mtrl_ic_cancel = 0x7f0800c3
com.android.rockchip.mediacodecnew:id/fill_horizontal = 0x7f0900e7
com.android.rockchip.mediacodecnew:attr/actionOverflowButtonStyle = 0x7f040022
com.android.rockchip.mediacodecnew:attr/switchMinWidth = 0x7f04040d
com.android.rockchip.mediacodecnew:attr/motionStagger = 0x7f040349
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1202b2
com.android.rockchip.mediacodecnew:string/character_counter_pattern = 0x7f110026
com.android.rockchip.mediacodecnew:id/seekbar_hz = 0x7f0901ec
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral17 = 0x7f0600f9
com.android.rockchip.mediacodecnew:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0701a4
com.android.rockchip.mediacodecnew:attr/titleTextEllipsize = 0x7f04049a
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral0 = 0x7f0600f5
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error95 = 0x7f0600f3
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error80 = 0x7f0600f1
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Dialog.MinWidth = 0x7f120218
com.android.rockchip.mediacodecnew:id/BOTTOM_END = 0x7f090001
com.android.rockchip.mediacodecnew:string/error_icon_content_description = 0x7f110029
com.android.rockchip.mediacodecnew:attr/minHideDelay = 0x7f040316
com.android.rockchip.mediacodecnew:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f070162
com.android.rockchip.mediacodecnew:color/m3_chip_text_color = 0x7f060072
com.android.rockchip.mediacodecnew:attr/layout_constraintCircleRadius = 0x7f040283
com.android.rockchip.mediacodecnew:style/Widget.Material3.BottomAppBar = 0x7f120351
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral10 = 0x7f0600f6
com.android.rockchip.mediacodecnew:id/textinput_helper_text = 0x7f09024e
com.android.rockchip.mediacodecnew:id/currentState = 0x7f0900af
com.android.rockchip.mediacodecnew:drawable/ic_clear_black_24 = 0x7f080089
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_track_color = 0x7f0d0141
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_rail_container_width = 0x7f07014a
com.android.rockchip.mediacodecnew:color/m3_textfield_indicator_text_color = 0x7f0601ec
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1202a3
com.android.rockchip.mediacodecnew:attr/paddingEnd = 0x7f040367
com.android.rockchip.mediacodecnew:string/mtrl_picker_end_date_description = 0x7f110070
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error70 = 0x7f0600f0
com.android.rockchip.mediacodecnew:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f07029c
com.android.rockchip.mediacodecnew:attr/subheaderColor = 0x7f0403ff
com.android.rockchip.mediacodecnew:dimen/m3_small_fab_max_image_size = 0x7f0701e5
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0d009a
com.android.rockchip.mediacodecnew:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0700ad
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error40 = 0x7f0600ed
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error30 = 0x7f0600ec
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1201dc
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error10 = 0x7f0600e9
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error0 = 0x7f0600e8
com.android.rockchip.mediacodecnew:attr/mock_showDiagonals = 0x7f04031e
com.android.rockchip.mediacodecnew:attr/cursorErrorColor = 0x7f040162
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_background = 0x7f060260
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadlineSmall = 0x7f040445
com.android.rockchip.mediacodecnew:attr/startIconMinSize = 0x7f0403ec
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0600e4
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0600e3
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0600e2
com.android.rockchip.mediacodecnew:drawable/design_password_eye = 0x7f080085
com.android.rockchip.mediacodecnew:attr/badgeShapeAppearance = 0x7f040058
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0600e1
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Menu = 0x7f120027
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0600dc
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.android.rockchip.mediacodecnew:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f070213
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary99 = 0x7f0600da
com.android.rockchip.mediacodecnew:attr/chipStandaloneStyle = 0x7f0400ce
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Headline2 = 0x7f120200
com.android.rockchip.mediacodecnew:attr/circularflow_defaultAngle = 0x7f0400d7
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary80 = 0x7f0600d7
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant50 = 0x7f060215
com.android.rockchip.mediacodecnew:dimen/abc_star_big = 0x7f07003b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary10 = 0x7f0600cf
com.android.rockchip.mediacodecnew:layout/activity_main = 0x7f0c001c
com.android.rockchip.mediacodecnew:anim/abc_slide_out_top = 0x7f010009
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary0 = 0x7f0600ce
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary70 = 0x7f0600c9
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f120436
com.android.rockchip.mediacodecnew:style/Theme.Material3.Light = 0x7f12023c
com.android.rockchip.mediacodecnew:attr/fastScrollEnabled = 0x7f0401d6
com.android.rockchip.mediacodecnew:color/m3_navigation_item_icon_tint = 0x7f060091
com.android.rockchip.mediacodecnew:color/tp_video_button_text_color = 0x7f0602ec
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ImageButton = 0x7f12030b
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1200df
com.android.rockchip.mediacodecnew:attr/fontWeight = 0x7f040209
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary60 = 0x7f0600c8
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0d0157
com.android.rockchip.mediacodecnew:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700c6
com.android.rockchip.mediacodecnew:dimen/material_clock_face_margin_top = 0x7f07021e
com.android.rockchip.mediacodecnew:id/SHIFT = 0x7f090007
com.android.rockchip.mediacodecnew:attr/badgeWithTextShapeAppearance = 0x7f040063
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary40 = 0x7f0600c6
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary100 = 0x7f0600c3
com.android.rockchip.mediacodecnew:attr/homeLayout = 0x7f040224
com.android.rockchip.mediacodecnew:color/tp_video_button_stroke_pressed = 0x7f0602eb
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f120084
com.android.rockchip.mediacodecnew:attr/flow_firstVerticalBias = 0x7f0401ed
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f12029d
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f12003c
com.android.rockchip.mediacodecnew:attr/useDrawerArrowDrawable = 0x7f0404c1
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600c0
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600be
com.android.rockchip.mediacodecnew:drawable/tp_video_progress_thumb = 0x7f0800f1
com.android.rockchip.mediacodecnew:attr/motionTarget = 0x7f04034a
com.android.rockchip.mediacodecnew:attr/hideMotionSpec = 0x7f04021b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600bd
com.android.rockchip.mediacodecnew:layout/design_layout_snackbar_include = 0x7f0c0023
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600b9
com.android.rockchip.mediacodecnew:color/mtrl_chip_close_icon_tint = 0x7f0602a4
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.TabLayout = 0x7f12010f
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0d00e2
com.android.rockchip.mediacodecnew:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f06008e
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600b8
com.android.rockchip.mediacodecnew:integer/m3_btn_anim_delay_ms = 0x7f0a000a
com.android.rockchip.mediacodecnew:attr/setsTag = 0x7f0403b7
com.android.rockchip.mediacodecnew:attr/colorPrimaryDark = 0x7f040117
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral94 = 0x7f060108
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600b4
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f120097
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_icon_size = 0x7f0702c9
com.android.rockchip.mediacodecnew:id/seekbar_exposure_compensation = 0x7f0901e6
com.android.rockchip.mediacodecnew:attr/maxCharacterCount = 0x7f040308
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f070138
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral98 = 0x7f0600b2
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0d0120
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadline2 = 0x7f04043e
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_translation_z_base = 0x7f0702ac
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f120425
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.IconButton.Outlined = 0x7f120360
com.android.rockchip.mediacodecnew:attr/drawableTintMode = 0x7f040192
com.android.rockchip.mediacodecnew:macro/m3_comp_badge_color = 0x7f0d0002
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral96 = 0x7f0600b1
com.android.rockchip.mediacodecnew:attr/isFlipVertical = 0x7f040242
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600b0
com.android.rockchip.mediacodecnew:attr/cardForegroundColor = 0x7f0400a0
com.android.rockchip.mediacodecnew:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f120024
com.android.rockchip.mediacodecnew:attr/constraintSetStart = 0x7f040136
com.android.rockchip.mediacodecnew:attr/motionPath = 0x7f040346
com.android.rockchip.mediacodecnew:attr/paddingStartSystemWindowInsets = 0x7f04036b
com.android.rockchip.mediacodecnew:attr/clockNumberTextColor = 0x7f0400e0
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral94 = 0x7f0600af
com.android.rockchip.mediacodecnew:id/spacer = 0x7f09020c
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600ab
com.android.rockchip.mediacodecnew:dimen/m3_alert_dialog_elevation = 0x7f0700a0
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600aa
com.android.rockchip.mediacodecnew:attr/collapsedTitleTextColor = 0x7f0400ee
com.android.rockchip.mediacodecnew:attr/materialCircleRadius = 0x7f0402f2
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600a1
com.android.rockchip.mediacodecnew:dimen/design_snackbar_text_size = 0x7f070088
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary95 = 0x7f060125
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral17 = 0x7f0600a0
com.android.rockchip.mediacodecnew:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral100 = 0x7f06009e
com.android.rockchip.mediacodecnew:drawable/$m3_avd_show_password__0 = 0x7f08000a
com.android.rockchip.mediacodecnew:color/m3_radiobutton_ripple_tint = 0x7f06009a
com.android.rockchip.mediacodecnew:color/m3_popupmenu_overlay_color = 0x7f060097
com.android.rockchip.mediacodecnew:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f060094
com.android.rockchip.mediacodecnew:attr/tabSecondaryStyle = 0x7f040427
com.android.rockchip.mediacodecnew:color/m3_navigation_item_ripple_color = 0x7f060092
com.android.rockchip.mediacodecnew:id/asConfigured = 0x7f090052
com.android.rockchip.mediacodecnew:attr/crossfade = 0x7f04015f
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral10 = 0x7f060203
com.android.rockchip.mediacodecnew:id/constraint = 0x7f0900a2
com.android.rockchip.mediacodecnew:drawable/abc_btn_default_mtrl_shape = 0x7f080031
com.android.rockchip.mediacodecnew:attr/textAppearanceSearchResultTitle = 0x7f040451
com.android.rockchip.mediacodecnew:attr/onNegativeCross = 0x7f04035e
com.android.rockchip.mediacodecnew:attr/materialDividerStyle = 0x7f0402f6
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0d00b6
com.android.rockchip.mediacodecnew:attr/textInputFilledDenseStyle = 0x7f040461
com.android.rockchip.mediacodecnew:color/m3_icon_button_icon_color_selector = 0x7f06008c
com.android.rockchip.mediacodecnew:color/m3_hint_foreground = 0x7f06008b
com.android.rockchip.mediacodecnew:layout/design_navigation_item_separator = 0x7f0c0029
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadline5 = 0x7f040441
com.android.rockchip.mediacodecnew:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f120012
com.android.rockchip.mediacodecnew:color/m3_filled_icon_button_container_color_selector = 0x7f060089
com.android.rockchip.mediacodecnew:color/m3_fab_efab_foreground_color_selector = 0x7f060087
com.android.rockchip.mediacodecnew:attr/collapsingToolbarLayoutLargeSize = 0x7f0400ef
com.android.rockchip.mediacodecnew:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.android.rockchip.mediacodecnew:attr/itemMaxLines = 0x7f04024f
com.android.rockchip.mediacodecnew:styleable/SearchView = 0x7f13007b
com.android.rockchip.mediacodecnew:color/m3_dynamic_hint_foreground = 0x7f060082
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f12018a
com.android.rockchip.mediacodecnew:id/center_horizontal = 0x7f090090
com.android.rockchip.mediacodecnew:color/tp_video_button_stroke_disabled = 0x7f0602e9
com.android.rockchip.mediacodecnew:color/m3_dynamic_highlighted_text = 0x7f060081
com.android.rockchip.mediacodecnew:style/Widget.Material3.SideSheet.Detached = 0x7f1203d0
com.android.rockchip.mediacodecnew:attr/motionEasingLinearInterpolator = 0x7f040338
com.android.rockchip.mediacodecnew:attr/pivotAnchor = 0x7f04037e
com.android.rockchip.mediacodecnew:color/primary_text_disabled_material_dark = 0x7f0602d6
com.android.rockchip.mediacodecnew:color/m3_dynamic_dark_primary_text_disable_only = 0x7f06007e
com.android.rockchip.mediacodecnew:color/m3_dark_highlighted_text = 0x7f060075
com.android.rockchip.mediacodecnew:id/btn_settings = 0x7f09007e
com.android.rockchip.mediacodecnew:color/m3_chip_assist_text_color = 0x7f06006e
com.android.rockchip.mediacodecnew:styleable/ConstraintLayout_Layout = 0x7f130027
com.android.rockchip.mediacodecnew:layout/dialog_tp_test = 0x7f0c0032
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_30 = 0x7f090028
com.android.rockchip.mediacodecnew:attr/fontProviderFetchStrategy = 0x7f040202
com.android.rockchip.mediacodecnew:color/mtrl_btn_text_color_selector = 0x7f06029d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary40 = 0x7f0600d3
com.android.rockchip.mediacodecnew:color/m3_checkbox_button_icon_tint = 0x7f06006c
com.android.rockchip.mediacodecnew:id/tv_current_time = 0x7f09026f
com.android.rockchip.mediacodecnew:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.android.rockchip.mediacodecnew:drawable/$m3_avd_show_password__1 = 0x7f08000b
com.android.rockchip.mediacodecnew:color/m3_card_stroke_color = 0x7f06006b
com.android.rockchip.mediacodecnew:dimen/m3_fab_translation_z_hovered_focused = 0x7f0701b1
com.android.rockchip.mediacodecnew:color/material_dynamic_primary70 = 0x7f060224
com.android.rockchip.mediacodecnew:color/m3_calendar_item_stroke_color = 0x7f060068
com.android.rockchip.mediacodecnew:color/m3_button_ripple_color_selector = 0x7f060066
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Large = 0x7f1201a0
com.android.rockchip.mediacodecnew:color/m3_button_outline_color_selector = 0x7f060064
com.android.rockchip.mediacodecnew:attr/tintNavigationIcon = 0x7f04048c
com.android.rockchip.mediacodecnew:attr/colorPrimaryFixedDim = 0x7f040119
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f12026f
com.android.rockchip.mediacodecnew:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f080021
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary90 = 0x7f0600cb
com.android.rockchip.mediacodecnew:attr/buttonIconTintMode = 0x7f040097
com.android.rockchip.mediacodecnew:attr/measureWithLargestChild = 0x7f040310
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0d0067
com.android.rockchip.mediacodecnew:attr/fontProviderSystemFontFamily = 0x7f040206
com.android.rockchip.mediacodecnew:color/m3_button_foreground_color_selector = 0x7f060063
com.android.rockchip.mediacodecnew:attr/contentPadding = 0x7f040142
com.android.rockchip.mediacodecnew:attr/triggerReceiver = 0x7f0404bc
com.android.rockchip.mediacodecnew:attr/textAllCaps = 0x7f040432
com.android.rockchip.mediacodecnew:id/design_menu_item_action_area_stub = 0x7f0900bd
com.android.rockchip.mediacodecnew:color/m3_button_background_color_selector = 0x7f060062
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary40 = 0x7f06012c
com.android.rockchip.mediacodecnew:raw/edge_detection_fragment = 0x7f10000f
com.android.rockchip.mediacodecnew:attr/circularflow_angles = 0x7f0400d6
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral30 = 0x7f0600fd
com.android.rockchip.mediacodecnew:style/Widget.Material3.Snackbar.TextView = 0x7f1203d7
com.android.rockchip.mediacodecnew:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1200ab
com.android.rockchip.mediacodecnew:attr/autoSizeTextType = 0x7f040047
com.android.rockchip.mediacodecnew:color/dim_foreground_material_light = 0x7f060057
com.android.rockchip.mediacodecnew:id/off = 0x7f090181
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_box_stroke_width_default = 0x7f0702f7
com.android.rockchip.mediacodecnew:color/design_fab_stroke_end_outer_color = 0x7f06004f
com.android.rockchip.mediacodecnew:color/m3_highlighted_text = 0x7f06008a
com.android.rockchip.mediacodecnew:id/mtrl_picker_title_text = 0x7f09016a
com.android.rockchip.mediacodecnew:attr/chipStrokeWidth = 0x7f0400d1
com.android.rockchip.mediacodecnew:color/design_fab_stroke_end_inner_color = 0x7f06004e
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600bc
com.android.rockchip.mediacodecnew:drawable/ic_m3_chip_checked_circle = 0x7f080091
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary95 = 0x7f060132
com.android.rockchip.mediacodecnew:color/design_fab_shadow_mid_color = 0x7f06004c
com.android.rockchip.mediacodecnew:style/VideoSpeedButton = 0x7f1202f1
com.android.rockchip.mediacodecnew:color/design_error = 0x7f06004a
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral92 = 0x7f060107
com.android.rockchip.mediacodecnew:layout/decoder = 0x7f0c001f
com.android.rockchip.mediacodecnew:color/design_default_color_secondary = 0x7f060047
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0d012c
com.android.rockchip.mediacodecnew:color/design_default_color_primary_variant = 0x7f060046
com.android.rockchip.mediacodecnew:dimen/design_snackbar_padding_vertical = 0x7f070086
com.android.rockchip.mediacodecnew:attr/bottomSheetStyle = 0x7f040080
com.android.rockchip.mediacodecnew:color/bright_foreground_material_light = 0x7f060027
com.android.rockchip.mediacodecnew:color/design_default_color_primary_dark = 0x7f060045
com.android.rockchip.mediacodecnew:color/design_default_color_on_primary = 0x7f060041
com.android.rockchip.mediacodecnew:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070082
com.android.rockchip.mediacodecnew:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.android.rockchip.mediacodecnew:color/mtrl_on_surface_ripple_color = 0x7f0602ba
com.android.rockchip.mediacodecnew:color/design_default_color_on_error = 0x7f060040
com.android.rockchip.mediacodecnew:dimen/tooltip_y_offset_touch = 0x7f07031a
com.android.rockchip.mediacodecnew:attr/sideSheetDialogTheme = 0x7f0403ce
com.android.rockchip.mediacodecnew:style/Widget.Design.NavigationView = 0x7f120342
com.android.rockchip.mediacodecnew:color/design_default_color_background = 0x7f06003d
com.android.rockchip.mediacodecnew:color/design_dark_default_color_secondary = 0x7f06003a
com.android.rockchip.mediacodecnew:color/design_dark_default_color_primary_variant = 0x7f060039
com.android.rockchip.mediacodecnew:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f070185
com.android.rockchip.mediacodecnew:color/switch_thumb_material_light = 0x7f0602e1
com.android.rockchip.mediacodecnew:id/counterclockwise = 0x7f0900ad
com.android.rockchip.mediacodecnew:color/design_dark_default_color_primary_dark = 0x7f060038
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral80 = 0x7f06020b
com.android.rockchip.mediacodecnew:color/design_dark_default_color_primary = 0x7f060037
com.android.rockchip.mediacodecnew:color/design_dark_default_color_on_surface = 0x7f060036
com.android.rockchip.mediacodecnew:attr/fabAlignmentModeEndMargin = 0x7f0401ce
com.android.rockchip.mediacodecnew:color/design_dark_default_color_on_primary = 0x7f060034
com.android.rockchip.mediacodecnew:color/design_dark_default_color_error = 0x7f060031
com.android.rockchip.mediacodecnew:dimen/m3_comp_search_bar_container_height = 0x7f07016e
com.android.rockchip.mediacodecnew:color/design_dark_default_color_background = 0x7f060030
com.android.rockchip.mediacodecnew:color/design_box_stroke_color = 0x7f06002f
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary90 = 0x7f060124
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0d0172
com.android.rockchip.mediacodecnew:attr/upDuration = 0x7f0404bf
com.android.rockchip.mediacodecnew:color/cardview_dark_background = 0x7f06002a
com.android.rockchip.mediacodecnew:color/bright_foreground_inverse_material_dark = 0x7f060024
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0601a4
com.android.rockchip.mediacodecnew:attr/layout_constraintDimensionRatio = 0x7f040284
com.android.rockchip.mediacodecnew:color/bright_foreground_disabled_material_dark = 0x7f060022
com.android.rockchip.mediacodecnew:dimen/m3_btn_icon_only_icon_padding = 0x7f0700d5
com.android.rockchip.mediacodecnew:drawable/m3_appbar_background = 0x7f08009e
com.android.rockchip.mediacodecnew:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f070208
com.android.rockchip.mediacodecnew:color/abc_tint_switch_track = 0x7f060018
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f060196
com.android.rockchip.mediacodecnew:attr/fabCustomSize = 0x7f0401d4
com.android.rockchip.mediacodecnew:color/m3_navigation_item_text_color = 0x7f060093
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1202cb
com.android.rockchip.mediacodecnew:id/baseline = 0x7f090059
com.android.rockchip.mediacodecnew:dimen/cardview_default_elevation = 0x7f070053
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f120394
com.android.rockchip.mediacodecnew:color/abc_tint_spinner = 0x7f060017
com.android.rockchip.mediacodecnew:color/abc_tint_seek_thumb = 0x7f060016
com.android.rockchip.mediacodecnew:color/abc_tint_edittext = 0x7f060015
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_days_of_week_height = 0x7f070275
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f080017
com.android.rockchip.mediacodecnew:color/abc_secondary_text_material_dark = 0x7f060011
com.android.rockchip.mediacodecnew:dimen/mtrl_switch_thumb_size = 0x7f0702f1
com.android.rockchip.mediacodecnew:color/highlighted_text_material_light = 0x7f06005d
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1202a0
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral99 = 0x7f06010c
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f070150
com.android.rockchip.mediacodecnew:attr/materialCalendarDayOfWeekLabel = 0x7f0402e0
com.android.rockchip.mediacodecnew:color/abc_primary_text_material_dark = 0x7f06000b
com.android.rockchip.mediacodecnew:color/mtrl_outlined_stroke_color = 0x7f0602bc
com.android.rockchip.mediacodecnew:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.android.rockchip.mediacodecnew:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070216
com.android.rockchip.mediacodecnew:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.android.rockchip.mediacodecnew:color/mtrl_choice_chip_text_color = 0x7f0602a9
com.android.rockchip.mediacodecnew:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0b000c
com.android.rockchip.mediacodecnew:color/abc_search_url_text_pressed = 0x7f06000f
com.android.rockchip.mediacodecnew:color/abc_decor_view_status_guard_light = 0x7f060006
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f12042b
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0d009e
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f07010f
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_error_container = 0x7f0601b6
com.android.rockchip.mediacodecnew:attr/carousel_firstView = 0x7f0400a7
com.android.rockchip.mediacodecnew:attr/onStateTransition = 0x7f040361
com.android.rockchip.mediacodecnew:color/abc_btn_colored_text_material = 0x7f060003
com.android.rockchip.mediacodecnew:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.android.rockchip.mediacodecnew:color/ripple_material_light = 0x7f0602d9
com.android.rockchip.mediacodecnew:layout/design_navigation_menu = 0x7f0c002b
com.android.rockchip.mediacodecnew:anim/abc_fade_in = 0x7f010000
com.android.rockchip.mediacodecnew:attr/linearProgressIndicatorStyle = 0x7f0402be
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600a9
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f120309
com.android.rockchip.mediacodecnew:raw/object_fragment = 0x7f10001c
com.android.rockchip.mediacodecnew:attr/layoutDuringTransition = 0x7f040271
com.android.rockchip.mediacodecnew:attr/zoomEnabled = 0x7f0404e1
com.android.rockchip.mediacodecnew:raw/basic_deformation_fragment = 0x7f100002
com.android.rockchip.mediacodecnew:drawable/tp_video_button_background = 0x7f0800ed
com.android.rockchip.mediacodecnew:attr/yearSelectedStyle = 0x7f0404de
com.android.rockchip.mediacodecnew:id/parentRelative = 0x7f090199
com.android.rockchip.mediacodecnew:dimen/m3_comp_slider_inactive_track_height = 0x7f070182
com.android.rockchip.mediacodecnew:dimen/mtrl_shape_corner_size_medium_component = 0x7f0702dc
com.android.rockchip.mediacodecnew:attr/controlBackground = 0x7f04014b
com.android.rockchip.mediacodecnew:dimen/mtrl_switch_text_padding = 0x7f0702ee
com.android.rockchip.mediacodecnew:layout/material_timepicker_dialog = 0x7f0c004a
com.android.rockchip.mediacodecnew:attr/elevation = 0x7f04019f
com.android.rockchip.mediacodecnew:attr/titlePositionInterpolator = 0x7f040497
com.android.rockchip.mediacodecnew:attr/windowActionBarOverlay = 0x7f0404d5
com.android.rockchip.mediacodecnew:string/abc_activity_chooser_view_see_all = 0x7f110004
com.android.rockchip.mediacodecnew:attr/colorOnSecondary = 0x7f040108
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600ad
com.android.rockchip.mediacodecnew:attr/windowActionBar = 0x7f0404d4
com.android.rockchip.mediacodecnew:id/centerCrop = 0x7f09008e
com.android.rockchip.mediacodecnew:dimen/mtrl_alert_dialog_background_inset_top = 0x7f070242
com.android.rockchip.mediacodecnew:attr/defaultState = 0x7f040176
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary70 = 0x7f06023e
com.android.rockchip.mediacodecnew:string/character_counter_content_description = 0x7f110024
com.android.rockchip.mediacodecnew:attr/hintTextColor = 0x7f040222
com.android.rockchip.mediacodecnew:attr/waveShape = 0x7f0404d2
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f070124
com.android.rockchip.mediacodecnew:color/error_color_material_dark = 0x7f060058
com.android.rockchip.mediacodecnew:attr/waveOffset = 0x7f0404cf
com.android.rockchip.mediacodecnew:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f070187
com.android.rockchip.mediacodecnew:id/topPanel = 0x7f09025a
com.android.rockchip.mediacodecnew:attr/visibilityMode = 0x7f0404cb
com.android.rockchip.mediacodecnew:attr/toolbarNavigationButtonStyle = 0x7f04049e
com.android.rockchip.mediacodecnew:dimen/notification_large_icon_width = 0x7f070309
com.android.rockchip.mediacodecnew:attr/viewTransitionOnCross = 0x7f0404c8
com.android.rockchip.mediacodecnew:attr/textInputOutlinedDenseStyle = 0x7f040465
com.android.rockchip.mediacodecnew:attr/viewTransitionMode = 0x7f0404c7
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f120137
com.android.rockchip.mediacodecnew:attr/floatingActionButtonLargeStyle = 0x7f0401de
com.android.rockchip.mediacodecnew:attr/collapsedTitleTextAppearance = 0x7f0400ed
com.android.rockchip.mediacodecnew:attr/verticalOffsetWithText = 0x7f0404c5
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f07019f
com.android.rockchip.mediacodecnew:attr/searchHintIcon = 0x7f0403ae
com.android.rockchip.mediacodecnew:attr/values = 0x7f0404c3
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.SearchView = 0x7f1201f4
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.BodySmall = 0x7f1201e8
com.android.rockchip.mediacodecnew:string/abc_action_menu_overflow_description = 0x7f110002
com.android.rockchip.mediacodecnew:color/material_personalized_primary_text_disable_only = 0x7f06028b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary0 = 0x7f0600c1
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_long1 = 0x7f0a0013
com.android.rockchip.mediacodecnew:attr/motionEffect_viewTransition = 0x7f040344
com.android.rockchip.mediacodecnew:attr/actionModeWebSearchDrawable = 0x7f040021
com.android.rockchip.mediacodecnew:style/Base.V28.Theme.AppCompat.Light = 0x7f1200ba
com.android.rockchip.mediacodecnew:id/rb_format_png = 0x7f0901b5
com.android.rockchip.mediacodecnew:attr/useCompatPadding = 0x7f0404c0
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.TonalButton.Icon = 0x7f12036a
com.android.rockchip.mediacodecnew:attr/actionMenuTextColor = 0x7f040012
com.android.rockchip.mediacodecnew:attr/ttcIndex = 0x7f0404be
com.android.rockchip.mediacodecnew:attr/listPreferredItemPaddingEnd = 0x7f0402ca
com.android.rockchip.mediacodecnew:attr/triggerSlack = 0x7f0404bd
com.android.rockchip.mediacodecnew:attr/transitionFlags = 0x7f0404b8
com.android.rockchip.mediacodecnew:dimen/m3_side_sheet_modal_elevation = 0x7f0701de
com.android.rockchip.mediacodecnew:animator/fragment_close_enter = 0x7f020003
com.android.rockchip.mediacodecnew:attr/cornerSizeTopRight = 0x7f040158
com.android.rockchip.mediacodecnew:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f120081
com.android.rockchip.mediacodecnew:attr/transformPivotTarget = 0x7f0404b5
com.android.rockchip.mediacodecnew:drawable/abc_btn_check_material = 0x7f08002c
com.android.rockchip.mediacodecnew:styleable/SwitchCompat = 0x7f130087
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f12003e
com.android.rockchip.mediacodecnew:id/actionDown = 0x7f090030
com.android.rockchip.mediacodecnew:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f080024
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_standard = 0x7f11003d
com.android.rockchip.mediacodecnew:attr/layout_constraintHeight_default = 0x7f04028b
com.android.rockchip.mediacodecnew:attr/trackDecorationTint = 0x7f0404af
com.android.rockchip.mediacodecnew:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary95 = 0x7f0600cc
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1200e1
com.android.rockchip.mediacodecnew:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f12009d
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f060187
com.android.rockchip.mediacodecnew:dimen/design_navigation_separator_vertical_padding = 0x7f07007d
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadline1 = 0x7f04043d
com.android.rockchip.mediacodecnew:attr/warmth = 0x7f0404cd
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0d0057
com.android.rockchip.mediacodecnew:drawable/btn_radio_on_mtrl = 0x7f080080
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f0702fb
com.android.rockchip.mediacodecnew:attr/trackColorInactive = 0x7f0404ac
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1201a7
com.android.rockchip.mediacodecnew:attr/trackColor = 0x7f0404aa
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1200fc
com.android.rockchip.mediacodecnew:string/abc_action_bar_up_description = 0x7f110001
com.android.rockchip.mediacodecnew:attr/counterMaxLength = 0x7f04015a
com.android.rockchip.mediacodecnew:attr/suffixTextColor = 0x7f04040b
com.android.rockchip.mediacodecnew:macro/m3_comp_slider_handle_color = 0x7f0d0110
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0d0072
com.android.rockchip.mediacodecnew:layout/material_clock_display_divider = 0x7f0c0040
com.android.rockchip.mediacodecnew:attr/touchRegionId = 0x7f0404a8
com.android.rockchip.mediacodecnew:attr/layout_constraintWidth_min = 0x7f0402a4
com.android.rockchip.mediacodecnew:attr/windowFixedWidthMinor = 0x7f0404da
com.android.rockchip.mediacodecnew:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0701b7
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Filter.Elevated = 0x7f120373
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary90 = 0x7f0600d8
com.android.rockchip.mediacodecnew:id/seekbar_denoise = 0x7f0901e5
com.android.rockchip.mediacodecnew:attr/waveDecay = 0x7f0404ce
com.android.rockchip.mediacodecnew:style/TpVideoSpeedItem = 0x7f1202ee
com.android.rockchip.mediacodecnew:color/design_default_color_primary = 0x7f060044
com.android.rockchip.mediacodecnew:attr/textAppearanceLabelLarge = 0x7f040446
com.android.rockchip.mediacodecnew:drawable/notification_bg_normal = 0x7f0800df
com.android.rockchip.mediacodecnew:attr/telltales_velocityMode = 0x7f040431
com.android.rockchip.mediacodecnew:attr/tooltipFrameBackground = 0x7f0404a2
com.android.rockchip.mediacodecnew:drawable/ic_mtrl_chip_close_circle = 0x7f080096
com.android.rockchip.mediacodecnew:attr/region_widthLessThan = 0x7f0403a1
com.android.rockchip.mediacodecnew:attr/showMotionSpec = 0x7f0403c9
com.android.rockchip.mediacodecnew:attr/liftOnScrollColor = 0x7f0402b9
com.android.rockchip.mediacodecnew:attr/toolbarId = 0x7f04049d
com.android.rockchip.mediacodecnew:id/TOP_END = 0x7f09000c
com.android.rockchip.mediacodecnew:attr/homeAsUpIndicator = 0x7f040223
com.android.rockchip.mediacodecnew:attr/toggleCheckedStateOnClick = 0x7f04049c
com.android.rockchip.mediacodecnew:attr/colorOnPrimaryContainer = 0x7f040104
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0d006c
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f07026a
com.android.rockchip.mediacodecnew:attr/titleTextColor = 0x7f040499
com.android.rockchip.mediacodecnew:dimen/material_helper_text_default_padding_top = 0x7f070235
com.android.rockchip.mediacodecnew:attr/titleMargins = 0x7f040496
com.android.rockchip.mediacodecnew:style/Widget.Material3.NavigationView = 0x7f1203c3
com.android.rockchip.mediacodecnew:attr/titleMarginTop = 0x7f040495
com.android.rockchip.mediacodecnew:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0c0054
com.android.rockchip.mediacodecnew:attr/yearStyle = 0x7f0404df
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f1202e0
com.android.rockchip.mediacodecnew:drawable/notification_bg_normal_pressed = 0x7f0800e0
com.android.rockchip.mediacodecnew:attr/titleMarginStart = 0x7f040494
com.android.rockchip.mediacodecnew:attr/tooltipForegroundColor = 0x7f0404a1
com.android.rockchip.mediacodecnew:color/material_on_primary_disabled = 0x7f060251
com.android.rockchip.mediacodecnew:attr/layout_constraintVertical_bias = 0x7f04029e
com.android.rockchip.mediacodecnew:attr/shapeAppearanceMediumComponent = 0x7f0403bf
com.android.rockchip.mediacodecnew:attr/backgroundOverlayColorAlpha = 0x7f040050
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f1202dc
com.android.rockchip.mediacodecnew:attr/titleMargin = 0x7f040491
com.android.rockchip.mediacodecnew:attr/motion_postLayoutCollision = 0x7f04034b
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_padding_bottom = 0x7f07025e
com.android.rockchip.mediacodecnew:attr/actionButtonStyle = 0x7f04000e
com.android.rockchip.mediacodecnew:attr/popupMenuStyle = 0x7f040385
com.android.rockchip.mediacodecnew:color/design_dark_default_color_on_error = 0x7f060033
com.android.rockchip.mediacodecnew:attr/helperTextTextAppearance = 0x7f040218
com.android.rockchip.mediacodecnew:attr/tabUnboundedRipple = 0x7f04042d
com.android.rockchip.mediacodecnew:attr/tabTextColor = 0x7f04042c
com.android.rockchip.mediacodecnew:attr/titleEnabled = 0x7f040490
com.android.rockchip.mediacodecnew:attr/titleCollapseMode = 0x7f04048f
com.android.rockchip.mediacodecnew:dimen/m3_badge_horizontal_offset = 0x7f0700b3
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Animation = 0x7f120125
com.android.rockchip.mediacodecnew:attr/title = 0x7f04048d
com.android.rockchip.mediacodecnew:attr/tintMode = 0x7f04048b
com.android.rockchip.mediacodecnew:style/Widget.Material3.Slider.Label = 0x7f1203d4
com.android.rockchip.mediacodecnew:integer/material_motion_path = 0x7f0a002c
com.android.rockchip.mediacodecnew:attr/tickVisible = 0x7f040489
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f070148
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f12016d
com.android.rockchip.mediacodecnew:id/menu_roi_mode = 0x7f090145
com.android.rockchip.mediacodecnew:color/m3_card_ripple_color = 0x7f06006a
com.android.rockchip.mediacodecnew:attr/tickMarkTintMode = 0x7f040486
com.android.rockchip.mediacodecnew:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0702e8
com.android.rockchip.mediacodecnew:drawable/ic_mtrl_chip_checked_circle = 0x7f080095
com.android.rockchip.mediacodecnew:id/design_menu_item_text = 0x7f0900be
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral20 = 0x7f0600fa
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f120191
com.android.rockchip.mediacodecnew:attr/helperTextTextColor = 0x7f040219
com.android.rockchip.mediacodecnew:attr/tickMarkTint = 0x7f040485
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0d0094
com.android.rockchip.mediacodecnew:drawable/dialog_background = 0x7f080087
com.android.rockchip.mediacodecnew:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.android.rockchip.mediacodecnew:drawable/ic_launcher_foreground = 0x7f08008f
com.android.rockchip.mediacodecnew:attr/percentHeight = 0x7f040379
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1203a5
com.android.rockchip.mediacodecnew:layout/mtrl_picker_header_dialog = 0x7f0c0066
com.android.rockchip.mediacodecnew:attr/ifTagSet = 0x7f040232
com.android.rockchip.mediacodecnew:attr/tickMark = 0x7f040484
com.android.rockchip.mediacodecnew:string/mtrl_exceed_max_badge_number_content_description = 0x7f110063
com.android.rockchip.mediacodecnew:attr/tickColor = 0x7f040481
com.android.rockchip.mediacodecnew:color/mtrl_switch_track_decoration_tint = 0x7f0602c1
com.android.rockchip.mediacodecnew:attr/thumbTintMode = 0x7f040480
com.android.rockchip.mediacodecnew:id/btn_switch_mode = 0x7f090083
com.android.rockchip.mediacodecnew:attr/polarRelativeTo = 0x7f040383
com.android.rockchip.mediacodecnew:attr/textAppearanceSearchResultSubtitle = 0x7f040450
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f070141
com.android.rockchip.mediacodecnew:dimen/m3_extended_fab_end_padding = 0x7f0701aa
com.android.rockchip.mediacodecnew:attr/thumbStrokeWidth = 0x7f04047d
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_icon_margin = 0x7f0702c8
com.android.rockchip.mediacodecnew:attr/state_dragged = 0x7f0403f3
com.android.rockchip.mediacodecnew:attr/thumbIconTint = 0x7f040479
com.android.rockchip.mediacodecnew:attr/thumbIconSize = 0x7f040478
com.android.rockchip.mediacodecnew:attr/layout_constraintBaseline_toTopOf = 0x7f04027d
com.android.rockchip.mediacodecnew:dimen/abc_config_prefDialogWidth = 0x7f070017
com.android.rockchip.mediacodecnew:attr/subheaderTextAppearance = 0x7f040402
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1203a3
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f12038f
com.android.rockchip.mediacodecnew:id/icon_group = 0x7f090108
com.android.rockchip.mediacodecnew:attr/thumbIcon = 0x7f040477
com.android.rockchip.mediacodecnew:dimen/mtrl_alert_dialog_background_inset_start = 0x7f070241
com.android.rockchip.mediacodecnew:attr/thickness = 0x7f040474
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_container = 0x7f060279
com.android.rockchip.mediacodecnew:id/tv_contrast_value = 0x7f09026a
com.android.rockchip.mediacodecnew:attr/track = 0x7f0404a9
com.android.rockchip.mediacodecnew:styleable/LightOpenGlView = 0x7f130048
com.android.rockchip.mediacodecnew:color/notification_icon_bg_color = 0x7f0602cf
com.android.rockchip.mediacodecnew:dimen/material_textinput_default_width = 0x7f070239
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.LabelMedium = 0x7f1201f0
com.android.rockchip.mediacodecnew:attr/splitTrack = 0x7f0403e0
com.android.rockchip.mediacodecnew:drawable/abc_action_bar_item_background_material = 0x7f08002a
com.android.rockchip.mediacodecnew:attr/textureWidth = 0x7f040472
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0800b9
com.android.rockchip.mediacodecnew:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f070250
com.android.rockchip.mediacodecnew:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.android.rockchip.mediacodecnew:attr/textureHeight = 0x7f040471
com.android.rockchip.mediacodecnew:attr/textureEffect = 0x7f040470
com.android.rockchip.mediacodecnew:layout/mtrl_calendar_month_labeled = 0x7f0c005b
com.android.rockchip.mediacodecnew:id/progress_horizontal = 0x7f0901a9
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f07012b
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_rail_icon_size = 0x7f07014d
com.android.rockchip.mediacodecnew:color/m3_fab_ripple_color_selector = 0x7f060088
com.android.rockchip.mediacodecnew:attr/layout_constraintGuide_begin = 0x7f040287
com.android.rockchip.mediacodecnew:color/m3_assist_chip_icon_tint_color = 0x7f06005f
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0d0080
com.android.rockchip.mediacodecnew:attr/textBackgroundZoom = 0x7f04045c
com.android.rockchip.mediacodecnew:attr/colorTertiaryFixed = 0x7f04012f
com.android.rockchip.mediacodecnew:attr/textPanY = 0x7f04046d
com.android.rockchip.mediacodecnew:attr/materialAlertDialogTitleTextStyle = 0x7f0402db
com.android.rockchip.mediacodecnew:drawable/abc_seekbar_tick_mark_material = 0x7f080064
com.android.rockchip.mediacodecnew:dimen/m3_slider_thumb_elevation = 0x7f0701e4
com.android.rockchip.mediacodecnew:integer/cancel_button_image_alpha = 0x7f0a0004
com.android.rockchip.mediacodecnew:attr/multiChoiceItemLayout = 0x7f04034e
com.android.rockchip.mediacodecnew:attr/colorControlNormal = 0x7f0400fb
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f120449
com.android.rockchip.mediacodecnew:id/scroll = 0x7f0901cc
com.android.rockchip.mediacodecnew:attr/iconTint = 0x7f04022e
com.android.rockchip.mediacodecnew:attr/textLocale = 0x7f040469
com.android.rockchip.mediacodecnew:integer/mtrl_btn_anim_duration_ms = 0x7f0a002f
com.android.rockchip.mediacodecnew:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0701c0
com.android.rockchip.mediacodecnew:attr/textInputFilledStyle = 0x7f040463
com.android.rockchip.mediacodecnew:color/material_personalized_hint_foreground_inverse = 0x7f060289
com.android.rockchip.mediacodecnew:attr/itemHorizontalTranslationEnabled = 0x7f04024b
com.android.rockchip.mediacodecnew:layout/material_timepicker = 0x7f0c0049
com.android.rockchip.mediacodecnew:attr/textBackgroundRotate = 0x7f04045b
com.android.rockchip.mediacodecnew:attr/queryBackground = 0x7f040392
com.android.rockchip.mediacodecnew:attr/listPreferredItemPaddingRight = 0x7f0402cc
com.android.rockchip.mediacodecnew:attr/textAppearanceTitleMedium = 0x7f040456
com.android.rockchip.mediacodecnew:styleable/CompoundButton = 0x7f130025
com.android.rockchip.mediacodecnew:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0701b8
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error20 = 0x7f0600eb
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f1203ed
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_extra_long2 = 0x7f0a0010
com.android.rockchip.mediacodecnew:color/tp_video_play_button_bg_normal = 0x7f0602ed
com.android.rockchip.mediacodecnew:attr/textAppearanceSmallPopupMenu = 0x7f040452
com.android.rockchip.mediacodecnew:id/text_input_end_icon = 0x7f090249
com.android.rockchip.mediacodecnew:attr/textAppearanceOverline = 0x7f04044e
com.android.rockchip.mediacodecnew:attr/carousel_backwardTransition = 0x7f0400a5
com.android.rockchip.mediacodecnew:attr/textAppearanceListItemSmall = 0x7f04044d
com.android.rockchip.mediacodecnew:attr/textAppearanceListItemSecondary = 0x7f04044c
com.android.rockchip.mediacodecnew:attr/layout_constraintBottom_toBottomOf = 0x7f04027f
com.android.rockchip.mediacodecnew:attr/textAppearanceListItem = 0x7f04044b
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0d00fc
com.android.rockchip.mediacodecnew:attr/motionEasingStandardInterpolator = 0x7f04033c
com.android.rockchip.mediacodecnew:dimen/design_navigation_item_icon_padding = 0x7f070079
com.android.rockchip.mediacodecnew:dimen/mtrl_tooltip_arrowSize = 0x7f0702fe
com.android.rockchip.mediacodecnew:attr/textAppearanceLineHeightEnabled = 0x7f04044a
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_container_high = 0x7f06027a
com.android.rockchip.mediacodecnew:attr/toolbarStyle = 0x7f04049f
com.android.rockchip.mediacodecnew:attr/textAppearanceLargePopupMenu = 0x7f040449
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadlineMedium = 0x7f040444
com.android.rockchip.mediacodecnew:dimen/tooltip_horizontal_padding = 0x7f070314
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadline6 = 0x7f040442
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0d001d
com.android.rockchip.mediacodecnew:color/abc_primary_text_material_light = 0x7f06000c
com.android.rockchip.mediacodecnew:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.android.rockchip.mediacodecnew:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f070169
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadline4 = 0x7f040440
com.android.rockchip.mediacodecnew:attr/textAppearanceDisplayMedium = 0x7f04043b
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_bar_icon_size = 0x7f07013e
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f120110
com.android.rockchip.mediacodecnew:attr/tickRadiusActive = 0x7f040487
com.android.rockchip.mediacodecnew:attr/tabIndicatorAnimationMode = 0x7f040418
com.android.rockchip.mediacodecnew:attr/textAppearanceBodySmall = 0x7f040437
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_secondary = 0x7f0601c9
com.android.rockchip.mediacodecnew:attr/carousel_emptyViewsBehavior = 0x7f0400a6
com.android.rockchip.mediacodecnew:attr/thumbRadius = 0x7f04047b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error90 = 0x7f0600f2
com.android.rockchip.mediacodecnew:attr/carousel_forwardTransition = 0x7f0400a8
com.android.rockchip.mediacodecnew:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0800ae
com.android.rockchip.mediacodecnew:color/m3_timepicker_time_input_stroke_color = 0x7f0601f9
com.android.rockchip.mediacodecnew:drawable/abc_popup_background_mtrl_mult = 0x7f08005a
com.android.rockchip.mediacodecnew:id/elastic = 0x7f0900d7
com.android.rockchip.mediacodecnew:attr/customColorValue = 0x7f040166
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f120290
com.android.rockchip.mediacodecnew:id/groups = 0x7f0900fe
com.android.rockchip.mediacodecnew:attr/motionDurationLong4 = 0x7f040328
com.android.rockchip.mediacodecnew:attr/preserveIconSpacing = 0x7f04038b
com.android.rockchip.mediacodecnew:attr/textAppearanceBodyMedium = 0x7f040436
com.android.rockchip.mediacodecnew:dimen/m3_chip_dragged_translation_z = 0x7f0700f6
com.android.rockchip.mediacodecnew:attr/textAppearanceBodyLarge = 0x7f040435
com.android.rockchip.mediacodecnew:attr/textAppearanceBody2 = 0x7f040434
com.android.rockchip.mediacodecnew:layout/abc_screen_toolbar = 0x7f0c0017
com.android.rockchip.mediacodecnew:attr/textAppearanceBody1 = 0x7f040433
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f120026
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0d004e
com.android.rockchip.mediacodecnew:attr/targetId = 0x7f04042e
com.android.rockchip.mediacodecnew:color/m3_timepicker_button_background_color = 0x7f0601f0
com.android.rockchip.mediacodecnew:attr/tooltipStyle = 0x7f0404a3
com.android.rockchip.mediacodecnew:styleable/CheckedTextView = 0x7f13001c
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f0701f7
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f120104
com.android.rockchip.mediacodecnew:attr/tabRippleColor = 0x7f040426
com.android.rockchip.mediacodecnew:attr/behavior_peekHeight = 0x7f040072
com.android.rockchip.mediacodecnew:attr/tabPaddingTop = 0x7f040425
com.android.rockchip.mediacodecnew:attr/windowActionModeOverlay = 0x7f0404d6
com.android.rockchip.mediacodecnew:id/action_menu_presenter = 0x7f09003f
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_surface = 0x7f060157
com.android.rockchip.mediacodecnew:attr/height = 0x7f040215
com.android.rockchip.mediacodecnew:attr/materialButtonToggleGroupStyle = 0x7f0402de
com.android.rockchip.mediacodecnew:id/settings_menu_recycler = 0x7f0901fb
com.android.rockchip.mediacodecnew:attr/tabPaddingBottom = 0x7f040422
com.android.rockchip.mediacodecnew:attr/tabMode = 0x7f040420
com.android.rockchip.mediacodecnew:attr/tabMinWidth = 0x7f04041f
com.android.rockchip.mediacodecnew:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0701a2
com.android.rockchip.mediacodecnew:styleable/Toolbar = 0x7f130090
com.android.rockchip.mediacodecnew:attr/tabIndicatorHeight = 0x7f04041c
com.android.rockchip.mediacodecnew:dimen/design_fab_translation_z_hovered_focused = 0x7f070073
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Placeholder = 0x7f1201d0
com.android.rockchip.mediacodecnew:attr/flow_firstVerticalStyle = 0x7f0401ee
com.android.rockchip.mediacodecnew:attr/actionOverflowMenuStyle = 0x7f040023
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_background = 0x7f06018a
com.android.rockchip.mediacodecnew:attr/tabIndicatorGravity = 0x7f04041b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600bf
com.android.rockchip.mediacodecnew:attr/tabIndicatorColor = 0x7f040419
com.android.rockchip.mediacodecnew:attr/viewTransitionOnNegativeCross = 0x7f0404c9
com.android.rockchip.mediacodecnew:attr/motionEffect_start = 0x7f040340
com.android.rockchip.mediacodecnew:dimen/m3_bottom_nav_min_height = 0x7f0700c1
com.android.rockchip.mediacodecnew:attr/thumbElevation = 0x7f040476
com.android.rockchip.mediacodecnew:color/material_personalized_color_text_primary_inverse = 0x7f060284
com.android.rockchip.mediacodecnew:color/black = 0x7f060021
com.android.rockchip.mediacodecnew:attr/tabIconTintMode = 0x7f040415
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.Material3.Dark = 0x7f12008a
com.android.rockchip.mediacodecnew:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0d016d
com.android.rockchip.mediacodecnew:attr/tabBackground = 0x7f040411
com.android.rockchip.mediacodecnew:dimen/design_snackbar_min_width = 0x7f070084
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f060190
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary10 = 0x7f06011b
com.android.rockchip.mediacodecnew:attr/actionModeSplitBackground = 0x7f04001e
com.android.rockchip.mediacodecnew:attr/behavior_overlapTop = 0x7f040071
com.android.rockchip.mediacodecnew:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070236
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600b6
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f120190
com.android.rockchip.mediacodecnew:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.android.rockchip.mediacodecnew:dimen/mtrl_card_checked_icon_size = 0x7f070295
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f120067
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_state_description_unchecked = 0x7f110061
com.android.rockchip.mediacodecnew:id/visible_removing_fragment_view_tag = 0x7f090294
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f070158
com.android.rockchip.mediacodecnew:color/m3_sys_color_on_primary_fixed_variant = 0x7f0601d7
com.android.rockchip.mediacodecnew:color/material_on_background_emphasis_medium = 0x7f060250
com.android.rockchip.mediacodecnew:attr/subtitleTextStyle = 0x7f040408
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_height_fullscreen = 0x7f07027b
com.android.rockchip.mediacodecnew:attr/navigationMode = 0x7f040352
com.android.rockchip.mediacodecnew:attr/fabCradleMargin = 0x7f0401d1
com.android.rockchip.mediacodecnew:attr/arcMode = 0x7f040039
com.android.rockchip.mediacodecnew:attr/subtitleTextColor = 0x7f040407
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.Corner.Small = 0x7f120176
com.android.rockchip.mediacodecnew:drawable/abc_item_background_holo_dark = 0x7f08004b
com.android.rockchip.mediacodecnew:attr/checkedIconGravity = 0x7f0400b9
com.android.rockchip.mediacodecnew:attr/actionModeCutDrawable = 0x7f040018
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface_container = 0x7f0601cd
com.android.rockchip.mediacodecnew:attr/subtitle = 0x7f040404
com.android.rockchip.mediacodecnew:attr/subMenuArrow = 0x7f0403fe
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f120299
com.android.rockchip.mediacodecnew:attr/useMaterialThemeColors = 0x7f0404c2
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_card_outline_width = 0x7f070155
com.android.rockchip.mediacodecnew:id/image_view = 0x7f09010d
com.android.rockchip.mediacodecnew:attr/buttonStyle = 0x7f040099
com.android.rockchip.mediacodecnew:attr/roundPercent = 0x7f0403a8
com.android.rockchip.mediacodecnew:attr/trackCornerRadius = 0x7f0404ad
com.android.rockchip.mediacodecnew:string/mtrl_picker_navigate_to_current_year_description = 0x7f110075
com.android.rockchip.mediacodecnew:attr/statusBarBackground = 0x7f0403f9
com.android.rockchip.mediacodecnew:attr/constraints = 0x7f040139
com.android.rockchip.mediacodecnew:attr/state_indeterminate = 0x7f0403f5
com.android.rockchip.mediacodecnew:attr/layout_constraintHeight = 0x7f04028a
com.android.rockchip.mediacodecnew:attr/motionEffect_strict = 0x7f040341
com.android.rockchip.mediacodecnew:attr/state_error = 0x7f0403f4
com.android.rockchip.mediacodecnew:string/mtrl_switch_thumb_path_checked = 0x7f11008b
com.android.rockchip.mediacodecnew:attr/autoPlay = 0x7f040041
com.android.rockchip.mediacodecnew:color/m3_chip_ripple_color = 0x7f060070
com.android.rockchip.mediacodecnew:dimen/m3_appbar_size_large = 0x7f0700aa
com.android.rockchip.mediacodecnew:attr/state_above_anchor = 0x7f0403f0
com.android.rockchip.mediacodecnew:dimen/design_snackbar_padding_vertical_2lines = 0x7f070087
com.android.rockchip.mediacodecnew:attr/startIconTintMode = 0x7f0403ef
com.android.rockchip.mediacodecnew:id/ratio = 0x7f0901b2
com.android.rockchip.mediacodecnew:color/material_grey_900 = 0x7f060249
com.android.rockchip.mediacodecnew:attr/prefixTextAppearance = 0x7f040389
com.android.rockchip.mediacodecnew:id/search_button = 0x7f0901d3
com.android.rockchip.mediacodecnew:id/customPanel = 0x7f0900b1
com.android.rockchip.mediacodecnew:id/arc = 0x7f090051
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_month_horizontal_padding = 0x7f070282
com.android.rockchip.mediacodecnew:color/m3_dynamic_dark_highlighted_text = 0x7f06007c
com.android.rockchip.mediacodecnew:attr/barrierMargin = 0x7f040069
com.android.rockchip.mediacodecnew:id/action_container = 0x7f09003a
com.android.rockchip.mediacodecnew:attr/startIconScaleType = 0x7f0403ed
com.android.rockchip.mediacodecnew:attr/drawableTopCompat = 0x7f040193
com.android.rockchip.mediacodecnew:attr/boxCornerRadiusTopStart = 0x7f040087
com.android.rockchip.mediacodecnew:id/src_over = 0x7f09021e
com.android.rockchip.mediacodecnew:id/open_search_view_scrim = 0x7f09018d
com.android.rockchip.mediacodecnew:attr/startIconDrawable = 0x7f0403eb
com.android.rockchip.mediacodecnew:attr/paddingLeftSystemWindowInsets = 0x7f040368
com.android.rockchip.mediacodecnew:id/aligned = 0x7f090049
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f070210
com.android.rockchip.mediacodecnew:attr/collapsedTitleGravity = 0x7f0400ec
com.android.rockchip.mediacodecnew:attr/subtitleTextAppearance = 0x7f040406
com.android.rockchip.mediacodecnew:attr/backgroundColor = 0x7f04004b
com.android.rockchip.mediacodecnew:attr/startIconCheckable = 0x7f0403e9
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f120362
com.android.rockchip.mediacodecnew:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f12009f
com.android.rockchip.mediacodecnew:dimen/mtrl_card_elevation = 0x7f070298
com.android.rockchip.mediacodecnew:attr/flow_horizontalStyle = 0x7f0401f2
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant95 = 0x7f06021a
com.android.rockchip.mediacodecnew:id/wifi_status_text = 0x7f090297
com.android.rockchip.mediacodecnew:color/abc_search_url_text_normal = 0x7f06000e
com.android.rockchip.mediacodecnew:id/btn_browse_path = 0x7f090067
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f070201
com.android.rockchip.mediacodecnew:macro/m3_comp_checkbox_selected_icon_color = 0x7f0d000b
com.android.rockchip.mediacodecnew:attr/drawableRightCompat = 0x7f04018e
com.android.rockchip.mediacodecnew:attr/textAppearanceCaption = 0x7f040439
com.android.rockchip.mediacodecnew:attr/floatingActionButtonPrimaryStyle = 0x7f0401e1
com.android.rockchip.mediacodecnew:attr/staggered = 0x7f0403e8
com.android.rockchip.mediacodecnew:attr/srcCompat = 0x7f0403e6
com.android.rockchip.mediacodecnew:attr/switchStyle = 0x7f04040f
com.android.rockchip.mediacodecnew:attr/springStopThreshold = 0x7f0403e5
com.android.rockchip.mediacodecnew:attr/springDamping = 0x7f0403e2
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_supporting_text_type = 0x7f0d00f0
com.android.rockchip.mediacodecnew:attr/springBoundary = 0x7f0403e1
com.android.rockchip.mediacodecnew:styleable/MaterialCardView = 0x7f130054
com.android.rockchip.mediacodecnew:style/Base.V28.Theme.AppCompat = 0x7f1200b9
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Subhead = 0x7f12002d
com.android.rockchip.mediacodecnew:attr/nestedScrollable = 0x7f040357
com.android.rockchip.mediacodecnew:string/material_motion_easing_standard = 0x7f11004b
com.android.rockchip.mediacodecnew:attr/spinnerStyle = 0x7f0403df
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_secondary_container = 0x7f060160
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionBar.Solid = 0x7f1202f3
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0d0101
com.android.rockchip.mediacodecnew:id/tag_on_receive_content_mime_types = 0x7f09023a
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral6 = 0x7f060101
com.android.rockchip.mediacodecnew:attr/colorAccent = 0x7f0400f5
com.android.rockchip.mediacodecnew:attr/spinBars = 0x7f0403dd
com.android.rockchip.mediacodecnew:attr/cursorColor = 0x7f040161
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_thumb_elevation = 0x7f0702e2
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0701f4
com.android.rockchip.mediacodecnew:dimen/m3_chip_disabled_translation_z = 0x7f0700f5
com.android.rockchip.mediacodecnew:attr/simpleItems = 0x7f0403d3
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface_dim = 0x7f0601d2
com.android.rockchip.mediacodecnew:attr/behavior_autoShrink = 0x7f04006b
com.android.rockchip.mediacodecnew:attr/menu = 0x7f040311
com.android.rockchip.mediacodecnew:attr/singleSelection = 0x7f0403d6
com.android.rockchip.mediacodecnew:styleable/KeyFramesAcceleration = 0x7f130042
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f12015c
com.android.rockchip.mediacodecnew:attr/fontProviderAuthority = 0x7f040200
com.android.rockchip.mediacodecnew:attr/motionEasingDecelerated = 0x7f040332
com.android.rockchip.mediacodecnew:attr/simpleItemLayout = 0x7f0403d0
com.android.rockchip.mediacodecnew:string/mtrl_picker_navigate_to_year_description = 0x7f110076
com.android.rockchip.mediacodecnew:id/disableScroll = 0x7f0900c6
com.android.rockchip.mediacodecnew:color/mtrl_chip_background_color = 0x7f0602a3
com.android.rockchip.mediacodecnew:attr/motionDurationMedium4 = 0x7f04032c
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f07018f
com.android.rockchip.mediacodecnew:style/Widget.Compat.NotificationActionContainer = 0x7f12033b
com.android.rockchip.mediacodecnew:attr/showTitle = 0x7f0403cc
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionBar = 0x7f1202f2
com.android.rockchip.mediacodecnew:drawable/$m3_avd_hide_password__1 = 0x7f080008
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_secondary = 0x7f060155
com.android.rockchip.mediacodecnew:attr/colorSurfaceContainerLow = 0x7f040127
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0d0070
com.android.rockchip.mediacodecnew:attr/viewTransitionOnPositiveCross = 0x7f0404ca
com.android.rockchip.mediacodecnew:attr/iconGravity = 0x7f04022a
com.android.rockchip.mediacodecnew:attr/trackTintMode = 0x7f0404b4
com.android.rockchip.mediacodecnew:macro/m3_comp_bottom_app_bar_container_color = 0x7f0d0005
com.android.rockchip.mediacodecnew:attr/queryHint = 0x7f040393
com.android.rockchip.mediacodecnew:attr/showDelay = 0x7f0403c7
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_padding_right = 0x7f070260
com.android.rockchip.mediacodecnew:id/test_button = 0x7f090241
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f070211
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f1203fc
com.android.rockchip.mediacodecnew:attr/actionModeSelectAllDrawable = 0x7f04001c
com.android.rockchip.mediacodecnew:attr/progressBarStyle = 0x7f04038e
com.android.rockchip.mediacodecnew:attr/showAnimationBehavior = 0x7f0403c5
com.android.rockchip.mediacodecnew:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f070170
com.android.rockchip.mediacodecnew:attr/fastScrollVerticalThumbDrawable = 0x7f0401d9
com.android.rockchip.mediacodecnew:color/secondary_text_disabled_material_light = 0x7f0602dd
com.android.rockchip.mediacodecnew:attr/shouldRemoveExpandedCorners = 0x7f0403c4
com.android.rockchip.mediacodecnew:drawable/ic_m3_chip_check = 0x7f080090
com.android.rockchip.mediacodecnew:id/north = 0x7f09017d
com.android.rockchip.mediacodecnew:color/m3_tonal_button_ripple_color_selector = 0x7f0601fa
com.android.rockchip.mediacodecnew:attr/shapeAppearanceLargeComponent = 0x7f0403be
com.android.rockchip.mediacodecnew:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f080027
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_container_shape = 0x7f0d00b3
com.android.rockchip.mediacodecnew:attr/shapeAppearanceCornerLarge = 0x7f0403bb
com.android.rockchip.mediacodecnew:attr/region_heightMoreThan = 0x7f0403a0
com.android.rockchip.mediacodecnew:attr/trackHeight = 0x7f0404b1
com.android.rockchip.mediacodecnew:attr/shapeAppearance = 0x7f0403b8
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_year_width = 0x7f070293
com.android.rockchip.mediacodecnew:attr/selectorSize = 0x7f0403b6
com.android.rockchip.mediacodecnew:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f070106
com.android.rockchip.mediacodecnew:attr/selectableItemBackgroundBorderless = 0x7f0403b4
com.android.rockchip.mediacodecnew:attr/transitionPathRotate = 0x7f0404b9
com.android.rockchip.mediacodecnew:style/Base.V21.Theme.MaterialComponents = 0x7f1200a6
com.android.rockchip.mediacodecnew:attr/searchViewStyle = 0x7f0403b1
com.android.rockchip.mediacodecnew:attr/sizePercent = 0x7f0403d7
com.android.rockchip.mediacodecnew:attr/scaleFromTextSize = 0x7f0403aa
com.android.rockchip.mediacodecnew:attr/saturation = 0x7f0403a9
com.android.rockchip.mediacodecnew:color/mtrl_navigation_bar_colored_item_tint = 0x7f0602b2
com.android.rockchip.mediacodecnew:styleable/ButtonBarLayout = 0x7f130018
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f120419
com.android.rockchip.mediacodecnew:color/design_default_color_on_background = 0x7f06003f
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0601b1
com.android.rockchip.mediacodecnew:color/design_snackbar_background_color = 0x7f060053
com.android.rockchip.mediacodecnew:attr/itemTextAppearance = 0x7f04025f
com.android.rockchip.mediacodecnew:dimen/m3_card_dragged_z = 0x7f0700e5
com.android.rockchip.mediacodecnew:string/mtrl_switch_thumb_path_name = 0x7f11008d
com.android.rockchip.mediacodecnew:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0d0106
com.android.rockchip.mediacodecnew:color/material_grey_300 = 0x7f060244
com.android.rockchip.mediacodecnew:attr/reverseLayout = 0x7f0403a4
com.android.rockchip.mediacodecnew:attr/region_widthMoreThan = 0x7f0403a2
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialDivider = 0x7f120439
com.android.rockchip.mediacodecnew:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
com.android.rockchip.mediacodecnew:dimen/m3_badge_offset = 0x7f0700b4
com.android.rockchip.mediacodecnew:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0601db
com.android.rockchip.mediacodecnew:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f070196
com.android.rockchip.mediacodecnew:attr/reactiveGuide_valueId = 0x7f04039d
com.android.rockchip.mediacodecnew:attr/maxLines = 0x7f04030b
com.android.rockchip.mediacodecnew:id/inward = 0x7f090114
com.android.rockchip.mediacodecnew:attr/wavePhase = 0x7f0404d1
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_8 = 0x7f09002e
com.android.rockchip.mediacodecnew:attr/floatingActionButtonSurfaceStyle = 0x7f0401e9
com.android.rockchip.mediacodecnew:attr/passwordToggleDrawable = 0x7f040373
com.android.rockchip.mediacodecnew:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0a0037
com.android.rockchip.mediacodecnew:attr/reactiveGuide_applyToConstraintSet = 0x7f04039c
com.android.rockchip.mediacodecnew:attr/reactiveGuide_applyToAllConstraintSets = 0x7f04039b
com.android.rockchip.mediacodecnew:attr/iconSize = 0x7f04022c
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1200e3
com.android.rockchip.mediacodecnew:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0d0009
com.android.rockchip.mediacodecnew:attr/tint = 0x7f04048a
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_long3 = 0x7f0a0015
com.android.rockchip.mediacodecnew:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402c0
com.android.rockchip.mediacodecnew:attr/ratingBarStyleSmall = 0x7f040399
com.android.rockchip.mediacodecnew:styleable/CircularProgressIndicator = 0x7f13001f
com.android.rockchip.mediacodecnew:attr/fabCradleVerticalOffset = 0x7f0401d3
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.RatingBar.Indicator = 0x7f12032d
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_height = 0x7f070063
com.android.rockchip.mediacodecnew:attr/rangeFillColor = 0x7f040396
com.android.rockchip.mediacodecnew:dimen/notification_subtext_size = 0x7f070310
com.android.rockchip.mediacodecnew:attr/queryPatterns = 0x7f040394
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f070147
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_primary_small_container_shape = 0x7f0d003b
com.android.rockchip.mediacodecnew:attr/quantizeMotionSteps = 0x7f040391
com.android.rockchip.mediacodecnew:id/open_search_view_header_container = 0x7f09018b
com.android.rockchip.mediacodecnew:dimen/design_snackbar_action_inline_max_width = 0x7f07007e
com.android.rockchip.mediacodecnew:attr/layout_constraintCircle = 0x7f040281
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_content_padding = 0x7f070277
com.android.rockchip.mediacodecnew:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0d0107
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_dialog_background_inset = 0x7f070276
com.android.rockchip.mediacodecnew:color/mtrl_error = 0x7f0602aa
com.android.rockchip.mediacodecnew:attr/state_liftable = 0x7f0403f6
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0d00a5
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_end_padding = 0x7f0702a3
com.android.rockchip.mediacodecnew:dimen/m3_btn_icon_only_default_padding = 0x7f0700d3
com.android.rockchip.mediacodecnew:dimen/design_navigation_icon_padding = 0x7f070076
com.android.rockchip.mediacodecnew:dimen/m3_side_sheet_width = 0x7f0701e0
com.android.rockchip.mediacodecnew:attr/shapeAppearanceCornerMedium = 0x7f0403bc
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f12045f
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f120250
com.android.rockchip.mediacodecnew:attr/chipCornerRadius = 0x7f0400c1
com.android.rockchip.mediacodecnew:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08005f
com.android.rockchip.mediacodecnew:color/mtrl_card_view_ripple = 0x7f0602a2
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Light = 0x7f1202d0
com.android.rockchip.mediacodecnew:attr/textColorSearchUrl = 0x7f04045e
com.android.rockchip.mediacodecnew:attr/chipSpacing = 0x7f0400cb
com.android.rockchip.mediacodecnew:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f070101
com.android.rockchip.mediacodecnew:attr/popupTheme = 0x7f040386
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral4 = 0x7f0600a5
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ListView = 0x7f120324
com.android.rockchip.mediacodecnew:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f040466
com.android.rockchip.mediacodecnew:id/tag_on_receive_content_listener = 0x7f090239
com.android.rockchip.mediacodecnew:attr/placeholderTextAppearance = 0x7f040380
com.android.rockchip.mediacodecnew:attr/emojiCompatEnabled = 0x7f0401a3
com.android.rockchip.mediacodecnew:attr/percentY = 0x7f04037c
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ShapeableImageView = 0x7f120445
com.android.rockchip.mediacodecnew:string/material_timepicker_text_input_mode_description = 0x7f110055
com.android.rockchip.mediacodecnew:attr/percentX = 0x7f04037b
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0702c2
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f120184
com.android.rockchip.mediacodecnew:attr/chipIconVisible = 0x7f0400c8
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_surface_container_lowest = 0x7f0601d1
com.android.rockchip.mediacodecnew:attr/percentWidth = 0x7f04037a
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary20 = 0x7f0600d1
com.android.rockchip.mediacodecnew:id/actionUp = 0x7f090032
com.android.rockchip.mediacodecnew:attr/trackThickness = 0x7f0404b2
com.android.rockchip.mediacodecnew:color/abc_search_url_text_selected = 0x7f060010
com.android.rockchip.mediacodecnew:attr/actionBarTabTextStyle = 0x7f04000b
com.android.rockchip.mediacodecnew:attr/errorIconDrawable = 0x7f0401b4
com.android.rockchip.mediacodecnew:attr/indicatorInset = 0x7f04023d
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionMode = 0x7f1202fa
com.android.rockchip.mediacodecnew:attr/motionEasingEmphasizedInterpolator = 0x7f040336
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0d0085
com.android.rockchip.mediacodecnew:attr/panEnabled = 0x7f04036e
com.android.rockchip.mediacodecnew:styleable/KeyAttribute = 0x7f13003f
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0702a1
com.android.rockchip.mediacodecnew:attr/paddingTopNoTitle = 0x7f04036c
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.IconButton.Filled = 0x7f12035e
com.android.rockchip.mediacodecnew:dimen/abc_text_size_display_3_material = 0x7f070045
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f12029f
com.android.rockchip.mediacodecnew:dimen/material_clock_period_toggle_horizontal_gap = 0x7f070224
com.android.rockchip.mediacodecnew:styleable/ShapeAppearance = 0x7f13007c
com.android.rockchip.mediacodecnew:raw/halftone_lines_fragment = 0x7f100017
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_tertiary_container = 0x7f06015a
com.android.rockchip.mediacodecnew:color/mtrl_btn_text_color_disabled = 0x7f06029c
com.android.rockchip.mediacodecnew:attr/paddingStart = 0x7f04036a
com.android.rockchip.mediacodecnew:dimen/material_clock_hand_stroke_width = 0x7f070221
com.android.rockchip.mediacodecnew:attr/triggerId = 0x7f0404bb
com.android.rockchip.mediacodecnew:drawable/abc_btn_radio_material = 0x7f080032
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1201df
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f060186
com.android.rockchip.mediacodecnew:id/mtrl_picker_text_input_range_end = 0x7f090168
com.android.rockchip.mediacodecnew:attr/paddingBottomNoButtons = 0x7f040365
com.android.rockchip.mediacodecnew:attr/indicatorColor = 0x7f04023a
com.android.rockchip.mediacodecnew:attr/tabIconTint = 0x7f040414
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f120431
com.android.rockchip.mediacodecnew:drawable/abc_switch_thumb_material = 0x7f08006a
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f12044d
com.android.rockchip.mediacodecnew:style/Base.Animation.AppCompat.Dialog = 0x7f12000d
com.android.rockchip.mediacodecnew:attr/colorOnTertiaryFixedVariant = 0x7f040112
com.android.rockchip.mediacodecnew:attr/panelMenuListTheme = 0x7f040370
com.android.rockchip.mediacodecnew:attr/overlapAnchor = 0x7f040363
com.android.rockchip.mediacodecnew:id/decelerate = 0x7f0900b4
com.android.rockchip.mediacodecnew:attr/onTouchUp = 0x7f040362
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1202ca
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f120069
com.android.rockchip.mediacodecnew:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401c8
com.android.rockchip.mediacodecnew:macro/m3_comp_elevated_card_container_color = 0x7f0d002b
com.android.rockchip.mediacodecnew:attr/onShow = 0x7f040360
com.android.rockchip.mediacodecnew:attr/onHide = 0x7f04035d
com.android.rockchip.mediacodecnew:id/seekbar_color_mode = 0x7f0901de
com.android.rockchip.mediacodecnew:dimen/cardview_default_radius = 0x7f070054
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0d008f
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary60 = 0x7f0600d5
com.android.rockchip.mediacodecnew:id/btn_fast_backward = 0x7f09006f
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0701cc
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.DialogWhenLarge = 0x7f120219
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f070111
com.android.rockchip.mediacodecnew:id/et_share_name = 0x7f0900df
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600bb
com.android.rockchip.mediacodecnew:style/Base.DialogWindowTitle.AppCompat = 0x7f120011
com.android.rockchip.mediacodecnew:attr/onCross = 0x7f04035c
com.android.rockchip.mediacodecnew:attr/tabMaxWidth = 0x7f04041e
com.android.rockchip.mediacodecnew:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080072
com.android.rockchip.mediacodecnew:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1200a9
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f060182
com.android.rockchip.mediacodecnew:attr/numericModifiers = 0x7f04035a
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents.Light = 0x7f120096
com.android.rockchip.mediacodecnew:attr/flow_verticalBias = 0x7f0401fa
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f12026e
com.android.rockchip.mediacodecnew:attr/numFilters = 0x7f040358
com.android.rockchip.mediacodecnew:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700bf
com.android.rockchip.mediacodecnew:attr/statusBarForeground = 0x7f0403fa
com.android.rockchip.mediacodecnew:attr/allowStacking = 0x7f04002e
com.android.rockchip.mediacodecnew:drawable/abc_text_cursor_material = 0x7f08006e
com.android.rockchip.mediacodecnew:attr/nestedScrollViewStyle = 0x7f040356
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f12024d
com.android.rockchip.mediacodecnew:attr/layout_constraintWidth_default = 0x7f0402a2
com.android.rockchip.mediacodecnew:dimen/m3_badge_with_text_vertical_padding = 0x7f0700bb
com.android.rockchip.mediacodecnew:color/material_dynamic_primary20 = 0x7f06021f
com.android.rockchip.mediacodecnew:attr/navigationViewStyle = 0x7f040354
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Light = 0x7f12021b
com.android.rockchip.mediacodecnew:attr/colorSurfaceContainer = 0x7f040124
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1203b9
com.android.rockchip.mediacodecnew:color/m3_tabs_icon_color_secondary = 0x7f0601e3
com.android.rockchip.mediacodecnew:attr/navigationIconTint = 0x7f040351
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0701c6
com.android.rockchip.mediacodecnew:attr/itemPaddingBottom = 0x7f040252
com.android.rockchip.mediacodecnew:dimen/m3_btn_icon_btn_padding_right = 0x7f0700d2
com.android.rockchip.mediacodecnew:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0401e4
com.android.rockchip.mediacodecnew:attr/navigationContentDescription = 0x7f04034f
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1202d2
com.android.rockchip.mediacodecnew:dimen/abc_dialog_min_width_minor = 0x7f070023
com.android.rockchip.mediacodecnew:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.android.rockchip.mediacodecnew:attr/minHeight = 0x7f040315
com.android.rockchip.mediacodecnew:attr/motionProgress = 0x7f040348
com.android.rockchip.mediacodecnew:id/add = 0x7f090046
com.android.rockchip.mediacodecnew:drawable/mtrl_popupmenu_background_overlay = 0x7f0800cb
com.android.rockchip.mediacodecnew:attr/motionPathRotate = 0x7f040347
com.android.rockchip.mediacodecnew:attr/motionInterpolator = 0x7f040345
com.android.rockchip.mediacodecnew:attr/motionEffect_translationY = 0x7f040343
com.android.rockchip.mediacodecnew:macro/m3_comp_assist_chip_label_text_type = 0x7f0d0001
com.android.rockchip.mediacodecnew:attr/overlay = 0x7f040364
com.android.rockchip.mediacodecnew:attr/motionEffect_translationX = 0x7f040342
com.android.rockchip.mediacodecnew:id/tag_state_description = 0x7f09023c
com.android.rockchip.mediacodecnew:attr/voiceIcon = 0x7f0404cc
com.android.rockchip.mediacodecnew:attr/textAppearancePopupMenuHeader = 0x7f04044f
com.android.rockchip.mediacodecnew:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1200a8
com.android.rockchip.mediacodecnew:color/material_on_background_disabled = 0x7f06024e
com.android.rockchip.mediacodecnew:attr/fontProviderQuery = 0x7f040205
com.android.rockchip.mediacodecnew:attr/motionEasingStandardDecelerateInterpolator = 0x7f04033b
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1200c5
com.android.rockchip.mediacodecnew:color/mtrl_chip_surface_color = 0x7f0602a5
com.android.rockchip.mediacodecnew:attr/motionEasingStandardAccelerateInterpolator = 0x7f04033a
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1200d9
com.android.rockchip.mediacodecnew:attr/motionEasingStandard = 0x7f040339
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f070202
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0d015b
com.android.rockchip.mediacodecnew:attr/expandActivityOverflowButtonDrawable = 0x7f0401ba
com.android.rockchip.mediacodecnew:color/accent_material_light = 0x7f06001a
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Counter.Overflow = 0x7f1201cc
com.android.rockchip.mediacodecnew:attr/motionEasingLinear = 0x7f040337
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1202a4
com.android.rockchip.mediacodecnew:id/menu_icon = 0x7f090142
com.android.rockchip.mediacodecnew:attr/strokeWidth = 0x7f0403fd
com.android.rockchip.mediacodecnew:attr/paddingTopSystemWindowInsets = 0x7f04036d
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Filter = 0x7f120372
com.android.rockchip.mediacodecnew:id/titleDividerNoCustom = 0x7f090256
com.android.rockchip.mediacodecnew:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0702b6
com.android.rockchip.mediacodecnew:attr/drawableBottomCompat = 0x7f04018b
com.android.rockchip.mediacodecnew:attr/tabContentStart = 0x7f040412
com.android.rockchip.mediacodecnew:string/material_hour_suffix = 0x7f110044
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_drawer_container_width = 0x7f070140
com.android.rockchip.mediacodecnew:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700a7
com.android.rockchip.mediacodecnew:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f040334
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Error = 0x7f1201cd
com.android.rockchip.mediacodecnew:attr/tabIndicatorFullWidth = 0x7f04041a
com.android.rockchip.mediacodecnew:id/menu_scene_mode = 0x7f090146
com.android.rockchip.mediacodecnew:attr/motionDurationShort4 = 0x7f040330
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.FloatingActionButton = 0x7f12041d
com.android.rockchip.mediacodecnew:attr/motionDurationShort3 = 0x7f04032f
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f120304
com.android.rockchip.mediacodecnew:attr/colorTertiaryFixedDim = 0x7f040130
com.android.rockchip.mediacodecnew:attr/motionDurationShort1 = 0x7f04032d
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0d0017
com.android.rockchip.mediacodecnew:attr/actionBarStyle = 0x7f040008
com.android.rockchip.mediacodecnew:attr/motionDurationMedium3 = 0x7f04032b
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.LabelLarge = 0x7f1201ef
com.android.rockchip.mediacodecnew:attr/motionDurationMedium1 = 0x7f040329
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.TitleSmall = 0x7f1201f8
com.android.rockchip.mediacodecnew:string/mtrl_switch_thumb_path_morphing = 0x7f11008c
com.android.rockchip.mediacodecnew:attr/layout_constraintEnd_toEndOf = 0x7f040285
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1202cf
com.android.rockchip.mediacodecnew:attr/motionDurationLong2 = 0x7f040326
com.android.rockchip.mediacodecnew:attr/layout_constraintBaseline_toBottomOf = 0x7f04027c
com.android.rockchip.mediacodecnew:attr/motionDurationExtraLong3 = 0x7f040323
com.android.rockchip.mediacodecnew:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f12037c
com.android.rockchip.mediacodecnew:attr/layout_constraintTop_toBottomOf = 0x7f04029c
com.android.rockchip.mediacodecnew:attr/switchPadding = 0x7f04040e
com.android.rockchip.mediacodecnew:attr/motionDurationExtraLong1 = 0x7f040321
com.android.rockchip.mediacodecnew:attr/alphabeticModifiers = 0x7f040030
com.android.rockchip.mediacodecnew:attr/textInputOutlinedStyle = 0x7f040467
com.android.rockchip.mediacodecnew:id/menu_arrow = 0x7f090140
com.android.rockchip.mediacodecnew:dimen/mtrl_snackbar_margin = 0x7f0702eb
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f120294
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary50 = 0x7f06022f
com.android.rockchip.mediacodecnew:attr/itemTextAppearanceActiveBoldEnabled = 0x7f040261
com.android.rockchip.mediacodecnew:id/ifRoom = 0x7f090109
com.android.rockchip.mediacodecnew:attr/tabPaddingStart = 0x7f040424
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Light.Dialog = 0x7f120061
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_selected_icon_color = 0x7f0d00dc
com.android.rockchip.mediacodecnew:attr/layout_scrollInterpolator = 0x7f0402b6
com.android.rockchip.mediacodecnew:attr/motionDebug = 0x7f040320
com.android.rockchip.mediacodecnew:attr/tooltipText = 0x7f0404a4
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.NoActionBar = 0x7f120222
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_text_size = 0x7f0702cc
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0702cb
com.android.rockchip.mediacodecnew:attr/mock_showLabel = 0x7f04031f
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f12031a
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1201de
com.android.rockchip.mediacodecnew:id/mtrl_picker_fullscreen = 0x7f090162
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f0702f6
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f120454
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_corner_radius = 0x7f070252
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0d0154
com.android.rockchip.mediacodecnew:attr/mock_labelColor = 0x7f04031d
com.android.rockchip.mediacodecnew:attr/drawableEndCompat = 0x7f04018c
com.android.rockchip.mediacodecnew:attr/mock_labelBackgroundColor = 0x7f04031c
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0d00ac
com.android.rockchip.mediacodecnew:attr/mock_label = 0x7f04031b
com.android.rockchip.mediacodecnew:anim/mtrl_card_lowers_interpolator = 0x7f01002b
com.android.rockchip.mediacodecnew:dimen/material_emphasis_high_type = 0x7f07022d
com.android.rockchip.mediacodecnew:color/m3_navigation_bar_ripple_color_selector = 0x7f06008f
com.android.rockchip.mediacodecnew:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f120355
com.android.rockchip.mediacodecnew:attr/mock_diagonalsColor = 0x7f04031a
com.android.rockchip.mediacodecnew:attr/divider = 0x7f04017e
com.android.rockchip.mediacodecnew:attr/ensureMinTouchTargetSize = 0x7f0401af
com.android.rockchip.mediacodecnew:attr/materialTimePickerTheme = 0x7f040303
com.android.rockchip.mediacodecnew:color/mtrl_navigation_item_background_color = 0x7f0602b6
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0601a8
com.android.rockchip.mediacodecnew:id/btn_previous_video = 0x7f090077
com.android.rockchip.mediacodecnew:attr/minWidth = 0x7f040319
com.android.rockchip.mediacodecnew:color/material_personalized_color_control_activated = 0x7f06025b
com.android.rockchip.mediacodecnew:color/cardview_shadow_start_color = 0x7f06002d
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f120117
com.android.rockchip.mediacodecnew:attr/methodName = 0x7f040314
com.android.rockchip.mediacodecnew:string/mtrl_picker_date_header_title = 0x7f11006d
com.android.rockchip.mediacodecnew:attr/maxNumber = 0x7f04030c
com.android.rockchip.mediacodecnew:attr/maxImageSize = 0x7f04030a
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CheckedTextView = 0x7f12040d
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f070159
com.android.rockchip.mediacodecnew:attr/maxButtonHeight = 0x7f040307
com.android.rockchip.mediacodecnew:dimen/m3_alert_dialog_corner_size = 0x7f07009f
com.android.rockchip.mediacodecnew:animator/m3_btn_state_list_anim = 0x7f02000b
com.android.rockchip.mediacodecnew:attr/horizontalOffset = 0x7f040225
com.android.rockchip.mediacodecnew:drawable/mtrl_ic_arrow_drop_down = 0x7f0800c1
com.android.rockchip.mediacodecnew:attr/defaultMarginsEnabled = 0x7f040173
com.android.rockchip.mediacodecnew:attr/colorOnTertiary = 0x7f04010f
com.android.rockchip.mediacodecnew:attr/maxAcceleration = 0x7f040305
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f070112
com.android.rockchip.mediacodecnew:attr/startIconTint = 0x7f0403ee
com.android.rockchip.mediacodecnew:color/mtrl_fab_ripple_color = 0x7f0602ad
com.android.rockchip.mediacodecnew:color/m3_sys_color_primary_fixed_dim = 0x7f0601dd
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0d0034
com.android.rockchip.mediacodecnew:attr/materialThemeOverlay = 0x7f040301
com.android.rockchip.mediacodecnew:string/exposed_dropdown_menu_content_description = 0x7f11002a
com.android.rockchip.mediacodecnew:attr/layout_marginBaseline = 0x7f0402b2
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_picker_container_elevation = 0x7f07019a
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_icon_size = 0x7f070064
com.android.rockchip.mediacodecnew:attr/materialSearchViewToolbarStyle = 0x7f0402ff
com.android.rockchip.mediacodecnew:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0701bb
com.android.rockchip.mediacodecnew:attr/extendMotionSpec = 0x7f0401c5
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f070190
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1200ca
com.android.rockchip.mediacodecnew:attr/materialSearchViewToolbarHeight = 0x7f0402fe
com.android.rockchip.mediacodecnew:attr/chipIconEnabled = 0x7f0400c5
com.android.rockchip.mediacodecnew:attr/onPositiveCross = 0x7f04035f
com.android.rockchip.mediacodecnew:drawable/material_ic_calendar_black_24dp = 0x7f0800ab
com.android.rockchip.mediacodecnew:attr/materialSearchViewStyle = 0x7f0402fd
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0800cf
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0d0090
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_short3 = 0x7f0a001d
com.android.rockchip.mediacodecnew:dimen/abc_button_padding_vertical_material = 0x7f070015
com.android.rockchip.mediacodecnew:integer/bottom_sheet_slide_duration = 0x7f0a0003
com.android.rockchip.mediacodecnew:attr/materialSearchViewPrefixStyle = 0x7f0402fc
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Chip = 0x7f1201fe
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_primary_fixed = 0x7f0601ae
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionBar.TabText = 0x7f1202f5
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Body2 = 0x7f1201fb
com.android.rockchip.mediacodecnew:attr/materialSearchBarStyle = 0x7f0402fb
com.android.rockchip.mediacodecnew:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0702b9
com.android.rockchip.mediacodecnew:attr/materialIconButtonStyle = 0x7f0402fa
com.android.rockchip.mediacodecnew:attr/materialIconButtonOutlinedStyle = 0x7f0402f9
com.android.rockchip.mediacodecnew:raw/surface_fragment = 0x7f100029
com.android.rockchip.mediacodecnew:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f07015f
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.BottomNavigationView = 0x7f120100
com.android.rockchip.mediacodecnew:id/x_left = 0x7f09029e
com.android.rockchip.mediacodecnew:attr/motion_triggerOnCollision = 0x7f04034c
com.android.rockchip.mediacodecnew:attr/colorOnPrimaryFixed = 0x7f040105
com.android.rockchip.mediacodecnew:attr/materialIconButtonFilledTonalStyle = 0x7f0402f8
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral50 = 0x7f060208
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f08001a
com.android.rockchip.mediacodecnew:attr/materialDisplayDividerStyle = 0x7f0402f4
com.android.rockchip.mediacodecnew:attr/textOutlineThickness = 0x7f04046b
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f120127
com.android.rockchip.mediacodecnew:attr/materialClockStyle = 0x7f0402f3
com.android.rockchip.mediacodecnew:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.android.rockchip.mediacodecnew:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08003f
com.android.rockchip.mediacodecnew:color/tp_video_button_bg_normal = 0x7f0602e7
com.android.rockchip.mediacodecnew:attr/hideNavigationIcon = 0x7f04021c
com.android.rockchip.mediacodecnew:string/material_timepicker_minute = 0x7f110052
com.android.rockchip.mediacodecnew:attr/strokeColor = 0x7f0403fc
com.android.rockchip.mediacodecnew:dimen/abc_list_item_height_small_material = 0x7f070032
com.android.rockchip.mediacodecnew:attr/quantizeMotionInterpolator = 0x7f04038f
com.android.rockchip.mediacodecnew:animator/fragment_open_exit = 0x7f020008
com.android.rockchip.mediacodecnew:attr/materialCardViewStyle = 0x7f0402f1
com.android.rockchip.mediacodecnew:color/m3_switch_track_tint = 0x7f060149
com.android.rockchip.mediacodecnew:attr/materialCardViewOutlinedStyle = 0x7f0402f0
com.android.rockchip.mediacodecnew:styleable/CoordinatorLayout = 0x7f13002c
com.android.rockchip.mediacodecnew:attr/materialCardViewElevatedStyle = 0x7f0402ee
com.android.rockchip.mediacodecnew:attr/number = 0x7f040359
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f12041b
com.android.rockchip.mediacodecnew:attr/materialCalendarTheme = 0x7f0402ec
com.android.rockchip.mediacodecnew:id/radio_screen_stream = 0x7f0901ae
com.android.rockchip.mediacodecnew:dimen/m3_comp_divider_thickness = 0x7f070108
com.android.rockchip.mediacodecnew:style/Widget.Design.AppBarLayout = 0x7f12033d
com.android.rockchip.mediacodecnew:attr/materialCalendarStyle = 0x7f0402eb
com.android.rockchip.mediacodecnew:attr/materialCalendarMonthNavigationButton = 0x7f0402ea
com.android.rockchip.mediacodecnew:attr/itemStrokeWidth = 0x7f04025e
com.android.rockchip.mediacodecnew:attr/materialCalendarHeaderTitle = 0x7f0402e7
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1201da
com.android.rockchip.mediacodecnew:attr/windowMinWidthMajor = 0x7f0404db
com.android.rockchip.mediacodecnew:attr/gestureInsetBottomIgnored = 0x7f04020f
com.android.rockchip.mediacodecnew:attr/trackDecorationTintMode = 0x7f0404b0
com.android.rockchip.mediacodecnew:attr/materialCalendarHeaderDivider = 0x7f0402e4
com.android.rockchip.mediacodecnew:attr/materialCalendarDay = 0x7f0402df
com.android.rockchip.mediacodecnew:dimen/m3_extended_fab_min_height = 0x7f0701ac
com.android.rockchip.mediacodecnew:id/save_non_transition_alpha = 0x7f0901c7
com.android.rockchip.mediacodecnew:attr/materialButtonStyle = 0x7f0402dd
com.android.rockchip.mediacodecnew:attr/colorOutlineVariant = 0x7f040114
com.android.rockchip.mediacodecnew:color/material_harmonized_color_on_error = 0x7f06024c
com.android.rockchip.mediacodecnew:styleable/SideSheetBehavior_Layout = 0x7f13007e
com.android.rockchip.mediacodecnew:attr/colorOnSecondaryContainer = 0x7f040109
com.android.rockchip.mediacodecnew:attr/behavior_draggable = 0x7f04006c
com.android.rockchip.mediacodecnew:styleable/MaterialAlertDialog = 0x7f13004d
com.android.rockchip.mediacodecnew:attr/materialAlertDialogTitleIconStyle = 0x7f0402d9
com.android.rockchip.mediacodecnew:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f060095
com.android.rockchip.mediacodecnew:id/mtrl_calendar_frame = 0x7f090158
com.android.rockchip.mediacodecnew:attr/marginTopSystemWindowInsets = 0x7f0402d5
com.android.rockchip.mediacodecnew:color/dim_foreground_disabled_material_dark = 0x7f060054
com.android.rockchip.mediacodecnew:dimen/material_time_picker_minimum_screen_width = 0x7f07023d
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary10 = 0x7f06022a
com.android.rockchip.mediacodecnew:id/spinner_remote_path = 0x7f090215
com.android.rockchip.mediacodecnew:attr/autoSizeStepGranularity = 0x7f040046
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_pressed = 0x7f0800d0
com.android.rockchip.mediacodecnew:attr/startIconContentDescription = 0x7f0403ea
com.android.rockchip.mediacodecnew:id/material_hour_tv = 0x7f090132
com.android.rockchip.mediacodecnew:attr/itemMinHeight = 0x7f040250
com.android.rockchip.mediacodecnew:attr/marginHorizontal = 0x7f0402d2
com.android.rockchip.mediacodecnew:attr/logoScaleType = 0x7f0402d1
com.android.rockchip.mediacodecnew:attr/logoDescription = 0x7f0402d0
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.SeekBar.Discrete = 0x7f120332
com.android.rockchip.mediacodecnew:attr/logoAdjustViewBounds = 0x7f0402cf
com.android.rockchip.mediacodecnew:dimen/m3_btn_disabled_elevation = 0x7f0700cd
com.android.rockchip.mediacodecnew:layout/m3_alert_dialog_actions = 0x7f0c003a
com.android.rockchip.mediacodecnew:attr/logo = 0x7f0402ce
com.android.rockchip.mediacodecnew:attr/dividerHorizontal = 0x7f040180
com.android.rockchip.mediacodecnew:attr/contentInsetEndWithActions = 0x7f04013d
com.android.rockchip.mediacodecnew:attr/textEndPadding = 0x7f04045f
com.android.rockchip.mediacodecnew:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1203c8
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0600dd
com.android.rockchip.mediacodecnew:attr/customBoolean = 0x7f040164
com.android.rockchip.mediacodecnew:styleable/MaterialSwitch = 0x7f13005a
com.android.rockchip.mediacodecnew:attr/textBackground = 0x7f040458
com.android.rockchip.mediacodecnew:dimen/m3_card_elevation = 0x7f0700ea
com.android.rockchip.mediacodecnew:attr/simpleItemSelectedRippleColor = 0x7f0403d2
com.android.rockchip.mediacodecnew:attr/endIconMinSize = 0x7f0401a8
com.android.rockchip.mediacodecnew:attr/fabAlignmentMode = 0x7f0401cd
com.android.rockchip.mediacodecnew:attr/listPreferredItemHeightSmall = 0x7f0402c9
com.android.rockchip.mediacodecnew:color/switch_thumb_normal_material_dark = 0x7f0602e2
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error60 = 0x7f0600ef
com.android.rockchip.mediacodecnew:attr/listPreferredItemHeightLarge = 0x7f0402c8
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActionButton = 0x7f1200c8
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_rail_container_elevation = 0x7f070149
com.android.rockchip.mediacodecnew:attr/cornerSizeBottomLeft = 0x7f040155
com.android.rockchip.mediacodecnew:attr/state_with_icon = 0x7f0403f8
com.android.rockchip.mediacodecnew:attr/listPreferredItemHeight = 0x7f0402c7
com.android.rockchip.mediacodecnew:styleable/MaterialCalendar = 0x7f130052
com.android.rockchip.mediacodecnew:id/controlPanel = 0x7f0900a8
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadlineLarge = 0x7f040443
com.android.rockchip.mediacodecnew:attr/chipIcon = 0x7f0400c4
com.android.rockchip.mediacodecnew:drawable/abc_ratingbar_indicator_material = 0x7f08005b
com.android.rockchip.mediacodecnew:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700a4
com.android.rockchip.mediacodecnew:attr/listPopupWindowStyle = 0x7f0402c6
com.android.rockchip.mediacodecnew:integer/m3_badge_max_number = 0x7f0a0009
com.android.rockchip.mediacodecnew:attr/labelVisibilityMode = 0x7f04026b
com.android.rockchip.mediacodecnew:attr/layout_constraintHorizontal_weight = 0x7f040291
com.android.rockchip.mediacodecnew:styleable/Insets = 0x7f13003e
com.android.rockchip.mediacodecnew:attr/listChoiceBackgroundIndicator = 0x7f0402bf
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0d013a
com.android.rockchip.mediacodecnew:attr/motionDurationMedium2 = 0x7f04032a
com.android.rockchip.mediacodecnew:attr/contentInsetLeft = 0x7f04013e
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary20 = 0x7f060239
com.android.rockchip.mediacodecnew:attr/liftOnScroll = 0x7f0402b8
com.android.rockchip.mediacodecnew:attr/bottomAppBarStyle = 0x7f04007b
com.android.rockchip.mediacodecnew:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0401e7
com.android.rockchip.mediacodecnew:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f07012f
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary60 = 0x7f060121
com.android.rockchip.mediacodecnew:attr/layout_wrapBehaviorInParent = 0x7f0402b7
com.android.rockchip.mediacodecnew:color/mtrl_btn_text_btn_bg_color_selector = 0x7f06029a
com.android.rockchip.mediacodecnew:dimen/abc_text_size_menu_material = 0x7f07004b
com.android.rockchip.mediacodecnew:attr/scrimVisibleHeightTrigger = 0x7f0403ad
com.android.rockchip.mediacodecnew:layout/abc_search_dropdown_item_icons_2line = 0x7f0c0018
com.android.rockchip.mediacodecnew:attr/layout_insetEdge = 0x7f0402b0
com.android.rockchip.mediacodecnew:attr/layout_goneMarginStart = 0x7f0402ae
com.android.rockchip.mediacodecnew:id/wrap_content_constrained = 0x7f09029d
com.android.rockchip.mediacodecnew:id/menu_horizontal_flip = 0x7f090141
com.android.rockchip.mediacodecnew:attr/background = 0x7f04004a
com.android.rockchip.mediacodecnew:drawable/ic_launcher_background = 0x7f08008e
com.android.rockchip.mediacodecnew:attr/errorAccessibilityLiveRegion = 0x7f0401b1
com.android.rockchip.mediacodecnew:color/design_default_color_on_surface = 0x7f060043
com.android.rockchip.mediacodecnew:attr/maxVelocity = 0x7f04030e
com.android.rockchip.mediacodecnew:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.android.rockchip.mediacodecnew:attr/listMenuViewStyle = 0x7f0402c5
com.android.rockchip.mediacodecnew:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700a3
com.android.rockchip.mediacodecnew:attr/defaultDuration = 0x7f040172
com.android.rockchip.mediacodecnew:attr/actionModeCloseContentDescription = 0x7f040015
com.android.rockchip.mediacodecnew:attr/textAppearanceButton = 0x7f040438
com.android.rockchip.mediacodecnew:attr/layout_goneMarginRight = 0x7f0402ad
com.android.rockchip.mediacodecnew:attr/materialAlertDialogTheme = 0x7f0402d8
com.android.rockchip.mediacodecnew:id/browserButton = 0x7f090063
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary50 = 0x7f0600c7
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f120251
com.android.rockchip.mediacodecnew:dimen/m3_searchbar_text_size = 0x7f0701d9
com.android.rockchip.mediacodecnew:attr/shapeAppearanceCornerExtraSmall = 0x7f0403ba
com.android.rockchip.mediacodecnew:attr/layout_goneMarginLeft = 0x7f0402ac
com.android.rockchip.mediacodecnew:attr/subheaderInsetStart = 0x7f040401
com.android.rockchip.mediacodecnew:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.android.rockchip.mediacodecnew:raw/pixelated_fragment = 0x7f10001e
com.android.rockchip.mediacodecnew:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0402d7
com.android.rockchip.mediacodecnew:attr/errorAccessibilityLabel = 0x7f0401b0
com.android.rockchip.mediacodecnew:attr/layout_goneMarginBottom = 0x7f0402aa
com.android.rockchip.mediacodecnew:dimen/mtrl_low_ripple_focused_alpha = 0x7f0702b8
com.android.rockchip.mediacodecnew:attr/nestedScrollFlags = 0x7f040355
com.android.rockchip.mediacodecnew:attr/layout_goneMarginTop = 0x7f0402af
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_bright = 0x7f060278
com.android.rockchip.mediacodecnew:id/disableIntraAutoTransition = 0x7f0900c4
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_container_highest = 0x7f06027b
com.android.rockchip.mediacodecnew:attr/layout_constraintWidth_percent = 0x7f0402a5
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_secondary = 0x7f06019b
com.android.rockchip.mediacodecnew:attr/layout_constraintTop_toTopOf = 0x7f04029d
com.android.rockchip.mediacodecnew:attr/layout_constraintStart_toStartOf = 0x7f040299
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0d00bf
com.android.rockchip.mediacodecnew:attr/badgeWithTextWidth = 0x7f040065
com.android.rockchip.mediacodecnew:string/mtrl_picker_invalid_format_use = 0x7f110073
com.android.rockchip.mediacodecnew:dimen/m3_chip_checked_hovered_translation_z = 0x7f0700f3
com.android.rockchip.mediacodecnew:attr/layout_dodgeInsetEdges = 0x7f0402a6
com.android.rockchip.mediacodecnew:attr/layout_constraintRight_creator = 0x7f040295
com.android.rockchip.mediacodecnew:attr/layout_constraintLeft_toLeftOf = 0x7f040293
com.android.rockchip.mediacodecnew:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005
com.android.rockchip.mediacodecnew:dimen/mtrl_fab_min_touch_target = 0x7f0702b0
com.android.rockchip.mediacodecnew:attr/tabIndicator = 0x7f040416
com.android.rockchip.mediacodecnew:attr/itemTextAppearanceActive = 0x7f040260
com.android.rockchip.mediacodecnew:attr/layout_constraintHorizontal_chainStyle = 0x7f040290
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f070288
com.android.rockchip.mediacodecnew:color/material_on_surface_disabled = 0x7f060254
com.android.rockchip.mediacodecnew:attr/layout_constraintHeight_min = 0x7f04028d
com.android.rockchip.mediacodecnew:color/m3_navigation_item_background_color = 0x7f060090
com.android.rockchip.mediacodecnew:attr/carousel_touchUp_dampeningFactor = 0x7f0400ad
com.android.rockchip.mediacodecnew:drawable/notification_icon_background = 0x7f0800e1
com.android.rockchip.mediacodecnew:color/material_personalized__highlighted_text_inverse = 0x7f060259
com.android.rockchip.mediacodecnew:attr/indeterminateProgressStyle = 0x7f040239
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0d015d
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_medium4 = 0x7f0a001a
com.android.rockchip.mediacodecnew:id/scrollIndicatorDown = 0x7f0901cd
com.android.rockchip.mediacodecnew:attr/layout_constraintCircleAngle = 0x7f040282
com.android.rockchip.mediacodecnew:attr/thumbTextPadding = 0x7f04047e
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary99 = 0x7f0600cd
com.android.rockchip.mediacodecnew:attr/layout_constraintBottom_toTopOf = 0x7f040280
com.android.rockchip.mediacodecnew:style/Widget.Design.CollapsingToolbar = 0x7f120340
com.android.rockchip.mediacodecnew:attr/trackColorActive = 0x7f0404ab
com.android.rockchip.mediacodecnew:attr/layout_constraintBottom_creator = 0x7f04027e
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_standard_decelerate = 0x7f11003f
com.android.rockchip.mediacodecnew:attr/layout_constrainedWidth = 0x7f040279
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f120046
com.android.rockchip.mediacodecnew:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
com.android.rockchip.mediacodecnew:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f120050
com.android.rockchip.mediacodecnew:drawable/tooltip_frame_light = 0x7f0800e8
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1202c2
com.android.rockchip.mediacodecnew:attr/fastScrollHorizontalTrackDrawable = 0x7f0401d8
com.android.rockchip.mediacodecnew:attr/layout_constrainedHeight = 0x7f040278
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral12 = 0x7f06009f
com.android.rockchip.mediacodecnew:attr/cardCornerRadius = 0x7f04009e
com.android.rockchip.mediacodecnew:attr/layout_behavior = 0x7f040275
com.android.rockchip.mediacodecnew:attr/layout_anchor = 0x7f040273
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_primary = 0x7f06015d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary100 = 0x7f06011c
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Display2 = 0x7f12001c
com.android.rockchip.mediacodecnew:layout/abc_activity_chooser_view_list_item = 0x7f0c0007
com.android.rockchip.mediacodecnew:attr/layoutManager = 0x7f040272
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_bottom_padding = 0x7f07026d
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_tertiary = 0x7f060159
com.android.rockchip.mediacodecnew:attr/layout = 0x7f04026f
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_medium2 = 0x7f0a0018
com.android.rockchip.mediacodecnew:color/material_on_surface_stroke = 0x7f060257
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1201b7
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f12018c
com.android.rockchip.mediacodecnew:attr/windowFixedHeightMinor = 0x7f0404d8
com.android.rockchip.mediacodecnew:integer/m3_btn_anim_duration_ms = 0x7f0a000b
com.android.rockchip.mediacodecnew:id/radio_wb_manual = 0x7f0901b0
com.android.rockchip.mediacodecnew:attr/badgeWidePadding = 0x7f04005f
com.android.rockchip.mediacodecnew:attr/lStar = 0x7f040268
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0d0100
com.android.rockchip.mediacodecnew:id/speed_0_5 = 0x7f09020e
com.android.rockchip.mediacodecnew:attr/checkMarkTintMode = 0x7f0400b3
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f12030f
com.android.rockchip.mediacodecnew:attr/keylines = 0x7f040267
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f060184
com.android.rockchip.mediacodecnew:attr/keyboardIcon = 0x7f040266
com.android.rockchip.mediacodecnew:dimen/material_emphasis_disabled_background = 0x7f07022c
com.android.rockchip.mediacodecnew:attr/itemVerticalPadding = 0x7f040264
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_on_primary_container = 0x7f060154
com.android.rockchip.mediacodecnew:attr/itemTextAppearanceInactive = 0x7f040262
com.android.rockchip.mediacodecnew:id/mtrl_calendar_main_pane = 0x7f090159
com.android.rockchip.mediacodecnew:attr/layout_scrollEffect = 0x7f0402b4
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_state_description_indeterminate = 0x7f110060
com.android.rockchip.mediacodecnew:attr/itemStrokeColor = 0x7f04025d
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0d0020
com.android.rockchip.mediacodecnew:id/center = 0x7f09008d
com.android.rockchip.mediacodecnew:attr/actionBarSplitStyle = 0x7f040007
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary100 = 0x7f0600d0
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0d0018
com.android.rockchip.mediacodecnew:attr/appBarLayoutStyle = 0x7f040037
com.android.rockchip.mediacodecnew:id/material_clock_face = 0x7f09012b
com.android.rockchip.mediacodecnew:id/match_parent = 0x7f090128
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral87 = 0x7f0600ac
com.android.rockchip.mediacodecnew:styleable/ViewTransition = 0x7f13009b
com.android.rockchip.mediacodecnew:attr/carousel_previousState = 0x7f0400ab
com.android.rockchip.mediacodecnew:attr/textInputStyle = 0x7f040468
com.android.rockchip.mediacodecnew:dimen/m3_btn_dialog_btn_min_width = 0x7f0700cb
com.android.rockchip.mediacodecnew:attr/content = 0x7f04013a
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ListView = 0x7f1200e8
com.android.rockchip.mediacodecnew:attr/itemShapeInsetStart = 0x7f04025a
com.android.rockchip.mediacodecnew:attr/shapeAppearanceOverlay = 0x7f0403c0
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_tertiary_container = 0x7f0601c4
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary99 = 0x7f060126
com.android.rockchip.mediacodecnew:attr/badgeShapeAppearanceOverlay = 0x7f040059
com.android.rockchip.mediacodecnew:attr/commitIcon = 0x7f040131
com.android.rockchip.mediacodecnew:attr/itemShapeInsetBottom = 0x7f040258
com.android.rockchip.mediacodecnew:drawable/tp_video_play_button_background = 0x7f0800ef
com.android.rockchip.mediacodecnew:dimen/material_input_text_to_prefix_suffix_padding = 0x7f070238
com.android.rockchip.mediacodecnew:attr/motionDurationLong3 = 0x7f040327
com.android.rockchip.mediacodecnew:attr/customPixelDimension = 0x7f04016b
com.android.rockchip.mediacodecnew:attr/itemShapeFillColor = 0x7f040257
com.android.rockchip.mediacodecnew:attr/suffixTextAppearance = 0x7f04040a
com.android.rockchip.mediacodecnew:attr/cornerSize = 0x7f040154
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.HeadlineMedium = 0x7f1201ed
com.android.rockchip.mediacodecnew:dimen/m3_bottomappbar_fab_end_margin = 0x7f0700c8
com.android.rockchip.mediacodecnew:attr/itemShapeAppearance = 0x7f040255
com.android.rockchip.mediacodecnew:attr/colorOnPrimarySurface = 0x7f040107
com.android.rockchip.mediacodecnew:id/scrollIndicatorUp = 0x7f0901ce
com.android.rockchip.mediacodecnew:attr/itemPaddingTop = 0x7f040253
com.android.rockchip.mediacodecnew:dimen/notification_right_side_padding_top = 0x7f07030d
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral12 = 0x7f0600f8
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Assist.Elevated = 0x7f120371
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_background = 0x7f060170
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary80 = 0x7f060123
com.android.rockchip.mediacodecnew:attr/rippleColor = 0x7f0403a5
com.android.rockchip.mediacodecnew:attr/materialCalendarYearNavigationButton = 0x7f0402ed
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1201ca
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_text_size = 0x7f070268
com.android.rockchip.mediacodecnew:dimen/abc_list_item_height_large_material = 0x7f070030
com.android.rockchip.mediacodecnew:attr/itemIconTint = 0x7f04024e
com.android.rockchip.mediacodecnew:attr/itemHorizontalPadding = 0x7f04024a
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_radius = 0x7f0702d1
com.android.rockchip.mediacodecnew:attr/toolbarSurfaceStyle = 0x7f0404a0
com.android.rockchip.mediacodecnew:dimen/m3_comp_filter_chip_container_height = 0x7f07012c
com.android.rockchip.mediacodecnew:drawable/$avd_hide_password__2 = 0x7f080002
com.android.rockchip.mediacodecnew:attr/drawerLayoutCornerSize = 0x7f040195
com.android.rockchip.mediacodecnew:style/ShapeAppearance.Material3.Corner.Full = 0x7f120172
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f070195
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f12031c
com.android.rockchip.mediacodecnew:id/menu_title = 0x7f090148
com.android.rockchip.mediacodecnew:attr/iconEndPadding = 0x7f040229
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_primary_container = 0x7f06019a
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f12010b
com.android.rockchip.mediacodecnew:attr/isMaterial3Theme = 0x7f040245
com.android.rockchip.mediacodecnew:id/text2 = 0x7f090243
com.android.rockchip.mediacodecnew:attr/color = 0x7f0400f4
com.android.rockchip.mediacodecnew:anim/m3_side_sheet_enter_from_right = 0x7f010026
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_small_container_height = 0x7f070120
com.android.rockchip.mediacodecnew:string/mtrl_picker_invalid_format_example = 0x7f110072
com.android.rockchip.mediacodecnew:string/mtrl_checkbox_button_icon_path_name = 0x7f11005a
com.android.rockchip.mediacodecnew:attr/colorSurfaceContainerLowest = 0x7f040128
com.android.rockchip.mediacodecnew:attr/imagePanX = 0x7f040234
com.android.rockchip.mediacodecnew:color/material_blue_grey_950 = 0x7f0601fd
com.android.rockchip.mediacodecnew:attr/ifTagNotSet = 0x7f040231
com.android.rockchip.mediacodecnew:attr/autoTransition = 0x7f040048
com.android.rockchip.mediacodecnew:attr/iconStartPadding = 0x7f04022d
com.android.rockchip.mediacodecnew:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f110086
com.android.rockchip.mediacodecnew:attr/hoveredFocusedTranslationZ = 0x7f040227
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f120168
com.android.rockchip.mediacodecnew:attr/layout_constraintWidth_max = 0x7f0402a3
com.android.rockchip.mediacodecnew:attr/flow_horizontalGap = 0x7f0401f1
com.android.rockchip.mediacodecnew:attr/duration = 0x7f04019a
com.android.rockchip.mediacodecnew:attr/shapeAppearanceCornerSmall = 0x7f0403bd
com.android.rockchip.mediacodecnew:integer/mtrl_calendar_header_orientation = 0x7f0a0030
com.android.rockchip.mediacodecnew:attr/isMaterial3DynamicColorApplied = 0x7f040244
com.android.rockchip.mediacodecnew:attr/colorSurfaceInverse = 0x7f04012a
com.android.rockchip.mediacodecnew:color/material_dynamic_primary99 = 0x7f060228
com.android.rockchip.mediacodecnew:attr/chipIconSize = 0x7f0400c6
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.BottomAppBar = 0x7f1203f8
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1202dd
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_primary_container_color = 0x7f0d002d
com.android.rockchip.mediacodecnew:attr/hintTextAppearance = 0x7f040221
com.android.rockchip.mediacodecnew:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f120181
com.android.rockchip.mediacodecnew:attr/thumbStrokeColor = 0x7f04047c
com.android.rockchip.mediacodecnew:attr/scrimAnimationDuration = 0x7f0403ab
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f1202d8
com.android.rockchip.mediacodecnew:attr/hideOnContentScroll = 0x7f04021d
com.android.rockchip.mediacodecnew:macro/m3_comp_text_button_label_text_type = 0x7f0d0146
com.android.rockchip.mediacodecnew:id/supportScrollUp = 0x7f09022d
com.android.rockchip.mediacodecnew:attr/showAsAction = 0x7f0403c6
com.android.rockchip.mediacodecnew:attr/helperText = 0x7f040216
com.android.rockchip.mediacodecnew:attr/snackbarButtonStyle = 0x7f0403d9
com.android.rockchip.mediacodecnew:attr/fabAnimationMode = 0x7f0401d0
com.android.rockchip.mediacodecnew:attr/listPreferredItemPaddingStart = 0x7f0402cd
com.android.rockchip.mediacodecnew:style/Platform.MaterialComponents.Light = 0x7f12013c
com.android.rockchip.mediacodecnew:dimen/design_snackbar_background_corner_radius = 0x7f070080
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary70 = 0x7f060231
com.android.rockchip.mediacodecnew:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.android.rockchip.mediacodecnew:macro/m3_comp_dialog_container_color = 0x7f0d0023
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0601b0
com.android.rockchip.mediacodecnew:attr/haloColor = 0x7f040212
com.android.rockchip.mediacodecnew:attr/imageZoom = 0x7f040237
com.android.rockchip.mediacodecnew:attr/buttonTint = 0x7f04009b
com.android.rockchip.mediacodecnew:style/ThemeOverlay.AppCompat.DayNight = 0x7f120278
com.android.rockchip.mediacodecnew:attr/alertDialogCenterButtons = 0x7f04002b
com.android.rockchip.mediacodecnew:attr/buttonBarPositiveButtonStyle = 0x7f040090
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Button.Small = 0x7f1200d3
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f12005c
com.android.rockchip.mediacodecnew:color/m3_ref_palette_black = 0x7f06009b
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f12016e
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Spinner = 0x7f1200f7
com.android.rockchip.mediacodecnew:attr/foregroundInsidePadding = 0x7f04020c
com.android.rockchip.mediacodecnew:color/design_fab_stroke_top_outer_color = 0x7f060051
com.android.rockchip.mediacodecnew:id/material_value_index = 0x7f09013c
com.android.rockchip.mediacodecnew:attr/rotationCenterId = 0x7f0403a6
com.android.rockchip.mediacodecnew:dimen/mtrl_high_ripple_focused_alpha = 0x7f0702b4
com.android.rockchip.mediacodecnew:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f070247
com.android.rockchip.mediacodecnew:attr/chipSurfaceColor = 0x7f0400d3
com.android.rockchip.mediacodecnew:attr/paddingBottomSystemWindowInsets = 0x7f040366
com.android.rockchip.mediacodecnew:attr/fontVariationSettings = 0x7f040208
com.android.rockchip.mediacodecnew:attr/state_collapsible = 0x7f0403f2
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.CardView = 0x7f120101
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f070122
com.android.rockchip.mediacodecnew:attr/itemIconPadding = 0x7f04024c
com.android.rockchip.mediacodecnew:drawable/abc_ic_menu_overflow_material = 0x7f080045
com.android.rockchip.mediacodecnew:style/Widget.Material3.Snackbar = 0x7f1203d5
com.android.rockchip.mediacodecnew:color/white = 0x7f0602f6
com.android.rockchip.mediacodecnew:id/tv_dark_enhance_value = 0x7f090270
com.android.rockchip.mediacodecnew:attr/tickRadiusInactive = 0x7f040488
com.android.rockchip.mediacodecnew:attr/radioButtonStyle = 0x7f040395
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f070156
com.android.rockchip.mediacodecnew:attr/layout_constraintHeight_percent = 0x7f04028e
com.android.rockchip.mediacodecnew:attr/flow_lastVerticalStyle = 0x7f0401f6
com.android.rockchip.mediacodecnew:color/abc_hint_foreground_material_light = 0x7f060008
com.android.rockchip.mediacodecnew:attr/flow_horizontalBias = 0x7f0401f0
com.android.rockchip.mediacodecnew:color/m3_default_color_secondary_text = 0x7f060079
com.android.rockchip.mediacodecnew:animator/mtrl_btn_state_list_anim = 0x7f020015
com.android.rockchip.mediacodecnew:dimen/m3_extended_fab_top_padding = 0x7f0701ae
com.android.rockchip.mediacodecnew:attr/flow_firstHorizontalBias = 0x7f0401eb
com.android.rockchip.mediacodecnew:anim/tp_speed_dialog_exit = 0x7f01002d
com.android.rockchip.mediacodecnew:color/mtrl_tabs_ripple_color = 0x7f0602c7
com.android.rockchip.mediacodecnew:attr/cardBackgroundColor = 0x7f04009d
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_surface = 0x7f06017f
com.android.rockchip.mediacodecnew:animator/fragment_fade_enter = 0x7f020005
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.Chip = 0x7f120102
com.android.rockchip.mediacodecnew:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401e0
com.android.rockchip.mediacodecnew:drawable/$avd_show_password__2 = 0x7f080005
com.android.rockchip.mediacodecnew:attr/badgeTextAppearance = 0x7f04005c
com.android.rockchip.mediacodecnew:attr/itemShapeAppearanceOverlay = 0x7f040256
com.android.rockchip.mediacodecnew:integer/m3_sys_shape_corner_small_corner_family = 0x7f0a0025
com.android.rockchip.mediacodecnew:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401df
com.android.rockchip.mediacodecnew:attr/colorOnPrimaryFixedVariant = 0x7f040106
com.android.rockchip.mediacodecnew:attr/isMaterialTheme = 0x7f040246
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Light = 0x7f1202a7
com.android.rockchip.mediacodecnew:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080059
com.android.rockchip.mediacodecnew:layout/notification_template_part_time = 0x7f0c0075
com.android.rockchip.mediacodecnew:attr/contentPaddingEnd = 0x7f040144
com.android.rockchip.mediacodecnew:attr/titleCentered = 0x7f04048e
com.android.rockchip.mediacodecnew:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401dd
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f120398
com.android.rockchip.mediacodecnew:attr/fastScrollVerticalTrackDrawable = 0x7f0401da
com.android.rockchip.mediacodecnew:attr/blendSrc = 0x7f040076
com.android.rockchip.mediacodecnew:dimen/m3_navigation_item_shape_inset_end = 0x7f0701bc
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ActionBar.TabView = 0x7f1202f6
com.android.rockchip.mediacodecnew:raw/blur_fragment = 0x7f100005
com.android.rockchip.mediacodecnew:color/m3_timepicker_display_text_color = 0x7f0601f6
com.android.rockchip.mediacodecnew:layout/custom_dialog = 0x7f0c001e
com.android.rockchip.mediacodecnew:attr/textColorAlertDialogListItem = 0x7f04045d
com.android.rockchip.mediacodecnew:style/Theme.Material3.DynamicColors.Dark = 0x7f120239
com.android.rockchip.mediacodecnew:style/Base.Animation.AppCompat.Tooltip = 0x7f12000f
com.android.rockchip.mediacodecnew:layout/abc_expanded_menu_layout = 0x7f0c000d
com.android.rockchip.mediacodecnew:attr/sideSheetModalStyle = 0x7f0403cf
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_max_width = 0x7f07025d
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Design.TextInputEditText = 0x7f12027d
com.android.rockchip.mediacodecnew:macro/m3_comp_slider_active_track_color = 0x7f0d010c
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f070115
com.android.rockchip.mediacodecnew:attr/itemIconSize = 0x7f04024d
com.android.rockchip.mediacodecnew:string/side_sheet_accessibility_pane_title = 0x7f11009d
com.android.rockchip.mediacodecnew:attr/layout_constraintHeight_max = 0x7f04028c
com.android.rockchip.mediacodecnew:color/mtrl_fab_bg_color_selector = 0x7f0602ab
com.android.rockchip.mediacodecnew:color/design_dark_default_color_secondary_variant = 0x7f06003b
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_track_side_padding = 0x7f0702e6
com.android.rockchip.mediacodecnew:attr/motionDurationLong1 = 0x7f040325
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Light.Dialog = 0x7f120054
com.android.rockchip.mediacodecnew:attr/fastScrollHorizontalThumbDrawable = 0x7f0401d7
com.android.rockchip.mediacodecnew:attr/dividerPadding = 0x7f040183
com.android.rockchip.mediacodecnew:integer/material_motion_duration_short_2 = 0x7f0a002b
com.android.rockchip.mediacodecnew:attr/layout_constraintRight_toRightOf = 0x7f040297
com.android.rockchip.mediacodecnew:attr/maxScale = 0x7f04030d
com.android.rockchip.mediacodecnew:attr/indicatorSize = 0x7f04023e
com.android.rockchip.mediacodecnew:attr/fabCradleRoundedCornerRadius = 0x7f0401d2
com.android.rockchip.mediacodecnew:style/Base.V22.Theme.AppCompat.Light = 0x7f1200af
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f120029
com.android.rockchip.mediacodecnew:attr/extraMultilineHeightEnabled = 0x7f0401cc
com.android.rockchip.mediacodecnew:styleable/CoordinatorLayout_Layout = 0x7f13002d
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_path = 0x7f0a001f
com.android.rockchip.mediacodecnew:id/skipped = 0x7f090205
com.android.rockchip.mediacodecnew:attr/textAppearanceDisplayLarge = 0x7f04043a
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_day_horizontal_padding = 0x7f070271
com.android.rockchip.mediacodecnew:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0701c1
com.android.rockchip.mediacodecnew:attr/extendedFloatingActionButtonStyle = 0x7f0401c9
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.RatingBar = 0x7f12032c
com.android.rockchip.mediacodecnew:color/m3_dynamic_dark_hint_foreground = 0x7f06007d
com.android.rockchip.mediacodecnew:attr/colorSurfaceContainerHigh = 0x7f040125
com.android.rockchip.mediacodecnew:id/tv_mirror_value = 0x7f09027b
com.android.rockchip.mediacodecnew:id/btn_biological_scene = 0x7f090065
com.android.rockchip.mediacodecnew:attr/flow_firstHorizontalStyle = 0x7f0401ec
com.android.rockchip.mediacodecnew:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f070230
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f12003b
com.android.rockchip.mediacodecnew:attr/displayOptions = 0x7f04017d
com.android.rockchip.mediacodecnew:attr/contentInsetEnd = 0x7f04013c
com.android.rockchip.mediacodecnew:attr/tickColorActive = 0x7f040482
com.android.rockchip.mediacodecnew:attr/expandedTitleMarginEnd = 0x7f0401c0
com.android.rockchip.mediacodecnew:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f07012e
com.android.rockchip.mediacodecnew:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0800b1
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f120158
com.android.rockchip.mediacodecnew:attr/switchTextAppearance = 0x7f040410
com.android.rockchip.mediacodecnew:attr/haloRadius = 0x7f040213
com.android.rockchip.mediacodecnew:attr/expandedTitleMarginBottom = 0x7f0401bf
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral24 = 0x7f0600a3
com.android.rockchip.mediacodecnew:attr/attributeName = 0x7f04003d
com.android.rockchip.mediacodecnew:attr/expandedHintEnabled = 0x7f0401bc
com.android.rockchip.mediacodecnew:attr/errorTextAppearance = 0x7f0401b8
com.android.rockchip.mediacodecnew:attr/layout_constraintBaseline_toBaselineOf = 0x7f04027b
com.android.rockchip.mediacodecnew:attr/layout_collapseMode = 0x7f040276
com.android.rockchip.mediacodecnew:attr/closeIconVisible = 0x7f0400e7
com.android.rockchip.mediacodecnew:id/activity_chooser_view_content = 0x7f090045
com.android.rockchip.mediacodecnew:dimen/tooltip_corner_radius = 0x7f070313
com.android.rockchip.mediacodecnew:attr/errorIconTintMode = 0x7f0401b6
com.android.rockchip.mediacodecnew:color/m3_text_button_background_color_selector = 0x7f0601e8
com.android.rockchip.mediacodecnew:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.android.rockchip.mediacodecnew:id/rtsp_url_text = 0x7f0901c6
com.android.rockchip.mediacodecnew:attr/materialCalendarHeaderLayout = 0x7f0402e5
com.android.rockchip.mediacodecnew:attr/thumbIconTintMode = 0x7f04047a
com.android.rockchip.mediacodecnew:drawable/ic_settings_white_24 = 0x7f08009a
com.android.rockchip.mediacodecnew:id/direct = 0x7f0900c2
com.android.rockchip.mediacodecnew:attr/scrimBackground = 0x7f0403ac
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f12015d
com.android.rockchip.mediacodecnew:attr/enforceTextAppearance = 0x7f0401ae
com.android.rockchip.mediacodecnew:attr/deltaPolarRadius = 0x7f040178
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant80 = 0x7f060218
com.android.rockchip.mediacodecnew:attr/enforceMaterialTheme = 0x7f0401ad
com.android.rockchip.mediacodecnew:attr/badgeVerticalPadding = 0x7f04005e
com.android.rockchip.mediacodecnew:dimen/design_bottom_navigation_active_item_min_width = 0x7f070060
com.android.rockchip.mediacodecnew:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.android.rockchip.mediacodecnew:attr/isLightTheme = 0x7f040243
com.android.rockchip.mediacodecnew:macro/m3_comp_text_button_label_text_color = 0x7f0d0145
com.android.rockchip.mediacodecnew:attr/checkedChip = 0x7f0400b6
com.android.rockchip.mediacodecnew:attr/suggestionRowLayout = 0x7f04040c
com.android.rockchip.mediacodecnew:attr/endIconTint = 0x7f0401ab
com.android.rockchip.mediacodecnew:animator/m3_appbar_state_list_animator = 0x7f020009
com.android.rockchip.mediacodecnew:attr/chipGroupStyle = 0x7f0400c3
com.android.rockchip.mediacodecnew:attr/errorTextColor = 0x7f0401b9
com.android.rockchip.mediacodecnew:attr/endIconScaleType = 0x7f0401aa
com.android.rockchip.mediacodecnew:layout/abc_alert_dialog_button_bar_material = 0x7f0c0008
com.android.rockchip.mediacodecnew:attr/endIconMode = 0x7f0401a9
com.android.rockchip.mediacodecnew:attr/buttonIconTint = 0x7f040096
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_default_width = 0x7f0702c6
com.android.rockchip.mediacodecnew:styleable/Capability = 0x7f130019
com.android.rockchip.mediacodecnew:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f070197
com.android.rockchip.mediacodecnew:attr/backHandlingEnabled = 0x7f040049
com.android.rockchip.mediacodecnew:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f12009b
com.android.rockchip.mediacodecnew:attr/enableEdgeToEdge = 0x7f0401a4
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.HeadlineSmall = 0x7f1201ee
com.android.rockchip.mediacodecnew:color/background_material_dark = 0x7f06001f
com.android.rockchip.mediacodecnew:color/m3_bottom_sheet_drag_handle_color = 0x7f060061
com.android.rockchip.mediacodecnew:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.android.rockchip.mediacodecnew:id/src_atop = 0x7f09021c
com.android.rockchip.mediacodecnew:attr/backgroundInsetTop = 0x7f04004f
com.android.rockchip.mediacodecnew:attr/textFillColor = 0x7f040460
com.android.rockchip.mediacodecnew:dimen/m3_btn_icon_only_default_size = 0x7f0700d4
com.android.rockchip.mediacodecnew:color/material_personalized_color_control_normal = 0x7f06025d
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1200e7
com.android.rockchip.mediacodecnew:attr/wavePeriod = 0x7f0404d0
com.android.rockchip.mediacodecnew:drawable/ic_keyboard_black_24dp = 0x7f08008d
com.android.rockchip.mediacodecnew:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080035
com.android.rockchip.mediacodecnew:attr/elevationOverlayEnabled = 0x7f0401a2
com.android.rockchip.mediacodecnew:attr/textInputFilledExposedDropdownMenuStyle = 0x7f040462
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_hover_track_color = 0x7f0d0129
com.android.rockchip.mediacodecnew:attr/elevationOverlayColor = 0x7f0401a1
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary30 = 0x7f0600c5
com.android.rockchip.mediacodecnew:attr/itemRippleColor = 0x7f040254
com.android.rockchip.mediacodecnew:attr/elevationOverlayAccentColor = 0x7f0401a0
com.android.rockchip.mediacodecnew:color/design_default_color_surface = 0x7f060049
com.android.rockchip.mediacodecnew:attr/passwordToggleContentDescription = 0x7f040372
com.android.rockchip.mediacodecnew:style/TpVideoPlayButton = 0x7f1202e7
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f07020e
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Small = 0x7f12002b
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0d009d
com.android.rockchip.mediacodecnew:attr/layout_constraintTop_creator = 0x7f04029b
com.android.rockchip.mediacodecnew:attr/prefixTextColor = 0x7f04038a
com.android.rockchip.mediacodecnew:attr/editTextColor = 0x7f04019d
com.android.rockchip.mediacodecnew:attr/editTextBackground = 0x7f04019c
com.android.rockchip.mediacodecnew:drawable/abc_cab_background_internal_bg = 0x7f080038
com.android.rockchip.mediacodecnew:color/m3_calendar_item_disabled_text = 0x7f060067
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f12006f
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f070203
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_autocomplete_menu_list_item_selected_container_color = 0x7f0d00a2
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral20 = 0x7f060205
com.android.rockchip.mediacodecnew:color/cardview_shadow_end_color = 0x7f06002c
com.android.rockchip.mediacodecnew:color/m3_dynamic_dark_default_color_primary_text = 0x7f06007a
com.android.rockchip.mediacodecnew:dimen/mtrl_switch_track_width = 0x7f0702f3
com.android.rockchip.mediacodecnew:id/bounceBoth = 0x7f090060
com.android.rockchip.mediacodecnew:attr/clockHandColor = 0x7f0400de
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0d004a
com.android.rockchip.mediacodecnew:attr/dropdownListPreferredItemHeight = 0x7f040199
com.android.rockchip.mediacodecnew:attr/dropDownBackgroundTint = 0x7f040197
com.android.rockchip.mediacodecnew:attr/autoSizeMinTextSize = 0x7f040044
com.android.rockchip.mediacodecnew:style/Base.V22.Theme.AppCompat = 0x7f1200ae
com.android.rockchip.mediacodecnew:attr/itemBackground = 0x7f040248
com.android.rockchip.mediacodecnew:attr/alpha = 0x7f04002f
com.android.rockchip.mediacodecnew:id/btn_save_scene = 0x7f09007c
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary95 = 0x7f0600d9
com.android.rockchip.mediacodecnew:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant100 = 0x7f06010f
com.android.rockchip.mediacodecnew:dimen/material_clock_display_height = 0x7f07021b
com.android.rockchip.mediacodecnew:styleable/SwitchMaterial = 0x7f130088
com.android.rockchip.mediacodecnew:attr/drawableStartCompat = 0x7f040190
com.android.rockchip.mediacodecnew:macro/m3_comp_snackbar_supporting_text_color = 0x7f0d0116
com.android.rockchip.mediacodecnew:attr/offsetAlignmentMode = 0x7f04035b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral92 = 0x7f0600ae
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1202ab
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_disabled_track_opacity = 0x7f07018b
com.android.rockchip.mediacodecnew:anim/design_bottom_sheet_slide_out = 0x7f010019
com.android.rockchip.mediacodecnew:attr/extendStrategy = 0x7f0401c6
com.android.rockchip.mediacodecnew:attr/trackDecoration = 0x7f0404ae
com.android.rockchip.mediacodecnew:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.android.rockchip.mediacodecnew:id/dragUp = 0x7f0900cf
com.android.rockchip.mediacodecnew:drawable/abc_item_background_holo_light = 0x7f08004c
com.android.rockchip.mediacodecnew:attr/dragScale = 0x7f040188
com.android.rockchip.mediacodecnew:layout/mtrl_alert_dialog_actions = 0x7f0c0050
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_outline = 0x7f06015b
com.android.rockchip.mediacodecnew:attr/dragDirection = 0x7f040187
com.android.rockchip.mediacodecnew:color/design_fab_stroke_top_inner_color = 0x7f060050
com.android.rockchip.mediacodecnew:attr/doubleTapEnabled = 0x7f040186
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_year_horizontal_padding = 0x7f070291
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f07027f
com.android.rockchip.mediacodecnew:attr/textInputLayoutFocusedRectEnabled = 0x7f040464
com.android.rockchip.mediacodecnew:attr/dividerInsetEnd = 0x7f040181
com.android.rockchip.mediacodecnew:color/mtrl_navigation_item_icon_tint = 0x7f0602b7
com.android.rockchip.mediacodecnew:string/material_motion_easing_decelerated = 0x7f110048
com.android.rockchip.mediacodecnew:attr/windowFixedWidthMajor = 0x7f0404d9
com.android.rockchip.mediacodecnew:attr/tickColorInactive = 0x7f040483
com.android.rockchip.mediacodecnew:id/tv_ldc_value = 0x7f09027a
com.android.rockchip.mediacodecnew:id/postLayout = 0x7f0901a4
com.android.rockchip.mediacodecnew:attr/errorEnabled = 0x7f0401b3
com.android.rockchip.mediacodecnew:color/accent_material_dark = 0x7f060019
com.android.rockchip.mediacodecnew:style/Widget.Design.TextInputEditText = 0x7f120346
com.android.rockchip.mediacodecnew:attr/boxStrokeErrorColor = 0x7f040089
com.android.rockchip.mediacodecnew:style/MaterialAlertDialog.Material3.Title.Text = 0x7f12012c
com.android.rockchip.mediacodecnew:attr/buttonIcon = 0x7f040094
com.android.rockchip.mediacodecnew:attr/dialogTheme = 0x7f04017c
com.android.rockchip.mediacodecnew:color/m3_default_color_primary_text = 0x7f060078
com.android.rockchip.mediacodecnew:attr/tabGravity = 0x7f040413
com.android.rockchip.mediacodecnew:attr/titleTextStyle = 0x7f04049b
com.android.rockchip.mediacodecnew:attr/collapsedSize = 0x7f0400eb
com.android.rockchip.mediacodecnew:attr/layout_optimizationLevel = 0x7f0402b3
com.android.rockchip.mediacodecnew:attr/behavior_significantVelocityThreshold = 0x7f040074
com.android.rockchip.mediacodecnew:attr/chipStrokeColor = 0x7f0400d0
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_surface_container_highest = 0x7f060165
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Badge = 0x7f1203f7
com.android.rockchip.mediacodecnew:string/searchview_navigation_content_description = 0x7f11009c
com.android.rockchip.mediacodecnew:attr/paddingRightSystemWindowInsets = 0x7f040369
com.android.rockchip.mediacodecnew:attr/yearTodayStyle = 0x7f0404e0
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f070126
com.android.rockchip.mediacodecnew:attr/colorPrimaryVariant = 0x7f04011c
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_min_height = 0x7f0702a7
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f060178
com.android.rockchip.mediacodecnew:attr/expandedTitleTextAppearance = 0x7f0401c3
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_background = 0x7f06016c
com.android.rockchip.mediacodecnew:id/material_clock_display = 0x7f090129
com.android.rockchip.mediacodecnew:attr/dayTodayStyle = 0x7f040171
com.android.rockchip.mediacodecnew:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0800b3
com.android.rockchip.mediacodecnew:layout/abc_action_mode_bar = 0x7f0c0004
com.android.rockchip.mediacodecnew:attr/dayInvalidStyle = 0x7f04016e
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1200d0
com.android.rockchip.mediacodecnew:string/material_motion_easing_emphasized = 0x7f110049
com.android.rockchip.mediacodecnew:attr/customStringValue = 0x7f04016d
com.android.rockchip.mediacodecnew:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1203c5
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_stroke_size = 0x7f070264
com.android.rockchip.mediacodecnew:attr/customReference = 0x7f04016c
com.android.rockchip.mediacodecnew:color/m3_navigation_rail_ripple_color_selector = 0x7f060096
com.android.rockchip.mediacodecnew:attr/tabIndicatorAnimationDuration = 0x7f040417
com.android.rockchip.mediacodecnew:color/abc_tint_default = 0x7f060014
com.android.rockchip.mediacodecnew:id/on = 0x7f090182
com.android.rockchip.mediacodecnew:drawable/mtrl_ic_checkbox_checked = 0x7f0800c5
com.android.rockchip.mediacodecnew:attr/flow_lastHorizontalStyle = 0x7f0401f4
com.android.rockchip.mediacodecnew:attr/customNavigationLayout = 0x7f04016a
com.android.rockchip.mediacodecnew:attr/customDimension = 0x7f040167
com.android.rockchip.mediacodecnew:color/material_timepicker_modebutton_tint = 0x7f060296
com.android.rockchip.mediacodecnew:attr/checkedIconEnabled = 0x7f0400b8
com.android.rockchip.mediacodecnew:raw/negative_fragment = 0x7f10001b
com.android.rockchip.mediacodecnew:attr/fontProviderFetchTimeout = 0x7f040203
com.android.rockchip.mediacodecnew:attr/actionModeBackground = 0x7f040013
com.android.rockchip.mediacodecnew:attr/actionMenuTextAppearance = 0x7f040011
com.android.rockchip.mediacodecnew:attr/transitionEasing = 0x7f0404b7
com.android.rockchip.mediacodecnew:attr/shapeAppearanceSmallComponent = 0x7f0403c1
com.android.rockchip.mediacodecnew:attr/itemTextColor = 0x7f040263
com.android.rockchip.mediacodecnew:color/abc_tint_btn_checkable = 0x7f060013
com.android.rockchip.mediacodecnew:dimen/design_tab_scrollable_min_width = 0x7f07008a
com.android.rockchip.mediacodecnew:attr/arrowHeadLength = 0x7f04003a
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Chip.Entry = 0x7f120410
com.android.rockchip.mediacodecnew:style/Widget.Material3.Snackbar.FullWidth = 0x7f1203d6
com.android.rockchip.mediacodecnew:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0401e6
com.android.rockchip.mediacodecnew:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401ca
com.android.rockchip.mediacodecnew:attr/motionDurationExtraLong2 = 0x7f040322
com.android.rockchip.mediacodecnew:attr/editTextStyle = 0x7f04019e
com.android.rockchip.mediacodecnew:attr/itemShapeInsetEnd = 0x7f040259
com.android.rockchip.mediacodecnew:attr/counterOverflowTextColor = 0x7f04015c
com.android.rockchip.mediacodecnew:attr/flow_maxElementsWrap = 0x7f0401f7
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0d0013
com.android.rockchip.mediacodecnew:attr/state_collapsed = 0x7f0403f1
com.android.rockchip.mediacodecnew:attr/actionTextColorAlpha = 0x7f040025
com.android.rockchip.mediacodecnew:color/m3_dynamic_dark_default_color_secondary_text = 0x7f06007b
com.android.rockchip.mediacodecnew:attr/boxCornerRadiusBottomEnd = 0x7f040084
com.android.rockchip.mediacodecnew:attr/forceApplySystemWindowInsetTop = 0x7f04020a
com.android.rockchip.mediacodecnew:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.android.rockchip.mediacodecnew:id/textinput_counter = 0x7f09024c
com.android.rockchip.mediacodecnew:attr/counterOverflowTextAppearance = 0x7f04015b
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary10 = 0x7f060237
com.android.rockchip.mediacodecnew:attr/counterEnabled = 0x7f040159
com.android.rockchip.mediacodecnew:drawable/ic_clock_black_24dp = 0x7f08008a
com.android.rockchip.mediacodecnew:animator/fragment_close_exit = 0x7f020004
com.android.rockchip.mediacodecnew:attr/closeIcon = 0x7f0400e1
com.android.rockchip.mediacodecnew:attr/compatShadowEnabled = 0x7f040132
com.android.rockchip.mediacodecnew:drawable/abc_list_selector_disabled_holo_light = 0x7f080056
com.android.rockchip.mediacodecnew:attr/floatingActionButtonTertiaryStyle = 0x7f0401ea
com.android.rockchip.mediacodecnew:attr/cornerSizeBottomRight = 0x7f040156
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1201c3
com.android.rockchip.mediacodecnew:attr/cornerRadius = 0x7f040153
com.android.rockchip.mediacodecnew:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f12016f
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_21 = 0x7f09001e
com.android.rockchip.mediacodecnew:attr/colorControlActivated = 0x7f0400f9
com.android.rockchip.mediacodecnew:attr/itemPadding = 0x7f040251
com.android.rockchip.mediacodecnew:styleable/AnimatedStateListDrawableItem = 0x7f130008
com.android.rockchip.mediacodecnew:id/tv_ct_blue_value = 0x7f09026b
com.android.rockchip.mediacodecnew:attr/activeIndicatorLabelPadding = 0x7f040027
com.android.rockchip.mediacodecnew:attr/state_lifted = 0x7f0403f7
com.android.rockchip.mediacodecnew:style/Theme.Material3.Dark.Dialog = 0x7f12022b
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f120107
com.android.rockchip.mediacodecnew:string/m3_sys_motion_easing_standard_accelerate = 0x7f11003e
com.android.rockchip.mediacodecnew:attr/contentInsetRight = 0x7f04013f
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0800d4
com.android.rockchip.mediacodecnew:attr/flow_verticalAlign = 0x7f0401f9
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f12044c
com.android.rockchip.mediacodecnew:attr/cornerFamilyTopLeft = 0x7f040151
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f1202d9
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary80 = 0x7f060130
com.android.rockchip.mediacodecnew:id/screen = 0x7f0901cb
com.android.rockchip.mediacodecnew:attr/cornerFamilyBottomRight = 0x7f040150
com.android.rockchip.mediacodecnew:attr/hideOnScroll = 0x7f04021e
com.android.rockchip.mediacodecnew:attr/cornerFamilyBottomLeft = 0x7f04014f
com.android.rockchip.mediacodecnew:attr/chipEndPadding = 0x7f0400c2
com.android.rockchip.mediacodecnew:attr/firstBaselineToTopHeight = 0x7f0401db
com.android.rockchip.mediacodecnew:layout/mtrl_picker_actions = 0x7f0c0063
com.android.rockchip.mediacodecnew:attr/cornerFamily = 0x7f04014e
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f07020c
com.android.rockchip.mediacodecnew:dimen/m3_extended_fab_start_padding = 0x7f0701ad
com.android.rockchip.mediacodecnew:attr/coplanarSiblingViewId = 0x7f04014d
com.android.rockchip.mediacodecnew:styleable/AppCompatImageView = 0x7f13000e
com.android.rockchip.mediacodecnew:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f040335
com.android.rockchip.mediacodecnew:attr/layout_constraintGuide_percent = 0x7f040289
com.android.rockchip.mediacodecnew:attr/iconifiedByDefault = 0x7f040230
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_headline_type = 0x7f0d0088
com.android.rockchip.mediacodecnew:color/m3_dynamic_default_color_primary_text = 0x7f06007f
com.android.rockchip.mediacodecnew:attr/carousel_touchUpMode = 0x7f0400ac
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600b3
com.android.rockchip.mediacodecnew:attr/stackFromEnd = 0x7f0403e7
com.android.rockchip.mediacodecnew:color/button_material_dark = 0x7f060028
com.android.rockchip.mediacodecnew:attr/brightness = 0x7f04008c
com.android.rockchip.mediacodecnew:attr/colorSurface = 0x7f040122
com.android.rockchip.mediacodecnew:attr/contentScrim = 0x7f040149
com.android.rockchip.mediacodecnew:color/primary_material_dark = 0x7f0602d2
com.android.rockchip.mediacodecnew:attr/layout_constraintHorizontal_bias = 0x7f04028f
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Body1 = 0x7f120017
com.android.rockchip.mediacodecnew:attr/contentPaddingStart = 0x7f040147
com.android.rockchip.mediacodecnew:styleable/Spinner = 0x7f130082
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Bridge = 0x7f120263
com.android.rockchip.mediacodecnew:id/bounceEnd = 0x7f090061
com.android.rockchip.mediacodecnew:attr/contentPaddingBottom = 0x7f040143
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f120455
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_medium3 = 0x7f0a0019
com.android.rockchip.mediacodecnew:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.android.rockchip.mediacodecnew:dimen/abc_progress_bar_height_material = 0x7f070035
com.android.rockchip.mediacodecnew:attr/guidelineUseRtl = 0x7f040211
com.android.rockchip.mediacodecnew:layout/abc_action_bar_up_container = 0x7f0c0001
com.android.rockchip.mediacodecnew:dimen/material_textinput_min_width = 0x7f07023b
com.android.rockchip.mediacodecnew:attr/customFloatValue = 0x7f040168
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f120186
com.android.rockchip.mediacodecnew:attr/floatingActionButtonSmallStyle = 0x7f0401e5
com.android.rockchip.mediacodecnew:attr/drawPath = 0x7f04018a
com.android.rockchip.mediacodecnew:id/open_search_view_root = 0x7f09018c
com.android.rockchip.mediacodecnew:attr/contentInsetStartWithNavigation = 0x7f040141
com.android.rockchip.mediacodecnew:color/tp_video_progress_primary = 0x7f0602f0
com.android.rockchip.mediacodecnew:style/Base.Widget.Material3.Snackbar = 0x7f12010e
com.android.rockchip.mediacodecnew:color/material_dynamic_neutral_variant30 = 0x7f060213
com.android.rockchip.mediacodecnew:attr/circularflow_viewCenter = 0x7f0400da
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Assist = 0x7f120370
com.android.rockchip.mediacodecnew:attr/colorSecondaryFixedDim = 0x7f040120
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral10 = 0x7f06009d
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_start_padding = 0x7f0702a9
com.android.rockchip.mediacodecnew:attr/singleChoiceItemLayout = 0x7f0403d4
com.android.rockchip.mediacodecnew:color/design_dark_default_color_on_background = 0x7f060032
com.android.rockchip.mediacodecnew:attr/layout_constraintWidth = 0x7f0402a1
com.android.rockchip.mediacodecnew:attr/colorSecondaryVariant = 0x7f040121
com.android.rockchip.mediacodecnew:drawable/notification_template_icon_low_bg = 0x7f0800e3
com.android.rockchip.mediacodecnew:drawable/material_cursor_drawable = 0x7f0800aa
com.android.rockchip.mediacodecnew:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f07019b
com.android.rockchip.mediacodecnew:attr/badgeRadius = 0x7f040057
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_input_text_color = 0x7f0d00ea
com.android.rockchip.mediacodecnew:drawable/mtrl_dropdown_arrow = 0x7f0800c0
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.TitleMedium = 0x7f1201f7
com.android.rockchip.mediacodecnew:attr/colorOnContainer = 0x7f0400ff
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_day_height = 0x7f070270
com.android.rockchip.mediacodecnew:attr/actionProviderClass = 0x7f040024
com.android.rockchip.mediacodecnew:style/Base.V23.Theme.AppCompat.Light = 0x7f1200b1
com.android.rockchip.mediacodecnew:attr/chipMinTouchTargetSize = 0x7f0400ca
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1203a4
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f070153
com.android.rockchip.mediacodecnew:anim/abc_tooltip_exit = 0x7f01000b
com.android.rockchip.mediacodecnew:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0d00ce
com.android.rockchip.mediacodecnew:dimen/compat_notification_large_icon_max_width = 0x7f07005c
com.android.rockchip.mediacodecnew:attr/popupWindowStyle = 0x7f040387
com.android.rockchip.mediacodecnew:attr/barLength = 0x7f040066
com.android.rockchip.mediacodecnew:attr/textAppearanceTitleLarge = 0x7f040455
com.android.rockchip.mediacodecnew:dimen/m3_fab_translation_z_pressed = 0x7f0701b2
com.android.rockchip.mediacodecnew:attr/constraintSetEnd = 0x7f040135
com.android.rockchip.mediacodecnew:attr/forceDefaultNavigationOnClickListener = 0x7f04020b
com.android.rockchip.mediacodecnew:color/material_blue_grey_800 = 0x7f0601fb
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0d00af
com.android.rockchip.mediacodecnew:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401dc
com.android.rockchip.mediacodecnew:id/submenuarrow = 0x7f09022b
com.android.rockchip.mediacodecnew:color/background_floating_material_light = 0x7f06001e
com.android.rockchip.mediacodecnew:attr/constraintRotate = 0x7f040133
com.android.rockchip.mediacodecnew:integer/material_motion_duration_short_1 = 0x7f0a002a
com.android.rockchip.mediacodecnew:attr/constraint_referenced_ids = 0x7f040137
com.android.rockchip.mediacodecnew:color/m3_assist_chip_stroke_color = 0x7f060060
com.android.rockchip.mediacodecnew:color/material_harmonized_color_error = 0x7f06024a
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0d011d
com.android.rockchip.mediacodecnew:drawable/tp_video_settings_button_background = 0x7f0800f2
com.android.rockchip.mediacodecnew:attr/colorTertiaryContainer = 0x7f04012e
com.android.rockchip.mediacodecnew:animator/tp_video_button_press = 0x7f020022
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f070114
com.android.rockchip.mediacodecnew:attr/colorSurfaceVariant = 0x7f04012b
com.android.rockchip.mediacodecnew:style/Widget.Material3.SearchView = 0x7f1203cc
com.android.rockchip.mediacodecnew:animator/mtrl_chip_state_list_anim = 0x7f020018
com.android.rockchip.mediacodecnew:string/abc_searchview_description_search = 0x7f110015
com.android.rockchip.mediacodecnew:attr/flow_verticalGap = 0x7f0401fb
com.android.rockchip.mediacodecnew:attr/listPreferredItemPaddingLeft = 0x7f0402cb
com.android.rockchip.mediacodecnew:style/Widget.Material3.Chip.Input = 0x7f120374
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_year_height = 0x7f070290
com.android.rockchip.mediacodecnew:dimen/design_bottom_sheet_elevation = 0x7f07006b
com.android.rockchip.mediacodecnew:dimen/abc_text_size_title_material = 0x7f07004f
com.android.rockchip.mediacodecnew:color/material_deep_teal_500 = 0x7f060200
com.android.rockchip.mediacodecnew:style/Widget.Material3.Light.ActionBar.Solid = 0x7f12039b
com.android.rockchip.mediacodecnew:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f120387
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f120062
com.android.rockchip.mediacodecnew:attr/colorSurfaceBright = 0x7f040123
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.SearchView.ActionBar = 0x7f120330
com.android.rockchip.mediacodecnew:drawable/abc_cab_background_top_mtrl_alpha = 0x7f08003a
com.android.rockchip.mediacodecnew:attr/badgeWithTextShapeAppearanceOverlay = 0x7f040064
com.android.rockchip.mediacodecnew:integer/m3_sys_motion_duration_long2 = 0x7f0a0014
com.android.rockchip.mediacodecnew:attr/listDividerAlertDialog = 0x7f0402c2
com.android.rockchip.mediacodecnew:attr/lineSpacing = 0x7f0402bd
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1203f6
com.android.rockchip.mediacodecnew:attr/recyclerViewStyle = 0x7f04039e
com.android.rockchip.mediacodecnew:anim/tp_speed_dialog_enter = 0x7f01002c
com.android.rockchip.mediacodecnew:attr/colorPrimaryFixed = 0x7f040118
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral60 = 0x7f060102
com.android.rockchip.mediacodecnew:styleable/CardView = 0x7f13001a
com.android.rockchip.mediacodecnew:color/m3_selection_control_ripple_color_selector = 0x7f060142
com.android.rockchip.mediacodecnew:attr/badgeHeight = 0x7f040056
com.android.rockchip.mediacodecnew:attr/colorOutline = 0x7f040113
com.android.rockchip.mediacodecnew:attr/layout_editor_absoluteY = 0x7f0402a8
com.android.rockchip.mediacodecnew:color/abc_decor_view_status_guard = 0x7f060005
com.android.rockchip.mediacodecnew:attr/behavior_halfExpandedRatio = 0x7f04006f
com.android.rockchip.mediacodecnew:style/Widget.Material3.ChipGroup = 0x7f12037a
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb_checked = 0x7f0800cd
com.android.rockchip.mediacodecnew:dimen/m3_badge_with_text_offset = 0x7f0700b8
com.android.rockchip.mediacodecnew:dimen/highlight_alpha_material_dark = 0x7f070094
com.android.rockchip.mediacodecnew:attr/colorOnSurfaceVariant = 0x7f04010e
com.android.rockchip.mediacodecnew:attr/textAppearanceSubtitle2 = 0x7f040454
com.android.rockchip.mediacodecnew:attr/colorOnSurfaceInverse = 0x7f04010d
com.android.rockchip.mediacodecnew:color/material_personalized_color_primary = 0x7f06026e
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f070128
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1202ae
com.android.rockchip.mediacodecnew:attr/keyPositionType = 0x7f040265
com.android.rockchip.mediacodecnew:style/Base.V26.Theme.AppCompat.Light = 0x7f1200b7
com.android.rockchip.mediacodecnew:attr/colorOnSecondaryFixedVariant = 0x7f04010b
com.android.rockchip.mediacodecnew:integer/mtrl_view_gone = 0x7f0a003f
com.android.rockchip.mediacodecnew:attr/layout_constraintLeft_toRightOf = 0x7f040294
com.android.rockchip.mediacodecnew:bool/mtrl_btn_textappearance_all_caps = 0x7f050002
com.android.rockchip.mediacodecnew:styleable/CustomAttribute = 0x7f13002e
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f12026c
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.SearchBar = 0x7f1201f3
com.android.rockchip.mediacodecnew:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.android.rockchip.mediacodecnew:attr/arrowShaftLength = 0x7f04003b
com.android.rockchip.mediacodecnew:attr/currentState = 0x7f040160
com.android.rockchip.mediacodecnew:color/secondary_text_default_material_dark = 0x7f0602da
com.android.rockchip.mediacodecnew:attr/colorOnPrimary = 0x7f040103
com.android.rockchip.mediacodecnew:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700a8
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f120314
com.android.rockchip.mediacodecnew:attr/colorPrimaryInverse = 0x7f04011a
com.android.rockchip.mediacodecnew:id/tv_bandwidth_value = 0x7f090265
com.android.rockchip.mediacodecnew:array/image_format_options = 0x7f030000
com.android.rockchip.mediacodecnew:attr/motionEffect_end = 0x7f04033e
com.android.rockchip.mediacodecnew:anim/design_snackbar_in = 0x7f01001a
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.CompoundButton.Switch = 0x7f120307
com.android.rockchip.mediacodecnew:attr/behavior_skipCollapsed = 0x7f040075
com.android.rockchip.mediacodecnew:macro/m3_comp_dialog_headline_type = 0x7f0d0026
com.android.rockchip.mediacodecnew:attr/colorOnError = 0x7f040101
com.android.rockchip.mediacodecnew:styleable/ClockFaceView = 0x7f130020
com.android.rockchip.mediacodecnew:attr/framePosition = 0x7f04020d
com.android.rockchip.mediacodecnew:attr/colorOnContainerUnchecked = 0x7f040100
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1202d6
com.android.rockchip.mediacodecnew:attr/circularflow_defaultRadius = 0x7f0400d8
com.android.rockchip.mediacodecnew:attr/snackbarStyle = 0x7f0403da
com.android.rockchip.mediacodecnew:attr/colorControlHighlight = 0x7f0400fa
com.android.rockchip.mediacodecnew:attr/gapBetweenBars = 0x7f04020e
com.android.rockchip.mediacodecnew:drawable/$avd_hide_password__1 = 0x7f080001
com.android.rockchip.mediacodecnew:color/m3_ref_palette_secondary99 = 0x7f060133
com.android.rockchip.mediacodecnew:attr/colorContainer = 0x7f0400f8
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1203b3
com.android.rockchip.mediacodecnew:dimen/m3_sys_elevation_level0 = 0x7f0701e9
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f07015a
com.android.rockchip.mediacodecnew:color/m3_card_foreground_color = 0x7f060069
com.android.rockchip.mediacodecnew:attr/colorOnBackground = 0x7f0400fe
com.android.rockchip.mediacodecnew:attr/colorButtonNormal = 0x7f0400f7
com.android.rockchip.mediacodecnew:attr/goIcon = 0x7f040210
com.android.rockchip.mediacodecnew:dimen/material_clock_period_toggle_height = 0x7f070223
com.android.rockchip.mediacodecnew:id/transition_current_scene = 0x7f09025f
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral96 = 0x7f06010a
com.android.rockchip.mediacodecnew:attr/shapeAppearanceCornerExtraLarge = 0x7f0403b9
com.android.rockchip.mediacodecnew:attr/materialCalendarHeaderSelection = 0x7f0402e6
com.android.rockchip.mediacodecnew:attr/telltales_tailColor = 0x7f04042f
com.android.rockchip.mediacodecnew:attr/closeIconStartPadding = 0x7f0400e5
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0701f6
com.android.rockchip.mediacodecnew:attr/minTouchTargetSize = 0x7f040318
com.android.rockchip.mediacodecnew:string/mtrl_picker_range_header_only_end_selected = 0x7f110078
com.android.rockchip.mediacodecnew:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0a003a
com.android.rockchip.mediacodecnew:id/search_bar = 0x7f0901d2
com.android.rockchip.mediacodecnew:attr/imageButtonStyle = 0x7f040233
com.android.rockchip.mediacodecnew:color/tp_video_text_disabled = 0x7f0602f4
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.UnelevatedButton = 0x7f12036b
com.android.rockchip.mediacodecnew:attr/collapsingToolbarLayoutStyle = 0x7f0400f3
com.android.rockchip.mediacodecnew:id/action_mode_bar = 0x7f090040
com.android.rockchip.mediacodecnew:anim/abc_fade_out = 0x7f010001
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_card_container_shape = 0x7f0d0048
com.android.rockchip.mediacodecnew:attr/motionDurationShort2 = 0x7f04032e
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.AppCompat.Display4 = 0x7f12001e
com.android.rockchip.mediacodecnew:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700b7
com.android.rockchip.mediacodecnew:attr/bottomInsetScrimEnabled = 0x7f04007c
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_padding_top = 0x7f070261
com.android.rockchip.mediacodecnew:attr/boxCornerRadiusTopEnd = 0x7f040086
com.android.rockchip.mediacodecnew:attr/closeIconEndPadding = 0x7f0400e3
com.android.rockchip.mediacodecnew:attr/centerIfNoTextEnabled = 0x7f0400af
com.android.rockchip.mediacodecnew:color/button_material_light = 0x7f060029
com.android.rockchip.mediacodecnew:attr/clickAction = 0x7f0400dc
com.android.rockchip.mediacodecnew:anim/m3_bottom_sheet_slide_out = 0x7f010022
com.android.rockchip.mediacodecnew:macro/m3_comp_elevated_card_container_shape = 0x7f0d002c
com.android.rockchip.mediacodecnew:attr/clockFaceBackgroundColor = 0x7f0400dd
com.android.rockchip.mediacodecnew:color/m3_primary_text_disable_only = 0x7f060098
com.android.rockchip.mediacodecnew:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1200b8
com.android.rockchip.mediacodecnew:color/design_fab_shadow_start_color = 0x7f06004d
com.android.rockchip.mediacodecnew:color/m3_chip_stroke_color = 0x7f060071
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0600e7
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Bridge = 0x7f120066
com.android.rockchip.mediacodecnew:attr/autoSizeMaxTextSize = 0x7f040043
com.android.rockchip.mediacodecnew:color/material_personalized_color_on_surface_inverse = 0x7f060268
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f12014d
com.android.rockchip.mediacodecnew:attr/clearsTag = 0x7f0400db
com.android.rockchip.mediacodecnew:attr/contentPaddingTop = 0x7f040148
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar.Day = 0x7f12039f
com.android.rockchip.mediacodecnew:attr/circularflow_radiusInDP = 0x7f0400d9
com.android.rockchip.mediacodecnew:style/Widget.Material3.MaterialCalendar = 0x7f12039e
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_text_btn_icon_padding = 0x7f070265
com.android.rockchip.mediacodecnew:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.android.rockchip.mediacodecnew:attr/circularProgressIndicatorStyle = 0x7f0400d5
com.android.rockchip.mediacodecnew:style/ShapeAppearanceOverlay.Material3.Button = 0x7f120182
com.android.rockchip.mediacodecnew:id/tv_hue_value = 0x7f090278
com.android.rockchip.mediacodecnew:attr/textPanX = 0x7f04046c
com.android.rockchip.mediacodecnew:style/Platform.V21.AppCompat.Light = 0x7f120142
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f12008b
com.android.rockchip.mediacodecnew:attr/expandedTitleTextColor = 0x7f0401c4
com.android.rockchip.mediacodecnew:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400f2
com.android.rockchip.mediacodecnew:macro/m3_comp_filled_button_label_text_color = 0x7f0d0045
com.android.rockchip.mediacodecnew:attr/textBackgroundPanX = 0x7f040459
com.android.rockchip.mediacodecnew:attr/badgeWithTextHeight = 0x7f040061
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f120453
com.android.rockchip.mediacodecnew:attr/itemFillColor = 0x7f040249
com.android.rockchip.mediacodecnew:layout/notification_action = 0x7f0c0070
com.android.rockchip.mediacodecnew:attr/closeIconTint = 0x7f0400e6
com.android.rockchip.mediacodecnew:drawable/mtrl_switch_thumb = 0x7f0800cc
com.android.rockchip.mediacodecnew:layout/select_dialog_multichoice_material = 0x7f0c0078
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary30 = 0x7f06011e
com.android.rockchip.mediacodecnew:style/Theme.Material3.DynamicColors.DayNight = 0x7f12023a
com.android.rockchip.mediacodecnew:attr/chipStartPadding = 0x7f0400cf
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f120113
com.android.rockchip.mediacodecnew:attr/materialSwitchStyle = 0x7f040300
com.android.rockchip.mediacodecnew:drawable/notification_bg_low = 0x7f0800dc
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1201a3
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1201a2
com.android.rockchip.mediacodecnew:dimen/compat_button_padding_horizontal_material = 0x7f070058
com.android.rockchip.mediacodecnew:attr/circleRadius = 0x7f0400d4
com.android.rockchip.mediacodecnew:attr/hideAnimationBehavior = 0x7f04021a
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0d0073
com.android.rockchip.mediacodecnew:id/skipCollapsed = 0x7f090204
com.android.rockchip.mediacodecnew:attr/actionModeTheme = 0x7f040020
com.android.rockchip.mediacodecnew:macro/m3_comp_badge_large_label_text_type = 0x7f0d0004
com.android.rockchip.mediacodecnew:dimen/m3_btn_padding_top = 0x7f0700dc
com.android.rockchip.mediacodecnew:attr/bottomSheetDragHandleStyle = 0x7f04007f
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_active_text_size = 0x7f0702c4
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0601ab
com.android.rockchip.mediacodecnew:attr/chipSpacingHorizontal = 0x7f0400cc
com.android.rockchip.mediacodecnew:attr/counterTextAppearance = 0x7f04015d
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_bottom_padding = 0x7f07029f
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1203f4
com.android.rockchip.mediacodecnew:attr/collapseIcon = 0x7f0400ea
com.android.rockchip.mediacodecnew:color/m3_checkbox_button_tint = 0x7f06006d
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0d008e
com.android.rockchip.mediacodecnew:attr/pathMotionArc = 0x7f040377
com.android.rockchip.mediacodecnew:attr/subheaderInsetEnd = 0x7f040400
com.android.rockchip.mediacodecnew:attr/activityChooserViewStyle = 0x7f040028
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f120443
com.android.rockchip.mediacodecnew:macro/m3_comp_sheet_side_docked_container_color = 0x7f0d010a
com.android.rockchip.mediacodecnew:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.android.rockchip.mediacodecnew:color/m3_dynamic_default_color_secondary_text = 0x7f060080
com.android.rockchip.mediacodecnew:attr/chipBackgroundColor = 0x7f0400c0
com.android.rockchip.mediacodecnew:dimen/m3_side_sheet_standard_elevation = 0x7f0701df
com.android.rockchip.mediacodecnew:attr/helperTextEnabled = 0x7f040217
com.android.rockchip.mediacodecnew:attr/checkedTextViewStyle = 0x7f0400bf
com.android.rockchip.mediacodecnew:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0602b3
com.android.rockchip.mediacodecnew:color/m3_timepicker_display_background_color = 0x7f0601f4
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f120317
com.android.rockchip.mediacodecnew:attr/defaultScrollFlagsEnabled = 0x7f040175
com.android.rockchip.mediacodecnew:attr/checkedIconVisible = 0x7f0400bd
com.android.rockchip.mediacodecnew:attr/materialCalendarHeaderCancelButton = 0x7f0402e2
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Button.TextButton = 0x7f12028e
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1200cc
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f07014e
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0800bb
com.android.rockchip.mediacodecnew:attr/flow_lastVerticalBias = 0x7f0401f5
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral100 = 0x7f0600f7
com.android.rockchip.mediacodecnew:attr/pressedTranslationZ = 0x7f04038c
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_20 = 0x7f09001d
com.android.rockchip.mediacodecnew:attr/windowNoTitle = 0x7f0404dd
com.android.rockchip.mediacodecnew:attr/checkedIcon = 0x7f0400b7
com.android.rockchip.mediacodecnew:interpolator/mtrl_fast_out_slow_in = 0x7f0b000f
com.android.rockchip.mediacodecnew:attr/checkboxStyle = 0x7f0400b4
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.DisplaySmall = 0x7f1201eb
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_item_icon_size = 0x7f0702c1
com.android.rockchip.mediacodecnew:styleable/MotionTelltales = 0x7f130069
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1200da
com.android.rockchip.mediacodecnew:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f120048
com.android.rockchip.mediacodecnew:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.android.rockchip.mediacodecnew:attr/thumbColor = 0x7f040475
com.android.rockchip.mediacodecnew:color/mtrl_filled_background_color = 0x7f0602ae
com.android.rockchip.mediacodecnew:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f12022a
com.android.rockchip.mediacodecnew:id/speed_0_75 = 0x7f09020f
com.android.rockchip.mediacodecnew:animator/m3_chip_state_list_anim = 0x7f02000e
com.android.rockchip.mediacodecnew:styleable/StateListDrawableItem = 0x7f130085
com.android.rockchip.mediacodecnew:drawable/mtrl_ic_arrow_drop_up = 0x7f0800c2
com.android.rockchip.mediacodecnew:string/abc_menu_space_shortcut_label = 0x7f11000f
com.android.rockchip.mediacodecnew:dimen/notification_main_column_padding_top = 0x7f07030a
com.android.rockchip.mediacodecnew:color/material_dynamic_primary40 = 0x7f060221
com.android.rockchip.mediacodecnew:attr/layout_anchorGravity = 0x7f040274
com.android.rockchip.mediacodecnew:styleable/Transition = 0x7f130095
com.android.rockchip.mediacodecnew:attr/animationMode = 0x7f040036
com.android.rockchip.mediacodecnew:color/m3_radiobutton_button_tint = 0x7f060099
com.android.rockchip.mediacodecnew:id/navigation_header_container = 0x7f090173
com.android.rockchip.mediacodecnew:color/material_personalized__highlighted_text = 0x7f060258
com.android.rockchip.mediacodecnew:attr/imageRotate = 0x7f040236
com.android.rockchip.mediacodecnew:attr/carousel_nextState = 0x7f0400aa
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f12011d
com.android.rockchip.mediacodecnew:animator/design_fab_show_motion_spec = 0x7f020002
com.android.rockchip.mediacodecnew:attr/materialAlertDialogTitlePanelStyle = 0x7f0402da
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.SeekBar = 0x7f1200f5
com.android.rockchip.mediacodecnew:attr/dividerThickness = 0x7f040184
com.android.rockchip.mediacodecnew:attr/font = 0x7f0401fe
com.android.rockchip.mediacodecnew:attr/buttonPanelSideLayout = 0x7f040098
com.android.rockchip.mediacodecnew:attr/titleMarginEnd = 0x7f040493
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f120434
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f120282
com.android.rockchip.mediacodecnew:attr/actionDropDownStyle = 0x7f04000f
com.android.rockchip.mediacodecnew:attr/colorSurfaceContainerHighest = 0x7f040126
com.android.rockchip.mediacodecnew:attr/cardViewStyle = 0x7f0400a4
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Headline5 = 0x7f120203
com.android.rockchip.mediacodecnew:attr/materialIconButtonFilledStyle = 0x7f0402f7
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_icon = 0x7f0800b7
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0d00fe
com.android.rockchip.mediacodecnew:attr/touchAnchorSide = 0x7f0404a7
com.android.rockchip.mediacodecnew:attr/cardUseCompatPadding = 0x7f0400a3
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f12025d
com.android.rockchip.mediacodecnew:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f070186
com.android.rockchip.mediacodecnew:attr/colorSecondaryContainer = 0x7f04011e
com.android.rockchip.mediacodecnew:attr/cardPreventCornerOverlap = 0x7f0400a2
com.android.rockchip.mediacodecnew:id/action_bar_title = 0x7f090039
com.android.rockchip.mediacodecnew:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0702ec
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.ProgressBar = 0x7f12032a
com.android.rockchip.mediacodecnew:id/transition_scene_layoutid_cache = 0x7f090262
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_tertiary = 0x7f0601a6
com.android.rockchip.mediacodecnew:attr/expandedTitleMarginStart = 0x7f0401c1
com.android.rockchip.mediacodecnew:attr/chainUseRtl = 0x7f0400b0
com.android.rockchip.mediacodecnew:color/m3_timepicker_button_text_color = 0x7f0601f2
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0600df
com.android.rockchip.mediacodecnew:id/packed = 0x7f090195
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary80 = 0x7f0600ca
com.android.rockchip.mediacodecnew:anim/abc_tooltip_enter = 0x7f01000a
com.android.rockchip.mediacodecnew:anim/m3_side_sheet_enter_from_left = 0x7f010025
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f12041e
com.android.rockchip.mediacodecnew:attr/buttonTintMode = 0x7f04009c
com.android.rockchip.mediacodecnew:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f120255
com.android.rockchip.mediacodecnew:animator/fragment_fade_exit = 0x7f020006
com.android.rockchip.mediacodecnew:attr/materialCalendarFullscreenTheme = 0x7f0402e1
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0d00fd
com.android.rockchip.mediacodecnew:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.android.rockchip.mediacodecnew:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f07009b
com.android.rockchip.mediacodecnew:attr/chipSpacingVertical = 0x7f0400cd
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0d00c3
com.android.rockchip.mediacodecnew:dimen/m3_comp_outlined_card_icon_size = 0x7f070154
com.android.rockchip.mediacodecnew:attr/textAppearanceSubtitle1 = 0x7f040453
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1203e0
com.android.rockchip.mediacodecnew:attr/autoSizePresetSizes = 0x7f040045
com.android.rockchip.mediacodecnew:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0d0007
com.android.rockchip.mediacodecnew:color/switch_thumb_disabled_material_light = 0x7f0602df
com.android.rockchip.mediacodecnew:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f120413
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1201e1
com.android.rockchip.mediacodecnew:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f12005e
com.android.rockchip.mediacodecnew:animator/fragment_open_enter = 0x7f020007
com.android.rockchip.mediacodecnew:attr/clockIcon = 0x7f0400df
com.android.rockchip.mediacodecnew:attr/deriveConstraintsFrom = 0x7f040179
com.android.rockchip.mediacodecnew:drawable/mtrl_ic_checkbox_unchecked = 0x7f0800c6
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_primary = 0x7f0601c7
com.android.rockchip.mediacodecnew:attr/buttonGravity = 0x7f040093
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.Button = 0x7f1201bb
com.android.rockchip.mediacodecnew:attr/endIconCheckable = 0x7f0401a5
com.android.rockchip.mediacodecnew:attr/actionBarDivider = 0x7f040003
com.android.rockchip.mediacodecnew:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f070144
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1203dc
com.android.rockchip.mediacodecnew:id/animateToStart = 0x7f09004e
com.android.rockchip.mediacodecnew:anim/tp_speed_dropdown_exit = 0x7f01002f
com.android.rockchip.mediacodecnew:anim/m3_motion_fade_exit = 0x7f010024
com.android.rockchip.mediacodecnew:attr/layout_constraintVertical_weight = 0x7f0402a0
com.android.rockchip.mediacodecnew:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080036
com.android.rockchip.mediacodecnew:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700c5
com.android.rockchip.mediacodecnew:dimen/design_navigation_padding_bottom = 0x7f07007c
com.android.rockchip.mediacodecnew:attr/listItemLayout = 0x7f0402c3
com.android.rockchip.mediacodecnew:attr/buttonCompat = 0x7f040092
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0d007e
com.android.rockchip.mediacodecnew:attr/buttonBarNegativeButtonStyle = 0x7f04008e
com.android.rockchip.mediacodecnew:attr/itemActiveIndicatorStyle = 0x7f040247
com.android.rockchip.mediacodecnew:attr/motionEasingEmphasized = 0x7f040333
com.android.rockchip.mediacodecnew:attr/animateCircleAngleTo = 0x7f040032
com.android.rockchip.mediacodecnew:attr/buttonBarButtonStyle = 0x7f04008d
com.android.rockchip.mediacodecnew:dimen/design_navigation_item_vertical_padding = 0x7f07007a
com.android.rockchip.mediacodecnew:styleable/PropertySet = 0x7f130073
com.android.rockchip.mediacodecnew:attr/springStiffness = 0x7f0403e4
com.android.rockchip.mediacodecnew:dimen/m3_badge_size = 0x7f0700b5
com.android.rockchip.mediacodecnew:attr/boxStrokeWidthFocused = 0x7f04008b
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral_variant60 = 0x7f060114
com.android.rockchip.mediacodecnew:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0d0064
com.android.rockchip.mediacodecnew:dimen/compat_button_inset_vertical_material = 0x7f070057
com.android.rockchip.mediacodecnew:id/container = 0x7f0900a3
com.android.rockchip.mediacodecnew:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.android.rockchip.mediacodecnew:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0701a3
com.android.rockchip.mediacodecnew:attr/boxStrokeColor = 0x7f040088
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_surface = 0x7f0601c1
com.android.rockchip.mediacodecnew:attr/daySelectedStyle = 0x7f04016f
com.android.rockchip.mediacodecnew:drawable/m3_bottom_sheet_drag_handle = 0x7f0800a1
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.TitleLarge = 0x7f1201f6
com.android.rockchip.mediacodecnew:id/decelerateAndComplete = 0x7f0900b5
com.android.rockchip.mediacodecnew:anim/abc_popup_enter = 0x7f010003
com.android.rockchip.mediacodecnew:id/checkbox_auto_exposure = 0x7f090096
com.android.rockchip.mediacodecnew:attr/actionBarTheme = 0x7f04000c
com.android.rockchip.mediacodecnew:layout/mtrl_alert_dialog_title = 0x7f0c0051
com.android.rockchip.mediacodecnew:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400f0
com.android.rockchip.mediacodecnew:string/mtrl_exceed_max_badge_number_suffix = 0x7f110064
com.android.rockchip.mediacodecnew:dimen/m3_comp_switch_track_width = 0x7f070192
com.android.rockchip.mediacodecnew:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0d00e3
com.android.rockchip.mediacodecnew:attr/SharedValue = 0x7f040001
com.android.rockchip.mediacodecnew:color/m3_dark_hint_foreground = 0x7f060076
com.android.rockchip.mediacodecnew:attr/checkedIconTint = 0x7f0400bc
com.android.rockchip.mediacodecnew:layout/mtrl_picker_header_title_text = 0x7f0c0069
com.android.rockchip.mediacodecnew:attr/sliderStyle = 0x7f0403d8
com.android.rockchip.mediacodecnew:attr/maxWidth = 0x7f04030f
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0600db
com.android.rockchip.mediacodecnew:attr/cornerSizeTopLeft = 0x7f040157
com.android.rockchip.mediacodecnew:dimen/m3_btn_disabled_translation_z = 0x7f0700ce
com.android.rockchip.mediacodecnew:style/Base.V21.Theme.AppCompat = 0x7f1200a2
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_on_primary = 0x7f06018f
com.android.rockchip.mediacodecnew:attr/path_percent = 0x7f040378
com.android.rockchip.mediacodecnew:attr/boxCornerRadiusBottomStart = 0x7f040085
com.android.rockchip.mediacodecnew:style/Theme.Design.NoActionBar = 0x7f120228
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0702c3
com.android.rockchip.mediacodecnew:attr/actionModeStyle = 0x7f04001f
com.android.rockchip.mediacodecnew:attr/colorBackgroundFloating = 0x7f0400f6
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Button.Small = 0x7f120302
com.android.rockchip.mediacodecnew:dimen/abc_text_size_caption_material = 0x7f070042
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary0 = 0x7f060236
com.android.rockchip.mediacodecnew:drawable/mtrl_bottomsheet_drag_handle = 0x7f0800b4
com.android.rockchip.mediacodecnew:color/androidx_core_ripple_material_light = 0x7f06001b
com.android.rockchip.mediacodecnew:attr/isFlipHorizontal = 0x7f040241
com.android.rockchip.mediacodecnew:id/endToStart = 0x7f0900da
com.android.rockchip.mediacodecnew:drawable/ic_fast_rewind_white_24 = 0x7f08008c
com.android.rockchip.mediacodecnew:color/bright_foreground_material_dark = 0x7f060026
com.android.rockchip.mediacodecnew:style/Widget.Material3.FloatingActionButton.Primary = 0x7f120393
com.android.rockchip.mediacodecnew:color/material_slider_thumb_color = 0x7f060291
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.MaterialComponents = 0x7f120092
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_container_low = 0x7f06027c
com.android.rockchip.mediacodecnew:anim/design_bottom_sheet_slide_in = 0x7f010018
com.android.rockchip.mediacodecnew:drawable/abc_ic_ab_back_material = 0x7f08003e
com.android.rockchip.mediacodecnew:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.android.rockchip.mediacodecnew:color/material_dynamic_primary0 = 0x7f06021c
com.android.rockchip.mediacodecnew:dimen/m3_appbar_size_medium = 0x7f0700ab
com.android.rockchip.mediacodecnew:attr/collapseContentDescription = 0x7f0400e9
com.android.rockchip.mediacodecnew:attr/bottomSheetDialogTheme = 0x7f04007e
com.android.rockchip.mediacodecnew:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f120207
com.android.rockchip.mediacodecnew:attr/badgeStyle = 0x7f04005a
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1202d3
com.android.rockchip.mediacodecnew:dimen/mtrl_snackbar_background_corner_radius = 0x7f0702e9
com.android.rockchip.mediacodecnew:attr/borderlessButtonStyle = 0x7f04007a
com.android.rockchip.mediacodecnew:attr/borderRoundPercent = 0x7f040078
com.android.rockchip.mediacodecnew:drawable/abc_vector_test = 0x7f080077
com.android.rockchip.mediacodecnew:attr/textAppearanceTitleSmall = 0x7f040457
com.android.rockchip.mediacodecnew:drawable/m3_tabs_line_indicator = 0x7f0800a7
com.android.rockchip.mediacodecnew:color/material_personalized_color_control_highlight = 0x7f06025c
com.android.rockchip.mediacodecnew:attr/fontProviderPackage = 0x7f040204
com.android.rockchip.mediacodecnew:style/TpVideoSettingsButton = 0x7f1202e9
com.android.rockchip.mediacodecnew:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f07017d
com.android.rockchip.mediacodecnew:attr/passwordToggleEnabled = 0x7f040374
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_primary_container_color = 0x7f0d0037
com.android.rockchip.mediacodecnew:attr/buttonStyleSmall = 0x7f04009a
com.android.rockchip.mediacodecnew:attr/lastBaselineToBottomHeight = 0x7f04026d
com.android.rockchip.mediacodecnew:attr/transitionShapeAppearance = 0x7f0404ba
com.android.rockchip.mediacodecnew:attr/colorPrimaryContainer = 0x7f040116
com.android.rockchip.mediacodecnew:attr/actionBarSize = 0x7f040006
com.android.rockchip.mediacodecnew:attr/collapsingToolbarLayoutMediumSize = 0x7f0400f1
com.android.rockchip.mediacodecnew:color/material_divider_color = 0x7f060201
com.android.rockchip.mediacodecnew:attr/badgeText = 0x7f04005b
com.android.rockchip.mediacodecnew:attr/iconTintMode = 0x7f04022f
com.android.rockchip.mediacodecnew:raw/rainbow_fragment = 0x7f100020
com.android.rockchip.mediacodecnew:color/m3_button_ripple_color = 0x7f060065
com.android.rockchip.mediacodecnew:drawable/abc_cab_background_top_material = 0x7f080039
com.android.rockchip.mediacodecnew:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1200bf
com.android.rockchip.mediacodecnew:color/m3_dark_default_color_primary_text = 0x7f060073
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f0701f8
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_outline_variant = 0x7f0601c6
com.android.rockchip.mediacodecnew:attr/addElevationShadow = 0x7f040029
com.android.rockchip.mediacodecnew:drawable/m3_tabs_background = 0x7f0800a6
com.android.rockchip.mediacodecnew:style/Theme.AppCompat.Dialog = 0x7f120216
com.android.rockchip.mediacodecnew:attr/flow_verticalStyle = 0x7f0401fc
com.android.rockchip.mediacodecnew:attr/thumbTint = 0x7f04047f
com.android.rockchip.mediacodecnew:id/action_text = 0x7f090043
com.android.rockchip.mediacodecnew:attr/errorContentDescription = 0x7f0401b2
com.android.rockchip.mediacodecnew:attr/colorSecondaryFixed = 0x7f04011f
com.android.rockchip.mediacodecnew:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.android.rockchip.mediacodecnew:attr/region_heightLessThan = 0x7f04039f
com.android.rockchip.mediacodecnew:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1203e4
com.android.rockchip.mediacodecnew:id/accessibility_custom_action_2 = 0x7f09001c
com.android.rockchip.mediacodecnew:attr/checkedState = 0x7f0400be
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f07010d
com.android.rockchip.mediacodecnew:style/Theme.Material3.DynamicColors.Light = 0x7f12023b
com.android.rockchip.mediacodecnew:anim/abc_slide_out_bottom = 0x7f010008
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error50 = 0x7f0600ee
com.android.rockchip.mediacodecnew:dimen/m3_navigation_rail_elevation = 0x7f0701c3
com.android.rockchip.mediacodecnew:styleable/BottomSheetBehavior_Layout = 0x7f130017
com.android.rockchip.mediacodecnew:attr/popupMenuBackground = 0x7f040384
com.android.rockchip.mediacodecnew:dimen/m3_ripple_pressed_alpha = 0x7f0701d0
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f1203f0
com.android.rockchip.mediacodecnew:integer/mtrl_chip_anim_duration = 0x7f0a0035
com.android.rockchip.mediacodecnew:attr/icon = 0x7f040228
com.android.rockchip.mediacodecnew:attr/AAEnabled = 0x7f040000
com.android.rockchip.mediacodecnew:attr/barrierDirection = 0x7f040068
com.android.rockchip.mediacodecnew:id/spread = 0x7f090218
com.android.rockchip.mediacodecnew:color/m3_fab_efab_background_color_selector = 0x7f060086
com.android.rockchip.mediacodecnew:color/material_personalized_color_error_container = 0x7f06025f
com.android.rockchip.mediacodecnew:attr/spinnerDropDownItemStyle = 0x7f0403de
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f120335
com.android.rockchip.mediacodecnew:attr/buttonIconDimen = 0x7f040095
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface_dim = 0x7f06027e
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.ElevatedButton = 0x7f12035a
com.android.rockchip.mediacodecnew:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1201d6
com.android.rockchip.mediacodecnew:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f12008c
com.android.rockchip.mediacodecnew:drawable/notification_bg = 0x7f0800db
com.android.rockchip.mediacodecnew:style/TextAppearance.Material3.DisplayLarge = 0x7f1201e9
com.android.rockchip.mediacodecnew:attr/actionModeCloseDrawable = 0x7f040016
com.android.rockchip.mediacodecnew:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f08001d
com.android.rockchip.mediacodecnew:attr/layout_constraintRight_toLeftOf = 0x7f040296
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0d0081
com.android.rockchip.mediacodecnew:attr/badgeWithTextRadius = 0x7f040062
com.android.rockchip.mediacodecnew:animator/tp_video_button_release = 0x7f020023
com.android.rockchip.mediacodecnew:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f070105
com.android.rockchip.mediacodecnew:color/abc_hint_foreground_material_dark = 0x7f060007
com.android.rockchip.mediacodecnew:color/abc_color_highlight_material = 0x7f060004
com.android.rockchip.mediacodecnew:attr/materialDividerHeavyStyle = 0x7f0402f5
com.android.rockchip.mediacodecnew:attr/badgeTextColor = 0x7f04005d
com.android.rockchip.mediacodecnew:drawable/design_fab_background = 0x7f080082
com.android.rockchip.mediacodecnew:raw/fxaa = 0x7f100012
com.android.rockchip.mediacodecnew:attr/backgroundTintMode = 0x7f040054
com.android.rockchip.mediacodecnew:attr/dividerVertical = 0x7f040185
com.android.rockchip.mediacodecnew:dimen/m3_comp_circular_progress_indicator_active_indicator_width = 0x7f070104
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f120420
com.android.rockchip.mediacodecnew:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0d0160
com.android.rockchip.mediacodecnew:attr/fontProviderCerts = 0x7f040201
com.android.rockchip.mediacodecnew:dimen/mtrl_navigation_rail_elevation = 0x7f0702c7
com.android.rockchip.mediacodecnew:attr/colorError = 0x7f0400fc
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_outline_variant = 0x7f06015c
com.android.rockchip.mediacodecnew:style/TextAppearance.Design.Tab = 0x7f1201d4
com.android.rockchip.mediacodecnew:attr/dialogCornerRadius = 0x7f04017a
com.android.rockchip.mediacodecnew:attr/colorOnSurface = 0x7f04010c
com.android.rockchip.mediacodecnew:interpolator/fast_out_slow_in = 0x7f0b0006
com.android.rockchip.mediacodecnew:attr/colorTertiary = 0x7f04012d
com.android.rockchip.mediacodecnew:id/switch_to_tv_button = 0x7f090231
com.android.rockchip.mediacodecnew:dimen/m3_comp_extended_fab_primary_container_height = 0x7f07010e
com.android.rockchip.mediacodecnew:attr/expanded = 0x7f0401bb
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f07027e
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_secondary50 = 0x7f0600d4
com.android.rockchip.mediacodecnew:drawable/ic_mtrl_chip_checked_black = 0x7f080094
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0800bd
com.android.rockchip.mediacodecnew:id/dragRight = 0x7f0900cd
com.android.rockchip.mediacodecnew:attr/checkMarkCompat = 0x7f0400b1
com.android.rockchip.mediacodecnew:color/material_personalized_color_primary_text_inverse = 0x7f060272
com.android.rockchip.mediacodecnew:style/Base.V26.Theme.AppCompat = 0x7f1200b6
com.android.rockchip.mediacodecnew:id/square = 0x7f09021b
com.android.rockchip.mediacodecnew:attr/motionEffect_move = 0x7f04033f
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0d0122
com.android.rockchip.mediacodecnew:macro/m3_comp_snackbar_container_shape = 0x7f0d0115
com.android.rockchip.mediacodecnew:attr/actionBarTabStyle = 0x7f04000a
com.android.rockchip.mediacodecnew:attr/labelBehavior = 0x7f040269
com.android.rockchip.mediacodecnew:attr/fontFamily = 0x7f0401ff
com.android.rockchip.mediacodecnew:attr/textAppearanceHeadline3 = 0x7f04043f
com.android.rockchip.mediacodecnew:attr/altSrc = 0x7f040031
com.android.rockchip.mediacodecnew:attr/customColorDrawableValue = 0x7f040165
com.android.rockchip.mediacodecnew:style/Widget.Material3.BottomSheet.Modal = 0x7f120358
com.android.rockchip.mediacodecnew:attr/autoShowKeyboard = 0x7f040042
com.android.rockchip.mediacodecnew:attr/contentDescription = 0x7f04013b
com.android.rockchip.mediacodecnew:styleable/NavigationBarView = 0x7f13006b
com.android.rockchip.mediacodecnew:id/progress_bar = 0x7f0901a7
com.android.rockchip.mediacodecnew:attr/autoCompleteMode = 0x7f04003f
com.android.rockchip.mediacodecnew:attr/layout_constraintEnd_toStartOf = 0x7f040286
com.android.rockchip.mediacodecnew:layout/material_textinput_timepicker = 0x7f0c0046
com.android.rockchip.mediacodecnew:attr/dragThreshold = 0x7f040189
com.android.rockchip.mediacodecnew:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.android.rockchip.mediacodecnew:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0d0144
com.android.rockchip.mediacodecnew:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401c7
com.android.rockchip.mediacodecnew:attr/behavior_hideable = 0x7f040070
com.android.rockchip.mediacodecnew:attr/colorOnTertiaryFixed = 0x7f040111
com.android.rockchip.mediacodecnew:dimen/design_fab_translation_z_pressed = 0x7f070074
com.android.rockchip.mediacodecnew:id/tv_mode_status_text = 0x7f09027d
com.android.rockchip.mediacodecnew:attr/hintEnabled = 0x7f040220
com.android.rockchip.mediacodecnew:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.android.rockchip.mediacodecnew:attr/animateRelativeTo = 0x7f040035
com.android.rockchip.mediacodecnew:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0d006f
com.android.rockchip.mediacodecnew:attr/backgroundSplit = 0x7f040051
com.android.rockchip.mediacodecnew:drawable/abc_text_select_handle_right_mtrl = 0x7f080071
com.android.rockchip.mediacodecnew:attr/dropDownListViewStyle = 0x7f040198
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.ProgressIndicator = 0x7f120444
com.android.rockchip.mediacodecnew:color/m3_ref_palette_error99 = 0x7f0600f4
com.android.rockchip.mediacodecnew:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0d00b4
com.android.rockchip.mediacodecnew:attr/verticalOffset = 0x7f0404c4
com.android.rockchip.mediacodecnew:color/design_dark_default_color_on_secondary = 0x7f060035
com.android.rockchip.mediacodecnew:attr/initialActivityCount = 0x7f04023f
com.android.rockchip.mediacodecnew:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0d0143
com.android.rockchip.mediacodecnew:attr/actionViewClass = 0x7f040026
com.android.rockchip.mediacodecnew:attr/titleTextAppearance = 0x7f040498
com.android.rockchip.mediacodecnew:color/m3_ref_palette_primary50 = 0x7f060120
com.android.rockchip.mediacodecnew:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f120151
com.android.rockchip.mediacodecnew:dimen/m3_divider_heavy_thickness = 0x7f0701a8
com.android.rockchip.mediacodecnew:macro/m3_comp_fab_primary_large_container_shape = 0x7f0d003a
com.android.rockchip.mediacodecnew:dimen/m3_comp_filled_button_container_elevation = 0x7f070123
com.android.rockchip.mediacodecnew:color/design_default_color_secondary_variant = 0x7f060048
com.android.rockchip.mediacodecnew:attr/borderWidth = 0x7f040079
com.android.rockchip.mediacodecnew:dimen/notification_big_circle_margin = 0x7f070306
com.android.rockchip.mediacodecnew:attr/largeFontVerticalOffsetAdjustment = 0x7f04026c
com.android.rockchip.mediacodecnew:dimen/m3_card_elevated_disabled_z = 0x7f0700e6
com.android.rockchip.mediacodecnew:attr/removeEmbeddedFabElevation = 0x7f0403a3
com.android.rockchip.mediacodecnew:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
com.android.rockchip.mediacodecnew:styleable/LinearProgressIndicator = 0x7f13004b
com.android.rockchip.mediacodecnew:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f120408
com.android.rockchip.mediacodecnew:attr/marginLeftSystemWindowInsets = 0x7f0402d3
com.android.rockchip.mediacodecnew:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f12006b
com.android.rockchip.mediacodecnew:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0d0022
com.android.rockchip.mediacodecnew:id/coordinator = 0x7f0900ab
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary20 = 0x7f0600c4
com.android.rockchip.mediacodecnew:id/right = 0x7f0901bd
com.android.rockchip.mediacodecnew:attr/layout_constraintLeft_creator = 0x7f040292
com.android.rockchip.mediacodecnew:dimen/m3_comp_fab_primary_large_container_height = 0x7f07011c
com.android.rockchip.mediacodecnew:attr/boxStrokeWidth = 0x7f04008a
com.android.rockchip.mediacodecnew:attr/animateMenuItems = 0x7f040033
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_primary10 = 0x7f0600c2
com.android.rockchip.mediacodecnew:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.android.rockchip.mediacodecnew:attr/layout_editor_absoluteX = 0x7f0402a7
com.android.rockchip.mediacodecnew:dimen/m3_bottomappbar_horizontal_padding = 0x7f0700ca
com.android.rockchip.mediacodecnew:anim/m3_bottom_sheet_slide_in = 0x7f010021
com.android.rockchip.mediacodecnew:attr/colorOnErrorContainer = 0x7f040102
com.android.rockchip.mediacodecnew:dimen/mtrl_btn_focused_z = 0x7f070257
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral40 = 0x7f0600ff
com.android.rockchip.mediacodecnew:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f120238
com.android.rockchip.mediacodecnew:color/material_dynamic_tertiary100 = 0x7f060238
com.android.rockchip.mediacodecnew:attr/menuAlignmentMode = 0x7f040312
com.android.rockchip.mediacodecnew:macro/m3_comp_extended_fab_surface_container_color = 0x7f0d0033
com.android.rockchip.mediacodecnew:attr/colorOnTertiaryContainer = 0x7f040110
com.android.rockchip.mediacodecnew:id/expand_activities_button = 0x7f0900e3
com.android.rockchip.mediacodecnew:attr/actionModeShareDrawable = 0x7f04001d
com.android.rockchip.mediacodecnew:string/mtrl_picker_save = 0x7f11007d
com.android.rockchip.mediacodecnew:dimen/m3_extended_fab_icon_padding = 0x7f0701ab
com.android.rockchip.mediacodecnew:attr/actionModePopupWindowStyle = 0x7f04001b
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_unselected_icon_color = 0x7f0d013b
com.android.rockchip.mediacodecnew:attr/listChoiceIndicatorSingleAnimated = 0x7f0402c1
com.android.rockchip.mediacodecnew:attr/actionModeFindDrawable = 0x7f040019
com.android.rockchip.mediacodecnew:attr/actionModeCopyDrawable = 0x7f040017
com.android.rockchip.mediacodecnew:attr/lastItemDecorated = 0x7f04026e
com.android.rockchip.mediacodecnew:dimen/notification_action_icon_size = 0x7f070304
com.android.rockchip.mediacodecnew:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0d0123
com.android.rockchip.mediacodecnew:drawable/mtrl_popupmenu_background = 0x7f0800ca
com.android.rockchip.mediacodecnew:color/foreground_material_dark = 0x7f06005a
com.android.rockchip.mediacodecnew:attr/contentPaddingLeft = 0x7f040145
com.android.rockchip.mediacodecnew:dimen/tooltip_vertical_padding = 0x7f070318
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1201c4
com.android.rockchip.mediacodecnew:string/mtrl_picker_range_header_selected = 0x7f11007a
com.android.rockchip.mediacodecnew:attr/actionBarTabBarStyle = 0x7f040009
com.android.rockchip.mediacodecnew:attr/tabInlineLabel = 0x7f04041d
com.android.rockchip.mediacodecnew:attr/cardElevation = 0x7f04009f
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_divider_thickness = 0x7f070279
com.android.rockchip.mediacodecnew:attr/materialCalendarMonth = 0x7f0402e9
com.android.rockchip.mediacodecnew:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f12011a
com.android.rockchip.mediacodecnew:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1200f4
com.android.rockchip.mediacodecnew:attr/floatingActionButtonSecondaryStyle = 0x7f0401e2
com.android.rockchip.mediacodecnew:attr/behavior_fitToContents = 0x7f04006e
com.android.rockchip.mediacodecnew:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0800b8
com.android.rockchip.mediacodecnew:dimen/material_time_picker_minimum_screen_height = 0x7f07023c
com.android.rockchip.mediacodecnew:attr/actionLayout = 0x7f040010
com.android.rockchip.mediacodecnew:id/tv_zoom_scale = 0x7f090286
com.android.rockchip.mediacodecnew:color/mtrl_switch_thumb_tint = 0x7f0602c0
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1202bb
com.android.rockchip.mediacodecnew:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0d00f1
com.android.rockchip.mediacodecnew:dimen/design_fab_elevation = 0x7f07006f
com.android.rockchip.mediacodecnew:attr/fontStyle = 0x7f040207
com.android.rockchip.mediacodecnew:attr/behavior_saveFlags = 0x7f040073
com.android.rockchip.mediacodecnew:drawable/ic_mtrl_checked_circle = 0x7f080093
com.android.rockchip.mediacodecnew:color/error_color_material_light = 0x7f060059
com.android.rockchip.mediacodecnew:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0702d7
com.android.rockchip.mediacodecnew:attr/aspectRatioMode = 0x7f04003c
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0702ae
com.android.rockchip.mediacodecnew:attr/layout_constraintStart_toEndOf = 0x7f040298
com.android.rockchip.mediacodecnew:animator/design_appbar_state_list_animator = 0x7f020000
com.android.rockchip.mediacodecnew:dimen/m3_comp_linear_progress_indicator_active_indicator_height = 0x7f070136
com.android.rockchip.mediacodecnew:attr/telltales_tailScale = 0x7f040430
com.android.rockchip.mediacodecnew:attr/ratingBarStyleIndicator = 0x7f040398
com.android.rockchip.mediacodecnew:id/showTitle = 0x7f090202
com.android.rockchip.mediacodecnew:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f070212
com.android.rockchip.mediacodecnew:color/material_dynamic_secondary99 = 0x7f060235
com.android.rockchip.mediacodecnew:attr/closeItemLayout = 0x7f0400e8
com.android.rockchip.mediacodecnew:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1201a4
com.android.rockchip.mediacodecnew:attr/panelBackground = 0x7f04036f
com.android.rockchip.mediacodecnew:attr/endIconDrawable = 0x7f0401a7
com.android.rockchip.mediacodecnew:attr/checkedIconSize = 0x7f0400bb
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1202a1
com.android.rockchip.mediacodecnew:attr/badgeWidth = 0x7f040060
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0600e6
com.android.rockchip.mediacodecnew:attr/deltaPolarAngle = 0x7f040177
com.android.rockchip.mediacodecnew:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f120389
com.android.rockchip.mediacodecnew:dimen/mtrl_slider_widget_height = 0x7f0702e7
com.android.rockchip.mediacodecnew:styleable/ConstraintOverride = 0x7f13002a
com.android.rockchip.mediacodecnew:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.android.rockchip.mediacodecnew:attr/prefixText = 0x7f040388
com.android.rockchip.mediacodecnew:style/Base.V24.Theme.Material3.Light = 0x7f1200b4
com.android.rockchip.mediacodecnew:attr/expandedTitleGravity = 0x7f0401bd
com.android.rockchip.mediacodecnew:color/material_on_primary_emphasis_medium = 0x7f060253
com.android.rockchip.mediacodecnew:style/Widget.Material3.SearchBar.Outlined = 0x7f1203cb
com.android.rockchip.mediacodecnew:attr/colorPrimarySurface = 0x7f04011b
com.android.rockchip.mediacodecnew:attr/chipMinHeight = 0x7f0400c9
com.android.rockchip.mediacodecnew:attr/layout_goneMarginEnd = 0x7f0402ab
com.android.rockchip.mediacodecnew:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f120368
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_neutral22 = 0x7f0600a2
com.android.rockchip.mediacodecnew:layout/material_clock_period_toggle = 0x7f0c0041
com.android.rockchip.mediacodecnew:dimen/compat_notification_large_icon_max_height = 0x7f07005b
com.android.rockchip.mediacodecnew:attr/fabAnchorMode = 0x7f0401cf
com.android.rockchip.mediacodecnew:color/m3_sys_color_dark_error = 0x7f06014b
com.android.rockchip.mediacodecnew:id/filled = 0x7f0900e9
com.android.rockchip.mediacodecnew:attr/buttonBarStyle = 0x7f040091
com.android.rockchip.mediacodecnew:style/Base.Theme.AppCompat.Dialog = 0x7f12004d
com.android.rockchip.mediacodecnew:color/abc_search_url_text = 0x7f06000d
com.android.rockchip.mediacodecnew:drawable/mtrl_ic_error = 0x7f0800c7
com.android.rockchip.mediacodecnew:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
com.android.rockchip.mediacodecnew:attr/layoutDescription = 0x7f040270
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.ActionBar = 0x7f12027f
com.android.rockchip.mediacodecnew:drawable/abc_switch_track_mtrl_alpha = 0x7f08006b
com.android.rockchip.mediacodecnew:color/m3_appbar_overlay_color = 0x7f06005e
com.android.rockchip.mediacodecnew:dimen/m3_chip_elevated_elevation = 0x7f0700f7
com.android.rockchip.mediacodecnew:attr/headerLayout = 0x7f040214
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral70 = 0x7f060103
com.android.rockchip.mediacodecnew:drawable/ic_play_arrow_white_24 = 0x7f080098
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f1202e4
com.android.rockchip.mediacodecnew:drawable/btn_checkbox_unchecked_mtrl = 0x7f08007c
com.android.rockchip.mediacodecnew:dimen/abc_search_view_preferred_width = 0x7f070037
com.android.rockchip.mediacodecnew:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f12034d
com.android.rockchip.mediacodecnew:string/path_password_strike_through = 0x7f110098
com.android.rockchip.mediacodecnew:attr/layout_constraintVertical_chainStyle = 0x7f04029f
com.android.rockchip.mediacodecnew:style/Widget.AppCompat.Spinner.DropDown = 0x7f120334
com.android.rockchip.mediacodecnew:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1201b2
com.android.rockchip.mediacodecnew:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.android.rockchip.mediacodecnew:color/mtrl_btn_text_btn_ripple_color = 0x7f06029b
com.android.rockchip.mediacodecnew:attr/layout_collapseParallaxMultiplier = 0x7f040277
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_light_outline_variant = 0x7f060198
com.android.rockchip.mediacodecnew:attr/showText = 0x7f0403cb
com.android.rockchip.mediacodecnew:color/m3_sys_color_light_on_primary_container = 0x7f0601be
com.android.rockchip.mediacodecnew:color/material_personalized_color_surface = 0x7f060277
com.android.rockchip.mediacodecnew:bool/abc_action_bar_embed_tabs = 0x7f050000
com.android.rockchip.mediacodecnew:attr/searchIcon = 0x7f0403af
com.android.rockchip.mediacodecnew:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0800b0
com.android.rockchip.mediacodecnew:attr/curveFit = 0x7f040163
com.android.rockchip.mediacodecnew:dimen/mtrl_textinput_counter_margin_start = 0x7f0702f9
com.android.rockchip.mediacodecnew:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0702a0
com.android.rockchip.mediacodecnew:attr/textAppearanceLabelMedium = 0x7f040447
com.android.rockchip.mediacodecnew:attr/behavior_autoHide = 0x7f04006a
com.android.rockchip.mediacodecnew:id/textStart = 0x7f090247
com.android.rockchip.mediacodecnew:attr/backgroundStacked = 0x7f040052
com.android.rockchip.mediacodecnew:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f070173
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f12028a
com.android.rockchip.mediacodecnew:attr/boxBackgroundColor = 0x7f040081
com.android.rockchip.mediacodecnew:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f12034c
com.android.rockchip.mediacodecnew:anim/design_snackbar_out = 0x7f01001b
com.android.rockchip.mediacodecnew:string/abc_searchview_description_voice = 0x7f110017
com.android.rockchip.mediacodecnew:anim/abc_slide_in_top = 0x7f010007
com.android.rockchip.mediacodecnew:attr/flow_wrapMode = 0x7f0401fd
com.android.rockchip.mediacodecnew:color/dim_foreground_disabled_material_light = 0x7f060055
com.android.rockchip.mediacodecnew:string/status_bar_notification_info_overflow = 0x7f11009f
com.android.rockchip.mediacodecnew:dimen/mtrl_calendar_header_height = 0x7f07027a
com.android.rockchip.mediacodecnew:attr/chipStyle = 0x7f0400d2
com.android.rockchip.mediacodecnew:id/m3_side_sheet = 0x7f090124
com.android.rockchip.mediacodecnew:attr/actionBarWidgetTheme = 0x7f04000d
com.android.rockchip.mediacodecnew:styleable/TabLayout = 0x7f13008a
com.android.rockchip.mediacodecnew:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f06016d
com.android.rockchip.mediacodecnew:attr/placeholderText = 0x7f04037f
com.android.rockchip.mediacodecnew:attr/endIconTintMode = 0x7f0401ac
com.android.rockchip.mediacodecnew:attr/trackTint = 0x7f0404b3
com.android.rockchip.mediacodecnew:anim/tp_speed_dropdown_enter = 0x7f01002e
com.android.rockchip.mediacodecnew:style/ThemeOverlay.Material3.Snackbar = 0x7f1202b5
com.android.rockchip.mediacodecnew:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0d00ff
com.android.rockchip.mediacodecnew:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080053
com.android.rockchip.mediacodecnew:color/mtrl_fab_icon_text_color_selector = 0x7f0602ac
com.android.rockchip.mediacodecnew:attr/layout_constraintGuide_end = 0x7f040288
com.android.rockchip.mediacodecnew:attr/iconPadding = 0x7f04022b
com.android.rockchip.mediacodecnew:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f1202da
com.android.rockchip.mediacodecnew:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0d0103
com.android.rockchip.mediacodecnew:attr/horizontalOffsetWithText = 0x7f040226
com.android.rockchip.mediacodecnew:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0600de
com.android.rockchip.mediacodecnew:styleable/MotionEffect = 0x7f130064
com.android.rockchip.mediacodecnew:attr/materialTimePickerStyle = 0x7f040302
com.android.rockchip.mediacodecnew:color/m3_ref_palette_neutral95 = 0x7f060109
com.android.rockchip.mediacodecnew:attr/viewInflaterClass = 0x7f0404c6
